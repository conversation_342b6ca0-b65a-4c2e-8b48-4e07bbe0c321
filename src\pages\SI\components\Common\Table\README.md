# DynamicDataTable 通用表格组件

一个高度可配置的表格组件，完全兼容 Antd Table 的所有属性，并提供额外的工具栏和选择功能。

## ✨ 特性

- ✅ **完全兼容 Antd Table** 所有原生属性
- ✅ **默认斑马线条纹** 自动实现，与 Antd 样式保持一致
- ✅ **无边框设计** 默认无边框，可配置
- ✅ **表头无分割线** 与 Antd 默认样式一致
- ✅ **可配置工具栏** 支持自定义按钮和布局
- ✅ **行选择功能** 支持单选、多选、批量操作
- ✅ **响应式设计** 自适应各种屏幕尺寸
- ✅ **自定义渲染** 支持列级别的自定义渲染

## 🎨 样式特点

### 深色主题设计
- **斑马线条纹**: 奇数行背景 `#0B2222`，偶数行背景 `#081618`
- **深色背景**: 表格采用深色主题，白色文字确保可读性
- **青绿色表头**: 背景 `#08E7CB`（10%透明度），文字 `#08E7CB`（60%透明度）
- **悬停效果**: 鼠标悬停显示深色高亮背景，表头悬停更亮的青绿色
- **选中效果**: 选中行显示深绿色背景
- **边框控制**: 可配置显示/隐藏边框，默认无边框

## 🚀 基础用法

```jsx
import DynamicDataTable from './components/Common/Table';

// 最简单的用法 - 默认样式
<DynamicDataTable
  dataSource={data}
  columns={columns}
/>
```

## ⚙️ 样式配置

### 表格外观控制

```jsx
// 默认样式 - 斑马线条纹，无边框
<DynamicDataTable
  dataSource={data}
  columns={columns}
/>

// 显示边框
<DynamicDataTable
  dataSource={data}
  columns={columns}
  bordered={true}
/>

// 紧凑型表格
<DynamicDataTable
  dataSource={data}
  columns={columns}
  size="small"
/>

// 中等尺寸表格
<DynamicDataTable
  dataSource={data}
  columns={columns}
  size="middle"
/>
```

### 自定义行样式

```jsx
// 自定义行类名
<DynamicDataTable
  dataSource={data}
  columns={columns}
  rowClassName={(record, index) => {
    if (record.status === 'error') return 'error-row';
    return ''; // 空字符串会保持默认斑马线条纹
  }}
/>

// 行事件处理
<DynamicDataTable
  dataSource={data}
  columns={columns}
  onRow={(record) => ({
    onClick: () => console.log('点击行:', record),
    onMouseEnter: () => console.log('鼠标进入'),
    style: { cursor: 'pointer' }
  })}
/>
```

## 🛠️ 工具栏配置

```jsx
const toolbarButtons = [
  {
    key: 'add',
    text: '新增',
    type: 'primary',
    icon: <PlusOutlined />,
    onClick: () => message.success('新增操作'),
  },
  {
    key: 'delete',
    text: '批量删除',
    icon: <DeleteOutlined />,
    needsSelection: true, // 需要选择数据才能点击
    selectionMessage: '请选择要删除的数据',
    onClick: (selectedKeys) => console.log('删除:', selectedKeys),
  },
];

<DynamicDataTable
  dataSource={data}
  columns={columns}
  showToolbar={true}
  toolbarButtons={toolbarButtons}
  showSelectionInfo={true} // 显示选择数量信息
  rowSelection={{}} // 启用行选择
/>
```

## 📋 完整配置示例

```jsx
<DynamicDataTable
  // 数据相关
  dataSource={dataSource}
  columns={columns}
  loading={false}
  
  // 样式相关 - 完全兼容 Antd Table
  size="default" // 'default' | 'middle' | 'small'
  bordered={false} // 是否显示边框
  showHeader={true} // 是否显示表头
  scroll={{ x: 800, y: 400 }} // 滚动配置
  
  // 工具栏相关
  showToolbar={true}
  toolbarButtons={toolbarButtons}
  toolbarAlign="left" // 'left' | 'center' | 'right' | 'space-between'
  showSelectionInfo={true}
  
  // 选择相关
  rowSelection={{
    type: 'checkbox', // 'checkbox' | 'radio'
    selectedRowKeys: selectedKeys,
    onChange: (keys) => setSelectedKeys(keys),
  }}
  
  // 分页相关
  pagination={{
    current: 1,
    pageSize: 10,
    total: 100,
    showSizeChanger: true,
    showQuickJumper: true,
  }}
  
  // 其他 Antd Table 属性
  expandable={{
    expandedRowRender: (record) => <div>{record.detail}</div>,
  }}
  sortDirections={['ascend', 'descend']}
  onChange={(pagination, filters, sorter) => {
    console.log('表格变化:', { pagination, filters, sorter });
  }}
/>
```

## 📝 Props 说明

### 数据相关
| 参数 | 说明 | 类型 | 默认值 |
|---|---|---|---|
| dataSource | 数据数组 | array | [] |
| columns | 表格列配置 | array | [] |
| loading | 加载状态 | boolean | false |

### 样式相关（完全兼容 Antd Table）
| 参数 | 说明 | 类型 | 默认值 |
|---|---|---|---|
| size | 表格大小 | 'default' \| 'middle' \| 'small' | 'default' |
| bordered | 是否显示边框 | boolean | false |
| showHeader | 是否显示表头 | boolean | true |
| scroll | 表格滚动配置 | object | - |
| tableLayout | 表格布局 | 'auto' \| 'fixed' | - |
| rowClassName | 行样式类名 | string \| function | - |

### 工具栏相关
| 参数 | 说明 | 类型 | 默认值 |
|---|---|---|---|
| showToolbar | 是否显示工具栏 | boolean | false |
| toolbarButtons | 工具栏按钮配置 | array | [] |
| toolbarAlign | 工具栏对齐方式 | 'left' \| 'center' \| 'right' \| 'space-between' | 'left' |
| showSelectionInfo | 是否显示选择信息 | boolean | false |

### 其他所有 Antd Table 原生属性
组件通过 `...restTableProps` 完全支持 Antd Table 的所有原生属性。

## 🎯 样式覆盖

如果需要自定义深色主题的颜色，可以通过以下方式（使用LESS语法）：

```less
/* 自定义斑马线颜色 */
.dynamic-table {
  .dynamic-table-row-even td {
    background-color: #1a1a2e !important; /* 自定义偶数行颜色 */
  }

  .ant-table-tbody > tr > td {
    background-color: #16213e !important; /* 自定义奇数行颜色 */
  }

  /* 自定义悬停效果 */
  .ant-table-tbody > tr:hover > td {
    background-color: #0f3460 !important; /* 自定义悬停颜色 */
  }

  /* 自定义选中效果 */
  .ant-table-tbody > tr.ant-table-row-selected > td {
    background-color: #0e6b0e !important; /* 自定义选中颜色 */
  }

  /* 自定义表头颜色 */
  .ant-table-thead > tr > th {
    background-color: rgba(255, 165, 0, 0.1) !important; /* 自定义表头背景 - 橙色10%透明度 */
    color: rgba(255, 165, 0, 0.8) !important; /* 自定义表头文字颜色 - 橙色80%透明度 */

    /* 自定义表头悬停效果 */
    &:hover {
      background-color: rgba(255, 165, 0, 0.2) !important; /* 悬停时更亮的背景 */
    }
  }
}
```

**注意**: 本组件使用 LESS 预处理器，请确保项目支持 LESS 文件编译。

## 📄 更新日志

### v1.5.0 (最新)
- ✅ 改用 LESS 预处理器，符合项目规范
- ✅ 移除对分页等其他 Antd 组件样式的修改
- ✅ 仅保留表格本身的深色主题和青绿色表头样式
- ✅ 优化 LESS 语法结构，提高可维护性

### v1.4.0
- ✅ 实现青绿色表头主题 (#08E7CB)
- ✅ 表头背景透明度10%，文字透明度60%
- ✅ 优化表头悬停效果和图标颜色适配
- ✅ 排序和筛选图标使用青绿色主题
- ✅ 更新文档和示例以反映新的表头设计

### v1.3.0
- ✅ 实现深色主题斑马线条纹 (#0B2222 和 #081618)
- ✅ 优化深色主题下的文字颜色和可读性
- ✅ 调整悬停和选中效果的颜色适配深色主题
- ✅ 完善选择框、图标、分页等组件的深色主题适配
- ✅ 更新文档和示例以反映深色主题设计

### v1.2.0
- ✅ 修正斑马线条纹实现
- ✅ 移除表头分割线，与 Antd 默认保持一致
- ✅ 移除行边框，仅保留底部分割线
- ✅ 优化 CSS 样式实现
- ✅ 完善样式配置文档

### v1.1.0
- 完全兼容 Antd Table 所有属性
- 添加工具栏和选择功能
- 支持自定义渲染

### v1.0.0
- 初始版本发布 