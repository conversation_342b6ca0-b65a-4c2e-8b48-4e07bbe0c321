import { queryPage } from '@/utils/MyRoute';
import { useModel } from "umi";
import { useState, useEffect } from 'react';
import { Get2, Post2 } from '@/services/general';
import { Button, Tabs, Card, Steps, Upload, List, Checkbox, Table, Form, Input, Select, Modal, DatePicker, message, Row, Col, Descriptions, Slider, InputNumber, Image, Spin } from 'antd';


const ChangeDetection = () => {
    const { setPage } = useModel("pageModel");

    const [modalVisible, setModalVisible] = useState(false);
    const [modalVisible2, setModalVisible2] = useState(false);
    const [currentStep, setCurrentStep] = useState(0); // 当前步骤
    const [beforeImgsLength, setBeforeImgsLength] = useState(0); // 前期影像数量
    const [afterImgsLength, setAfterImgsLength] = useState(0); // 后期影像数量
    const [modelList, setModelList] = useState([]); // 模型列表
    const [selectedModels, setSelectedModels] = useState([]); // 选择的模型
    const [cls, setCls] = useState({}) // 识别内容 (数字)
    const [user, setUser] = useState(); // 用户信息
    const [currentEditModel, setCurrentEditModel] = useState(null); // 当前选择的模型
    const [tempSelectedModels, setTempSelectedModels] = useState([]);
    const [tempCls, setTempCls] = useState({});

    const [form] = Form.useForm();


    // 打开新增窗口
    const openModal = () => {
        // 重置表单
        form.resetFields();

        setModalVisible(true);
        reset();
    };

    const cloceCreatTask = () => {
        // 重置表单
        form.resetFields();

        setModalVisible(false);
        reset();
    };

    // 除了表单的其他状态重置
    const reset = () => {
        setCurrentStep(0);
        setBeforeImgsLength(0);
        setAfterImgsLength(0);
    };

    // 获取AI模型列表
    const getModelData = async () => {
        const pst = await Get2('/api/v1/AIModel/GetList', {});
        setModelList(pst);
    };

    // 生成模型选择选项
    const getModelSelect = (modelList) => {
        const list = []
        modelList.forEach(e => {
            list.push(<Select.Option key={e.Guid} data={e} value={e.Guid} label={e.AName}>{e.AName}</Select.Option>)
        });
        return list;
    }
    // 选择模型弹框 确认后的处理
    const handleModalConfirm = () => {
        console.log('Selected Models:', tempSelectedModels);
        setSelectedModels(tempSelectedModels);
        setModalVisible2(false);
    };

    // 打开分类选择弹窗
    const openClsModal = (model) => {
        setCurrentEditModel(model);
        setTempCls({
            ...tempCls,
            [model.Guid]: cls[model.Guid] || []
        });
    };

    // 分类弹窗确认
    const handleClsConfirm = () => {
        setCls(prev => ({
            ...prev,
            [currentEditModel.Guid]: tempCls[currentEditModel.Guid] || []
        }));
        setTempSelectedModels(prev => {
            if (!prev.some(m => m.Guid === currentEditModel.Guid)) {
                return [...prev, currentEditModel];
            }
            return prev;
        });

        setCurrentEditModel(null);
    };

    // 生成识别内容选项
    const getAList = (selectedModel) => {
        if (!selectedModel) return <div></div>
        const arr = selectedModel.AList.split(",");

        const list = arr.map((e, index) => ({
            label: e,
            value: index,
            disabled: false,
        }));

        return (
            <Checkbox.Group
                value={cls[selectedModel.Guid] || []}
                onChange={(values) => {

                    setCls(prev => ({
                        ...prev,
                        [selectedModel.Guid]: values
                    }));
                }}
                options={list}
            />
        );
    }

    // 处理提交
    const handleSubmit = async (values) => {
        console.log('@@@form', form.getFieldsValue());
        console.log('@@@selectedModels', selectedModels);
        console.log('@@@cls', cls);
    };


    const steps = [
        {
            title: '选择前期影像',
            content: (
                <div style={{ marginTop: 16 }}>
                    <Form.Item
                        name="beforeImgs"
                        valuePropName="fileList"
                        getValueFromEvent={(e) => e?.fileList}
                    >
                        <Upload.Dragger
                            name="file"
                            multiple={true}
                            beforeUpload={(file) => {
                                // const isValidType = ['image/jpeg', 'image/png'].includes(file.type);
                                // if (!isValidType) {
                                //     message.error('仅支持jpg/png格式文件！');
                                //     return Upload.LIST_IGNORE; // 阻止上传
                                // }
                                return false;
                            }}
                            onChange={({ file, fileList }) => {
                                form.setFieldsValue({ beforeImgs: fileList });

                                setBeforeImgsLength(fileList.length);
                            }}
                            onRemove={(file) => {
                                const files = form.getFieldValue('beforeImgs') || [];
                                const newFiles = files.filter(f => f.uid !== file.uid);
                                form.setFieldsValue({ beforeImgs: newFiles });

                                setBeforeImgsLength(newFiles.length);
                                return true;
                            }}
                        >
                            <p className="ant-upload-text">点击或拖拽上传图片</p>
                            <p className="ant-upload-hint">支持jpg/png格式</p>
                        </Upload.Dragger>
                    </Form.Item>

                    <div style={{
                        display: 'flex',
                        position: 'absolute',
                        bottom: 20,
                        right: 20,
                        justifyContent: 'flex-end',
                        gap: 8,
                    }}>

                        <Button
                            type="primary"
                            disabled={!beforeImgsLength}
                            onClick={() => {
                                setCurrentStep(1);
                            }}
                        >
                            下一步
                        </Button>
                    </div>
                </div>
            ),
        },
        {
            title: '选择后期影像',
            content: (
                <div style={{ marginTop: 16 }}>
                    <Form.Item
                        name="afterImgs"
                        valuePropName="fileList"
                        getValueFromEvent={(e) => e?.fileList}
                    >
                        <Upload.Dragger
                            name="file"
                            multiple={true}
                            beforeUpload={(file) => {
                                // const isValidType = ['image/jpeg', 'image/png'].includes(file.type);
                                // if (!isValidType) {
                                //     message.error('仅支持jpg/png格式文件！');
                                //     return Upload.LIST_IGNORE; // 阻止上传
                                // }
                                return false;
                            }}
                            onChange={({ file, fileList }) => {
                                form.setFieldsValue({ afterImgs: fileList });

                                setAfterImgsLength(fileList.length);
                            }}
                            onRemove={(file) => {
                                const files = form.getFieldValue('afterImgs') || [];
                                const newFiles = files.filter(f => f.uid !== file.uid);
                                form.setFieldsValue({ afterImgs: newFiles });

                                setAfterImgsLength(newFiles.length);
                                return true;
                            }}
                        >
                            <p className="ant-upload-text">点击或拖拽上传图片</p>
                            <p className="ant-upload-hint">支持jpg/png格式</p>
                        </Upload.Dragger>
                    </Form.Item>
                    <div style={{
                        display: 'flex',
                        position: 'absolute',
                        bottom: 20,
                        right: 20,
                        justifyContent: 'flex-end',
                        gap: 8,
                    }}>
                        <Button
                            type="primary"
                            onClick={() => {
                                setCurrentStep(0);
                            }}
                        >
                            上一步
                        </Button>
                        <Button
                            type="primary"
                            disabled={!afterImgsLength}
                            onClick={() => {
                                setCurrentStep(2);
                            }}
                        >
                            下一步
                        </Button>
                    </div>

                </div>
            ),
        },
        {
            title: '配置模型参数',
            content: (
                <div style={{ marginTop: 16 }}>
                    <Form.Item name="TaskName" label="任务名称" rules={[{ required: true, message: '请输入任务名称' }]}>
                        <Input placeholder='请输入任务名称' />
                    </Form.Item>
                    <Form.Item label="AI模型" required>
                        <Select
                            mode="multiple"
                            placeholder="请选择模型"
                            style={{ width: '100%', minWidth: 100 }}
                            value={selectedModels.map(model => model.Guid)}
                            open={false}
                            onClick={() => setModalVisible2(true)}
                            onDeselect={(value) => {
                                // 移除对应的模型
                                const newSelected = selectedModels.filter(model => model.Guid !== value);
                                setSelectedModels(newSelected);

                                // 同时更新临时选中列表
                                setTempSelectedModels(newSelected);

                                // 清理cls中该模型的分类数据
                                const { [value]: removedCls, ...newCls } = cls;
                                setCls(newCls);

                                // 清理tempCls中该模型的分类数据
                                const { [value]: removedTempCls, ...newTempCls } = tempCls;
                                setTempCls(newTempCls);
                            }}

                        >
                            {getModelSelect(modelList)}
                        </Select>
                    </Form.Item>
                    <Modal
                        title="选择AI模型"
                        open={modalVisible2}
                        width="40vw"
                        onCancel={() => {
                            setModalVisible2(false);
                            setTempSelectedModels(selectedModels);
                        }}
                        onOk={handleModalConfirm}
                    >
                        <div style={{
                            maxHeight: '50vh',
                            overflowY: 'auto',
                            border: '1px solid #d9d9d9',
                            borderRadius: 4,
                            padding: 8
                        }}>
                            {/* {console.log('@@@modelList',modelList)} */}
                            <List
                                dataSource={modelList}
                                renderItem={model => (
                                    <List.Item
                                        key={model.Guid}
                                        style={{
                                            display: 'block',
                                            padding: '8px 12px',
                                            cursor: 'pointer',
                                            backgroundColor: tempSelectedModels.some(m => m.Guid === model.Guid)
                                                ? '#e6f7ff'
                                                : 'inherit'
                                        }}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            openClsModal(model);
                                        }}
                                    >
                                        <Checkbox
                                            checked={tempSelectedModels.some(m => m.Guid === model.Guid)}
                                            onChange={(e) => {
                                                e.stopPropagation();
                                                const exists = tempSelectedModels.some(m => m.Guid === model.Guid);
                                                setTempSelectedModels(prev =>
                                                    exists
                                                        ? prev.filter(m => m.Guid !== model.Guid)
                                                        : [...prev, model]
                                                );
                                            }}
                                            style={{ marginRight: 8 }}
                                        />
                                        <span>{model.AName}</span>
                                    </List.Item>
                                )}
                            />
                        </div>
                    </Modal>
                    {/* 分类选择弹窗 */}
                    <Modal
                        title="选择识别内容"
                        open={currentEditModel != null}
                        width="30vw"
                        onCancel={() => setCurrentEditModel(null)}
                        onOk={handleClsConfirm}
                    >
                        {currentEditModel && (
                            <div style={{ padding: 16 }}>
                                <div style={{ marginBottom: 16, fontSize: 16 }}>
                                    当前模型：{currentEditModel.AName}
                                </div>
                                <Checkbox.Group
                                    value={tempCls[currentEditModel.Guid] || []}
                                    onChange={(values) => {
                                        setTempCls(prev => ({
                                            ...prev,
                                            [currentEditModel.Guid]: values
                                        }));
                                    }}
                                    options={currentEditModel.AList.split(',').map((item, index) => ({
                                        label: item,
                                        value: index
                                    }))}
                                />
                            </div>
                        )}
                    </Modal>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 16, marginTop: 16, height: '18vh', overflowY: 'auto' }}>
                        {selectedModels.map(model => (
                            <div key={model.Guid}>
                                <div style={{
                                    marginBottom: 8,
                                    fontWeight: 500,
                                    borderBottom: '1px solid #f0f0f0',
                                    paddingBottom: 4
                                }}>
                                    {model.AName}
                                </div>
                                {getAList(model)}
                            </div>
                        ))}
                    </div>


                    <div style={{
                        display: 'flex',
                        position: 'absolute',
                        bottom: 20,
                        right: 20,
                        justifyContent: 'flex-end',
                        gap: 8,
                    }}>
                        <Button
                            type="primary"
                            onClick={() => {
                                setCurrentStep(1);
                            }}
                        >
                            上一步
                        </Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                handleSubmit();
                            }}
                        >
                            提交
                        </Button>
                    </div>

                </div>
            ),
        }
    ];



    const exr =
        <div>
            <Button
                type="primary"
                onClick={() => {
                    console.log('新增');
                    openModal();
                }}>
                新增
            </Button>
        </div>

    const items = steps.map((item) => ({ key: item.title, title: item.title }));

    useEffect(() => {
        getModelData();
    }, []);

    return (
        <div>
            <Card
                title={"变化检测"}
                styles={{
                    body: {
                        flex: 1,
                        display: "flex",
                        flexDirection: "column",
                        padding: 0,
                        overflow: "hidden",
                    },
                }}
                extra={exr}
            >

                <Modal
                    title="新增"
                    open={modalVisible}
                    onCancel={() => cloceCreatTask()}
                    footer={null}
                    width='40vw'
                    styles={{
                        body: {
                            height: '50vh',
                            display: 'flex',
                            flexDirection: 'column',
                            padding: 24
                        }
                    }}
                >
                    <Form layout="vertical" form={form}>
                        <Steps
                            current={currentStep}
                            items={items}
                        // onChange={changeStep} // 允许点击步骤条切换步骤
                        />
                        {steps[currentStep].content}
                    </Form>
                </Modal>
            </Card>
        </div>
    )
};

export default ChangeDetection;