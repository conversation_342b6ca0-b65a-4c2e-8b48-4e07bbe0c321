import BlockTitle from '@/components/BlockPanel/BlockTitle';
import './DataPanel.less';

import icon4 from '@/assets/icons/device.png';

import { BorderBox7 } from '@jiaminghi/data-view-react';
import {Badge ,Progress,Row,Col} from 'antd';
import { getGuid } from '@/utils/helper';

const getColor=(zt)=>{
  if(zt=="在线") return 'green';
  if(zt=="离线") return 'red';
  if(zt=="作业") return 'orange';

}

const DockStatePanel = () => {
  const data=[
    ['1#机场', '在线', 98],
    ['2#机场', '离线', 0],
    ['3#机场', '作业', 56],
    ['4#机场', '在线', 98],
    ['5#机场', '离线', 0],
    ['6#机场', '作业', 56],
  ]
  const getDCColor=(dc)=>{
      if(dc<30) return 'red';
      if(dc<60) return 'orange';
       return 'green';

  }
  const getItem = (title, zt,dc, bCol) => {
    return <Col  key={getGuid()} style={{width:120}}> <div  style={{ background: '', marginTop: 8.0, height: 72.0, width:120,padding:8.0}}>
    <div style={{ display: 'flex', height: 36.0, width:'100%', fontSize: 14.0 }}>
      <div style={{ width: 50, marginLeft: 8.0 }}><img src={icon4} height={36.0} width={36.0} /></div>
      <div style={{ flex: 'auto' }}><p style={{ lineHeight: '36px', color: 'white' }}>{title}</p></div>
    </div>

      <div style={{ height: 36.0, width: '100%', marginLeft: 8.0, fontSize: 11.0, color: 'white' }}>  
        <span style={{ width: 20.0 }}><Badge color={getColor(zt)} status={'success'} text={''} /></span>   
        <span style={{ width: 30.0,marginLeft:4.0 }}>{zt} </span>
        <span style={{ marginLeft: 8.0, }}><Progress strokeColor={getDCColor(dc)} steps={6} percent={dc} format={(percent) => <span style={{ color: 'white', fontSize: 12.0, }}>{percent + '%'}</span>} size="small" /></span> 
      </div>

    </div></Col> 
  }

  const getL = () => {
    const list = []
    let i = 0;
    data.forEach(e => {
      i++;

      console.log(e);
      list.push(getItem(e[0], e[1], e[2], `rgba(0,45,139,0.3)`))

    })
    return list;
  }


  return <div style={{ height:280.0, width: '100%', marginTop: 16.0 }}>
    <BorderBox7 style={{ background: `rgba(0,45,139,0.3)` }}>
      <BlockTitle style={{ margin: 8.0 }} title="机场状态监控" />
       <Row justify="space-around" style={{marginTop:12.0}} >
        {getL()}
       </Row>
    </BorderBox7>
  </div>
}

export default DockStatePanel;
