import { Space, Tag, message, Modal, Switch, Badge, Image, Alert } from "antd";
import { downloadFile, getImgUrl, isEmpty } from "@/utils/utils";
import { timeFormat } from "@/utils/helper";
import { axiosApi } from "@/services/general";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import { useModel } from "umi";
const getTableTitle = (title) => {
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
  );
};
const TableCols = (handlePageChange, isDJPage, handlePageChange2) => {

  return [
    {
      title: getTableTitle("任务名称"),
      dataIndex: "TaskName",
      key: "TaskName",
      align: "center",
    },
    {
      title: getTableTitle("模型名称"),
      dataIndex: "ModelName",
      key: "ModelName",
      align: "center",
    },
    {
      title: getTableTitle("识别项目"),
      dataIndex: "Aclsname",
      key: "Aclsname",
      align: "center",
    },
    {
      title: getTableTitle("任务状态"),
      //dataIndex: 'TaskState',
      // key: 'TaskState',
      align: "center",
      render: (record) => (
        <span style={{ width: "100%" }}>
          <span>
            {record.State == 1
              ? <span style={{ color: "green" }}>已完成</span>
              : <span style={{ color: "orange" }}>进行中</span>}
          </span>
        </span>
      ),
    },
    {
      title: getTableTitle("创建时间"),
      // dataIndex: "CreateTM",
      key: "CreateTM",
      align: "center",
      render: (_, record) => (
        <span>
          {isEmpty(record.CreateTime)
            ? "-"
            : timeFormat(record.CreateTime)
          }
        </span>
      )
    },
    {
      title: getTableTitle("结束时间"),
      // dataIndex: "CreateTM",
      key: "CreateTM",
      align: "center",
      render: (_, record) => (
        <span>
          {isEmpty(record.EndTime)
            ? "-"
            : timeFormat(record.EndTime)
          }
        </span>
      )
    },
    {
      title: getTableTitle("操作"),
      align: "center",
      render: (_, record) => (
        <Space size="middle">
          {record.State === 1 && (
            <MyButton
              style={{ padding: "2px 8px", color: '#17AF91', background: 'none' }}
              onClick={() => {
                localStorage.setItem('record', JSON.stringify(record));
                localStorage.setItem('isDJPage', isDJPage);
                if (isDJPage) {
                  handlePageChange2('识别详情');
                } else {
                  handlePageChange('识别详情');
                }
              }}
            >
              详情
            </MyButton>
          )}
        </Space>
      ),
    }
  ];
};

export default TableCols;
