import { useState, useEffect } from "react";
import {
  Card,
  Select,
  DatePicker,
  Descriptions,
  Row,
  Col,
  Form,
  Table,
  Button,
  Dropdown,
  Pagination,
  Modal,
  Switch,
  InputNumber,
} from "antd";
import { DownOutlined, SmileOutlined } from "@ant-design/icons";
import AddButton from "@/components/AddButton";
import { getBodyH, getDeviceName, isEmpty } from "@/utils/utils";
import { getGuid } from "@/utils/helper";
import { Get2, Post, Post2, Post3, axiosApi } from "@/services/general";
import WayLineAddForm from "./form_add";
import TableCols from "./table";
import WayLineMap from "./WayLineMap";
import { useModel } from "umi";
import { HGet2, HPost2 } from "@/utils/request";
import LastPageButton from "@/components/LastPageButton";
import dayjs from "dayjs";
import { queryPage2 } from "@/utils/MyRoute";
const { RangePicker } = DatePicker;
import { DynamicDataTable } from '@/pages/SI/components/Common';

const WayLineListPage = ({doNotShowLastButton}) => {
  let sjc1 = localStorage.getItem("PageParams");
  if (isEmpty(sjc1)) {
    sjc1 = "";
  }
  const { DoCMD, DoCMD2 } = useModel("cmdModel");
  const [chartData, setChartData] = useState([]);
  const [canSee, setCanSee] = useState(false);
  const [devices, setDevices] = useState([]);
  const [index, setIndex] = useState({});
  const [sJC, setSjc] = useState(sjc1);
  const [xList, setXList] = useState([]);
  const [sDate, setSDate] = useState({});
  const [ifRefrush, setIfRefrush] = useState(false);
  const { setPage, lastPage } = useModel("pageModel");
  const [currentPage, setCurrentPage] = useState(1); // 当前页码
  const [pageSize, setPageSize] = useState(10); // 每页显示条数
  const [currentRecord, setCurrentRecord] = useState(); // 当前选中记录
  const [isRth_mode, setIsrth_mode] = useState(0); //智能高度/设定高度，0：智能返航，1：定高返航
  const [rth_mode, setRth_mode] = useState(150); //返航模式值

  const items = [
    {
      key: "1",
      label: (
        <a
          target="_blank"
          rel="noopener noreferrer"
          onClick={() => {
            setPage(queryPage2("航点航线"));
          }}
        >
          航点航线
        </a>
      ),
    },
    {
      key: "2",
      label: (
        <a
          target="_blank"
          rel="noopener noreferrer"
          onClick={() => {
            setPage(queryPage2("带状航线"));
          }}
        >
          带状航线
        </a>
      ),
    },
    {
      key: "3",
      label: (
        <a
          target="_blank"
          rel="noopener noreferrer"
          onClick={() => {
            setPage(queryPage2("面状航线"));
          }}
        >
          面状航线
        </a>
      ),
    },
    {
      key: "4",
      label: (
        <a
          target="_blank"
          rel="noopener noreferrer"
          onClick={() => {
            setPage(queryPage2("垂直航线"));
          }}
        >
          垂直航线
        </a>
      ),
    },
  ];

  const ifPush = (e) => {
    let t1 = dayjs("1900/1/1");
    let t2 = dayjs("2900/1/1");
    if (!isEmpty(sDate)) {
      t1 = dayjs(sDate[0]);
      t2 = dayjs(sDate[1]);
    }

    if (!isEmpty(sDate)) {
      const t3 = dayjs(e.CreateTime);
      if (t1.isAfter(t3) || t2.isBefore(t3)) {
        return false;
      }
    }

    if (!isEmpty(sJC)) {
      if (e.SN != sJC) {
        return false;
      }
    }
    return true;
  };
  const getXL = (data) => {
    const xL = [];
    //
    data.forEach((e) => {
      if (ifPush(e)) {
        xL.push(e);
      }
    });
    return xL;
  };
  useEffect(() => {
    const xx = () => {
      const xL = getXL(chartData);
      setXList([...xL]);
    };
    xx();
    localStorage.setItem("PageParams", sJC);
  }, [sJC, sDate, ifRefrush, chartData]);

  useEffect(() => {
    const getDevices = async () => {
      const pst = await Get2("/api/v1/Device/GetAllList", {});
      setDevices(pst);
    };

    const getLineData = async () => {
      let pst = await Get2("/api/v1/WayLine/GetAllList", {});
      if (isEmpty(pst)) pst = [];
      setChartData([...pst]);
      // const xL = getXL(pst);
      // setXList([...xL]);
    };

    getLineData();
    getDevices();
  }, []);

  const refrush = async () => {
    const pst = await HGet2("/api/v1/WayLine/GetAllList", {});
    setChartData(pst);
    setIfRefrush(!ifRefrush);
  };

  const submit = async () => {
    //设置航线返航高度模式-空中下发航线
    let request = {
      fID: currentRecord.WanLineId,
      rth_altitude: rth_mode,
      rth_mode: isRth_mode ? 1 : 0,
    };
    await axiosApi(`/api/v1/WayLine/Fly`, "GET", request);
    message.success("设定成功");
    setCanSee(false);
  };
  const showModal = (record) => {
    setCanSee(true);
    setCurrentRecord(record);
  };

  const fhBtn = <LastPageButton title="航线点位" />;
  const showMap = (record) => {
    setPage(
      <Card title={fhBtn}>
        <WayLineMap h1={getBodyH(180)} data={record} key={getGuid()} />
      </Card>
    );
  };

  const LSWay = async () => {
    await HPost2("/api/v1/WayLine/FlyTo", { SN: "7CTDLCE00AC2J4", PList: "" });
  };

  const exr = (
    <div>
      <WayLineAddForm data={refrush} />
    </div>
  );

  const getWaySelect = (wayList, getLabel) => {
    
    const list = [];
    wayList.forEach((e) => {
      if(!getLabel(e)) return;//如果机场名称为空，则不显示
      list.push(
        <Select.Option key={e} data={e}>
          {getLabel(e)}
        </Select.Option>
      );
    });
    return list;
  };

  const getExr = () => {
    const dList = [];
    chartData.forEach((e) => {
      if (!dList.includes(e.SN)) {
        dList.push(e.SN);
      }
    });

    return (
      <Row>
        <Col style={{ marginLeft: 6.0 }}>
          <Select
            value={sJC ? sJC : undefined}
            allowClear={true}
            style={{ width: 200 }}
            onClear={() => setSjc("")}
            placeholder={"选择机场"}
            onSelect={(e) => setSjc(e)}
          >
            {getWaySelect(dList, (e) => getDeviceName(e))}
          </Select>
        </Col>
        <Col style={{ marginLeft: 6.0 }}>
          <RangePicker onChange={(e) => setSDate(e)} />
        </Col>
        <Col style={{ marginLeft: 12.0 }}>
          <WayLineAddForm data={refrush} />
        </Col>
        <Col style={{ marginLeft: 6.0 }}>
          <Dropdown menu={{ items }} placement="bottom" arrow>
            <Button>
              添加航线
              <DownOutlined />
            </Button>
          </Dropdown>
        </Col>
        {/* <Col style={{ marginLeft: 12.0 }}><WayLineAddByKMLForm  data={refrush}/></Col> */}
      </Row>
    );
  };

  const { columns, rowClassName } = TableCols(
    refrush,
    showMap,
    setPage,
    showModal
  );
  // 计算当前页的数据
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;

  const filteredData = getXL(chartData); // 应用筛选后的数据
  const currentPageData = filteredData.slice(startIndex, endIndex);

  return (
    <>
      <Modal
        title={"空中下发航线"}
        onOk={submit}
        open={canSee}
        onCancel={()=>setCanSee(false)}
        width={450}
      >
        <>
          <Descriptions column={1} colon={false} bordered>
            <Descriptions.Item label="返航模式">
              <Switch
                onChange={(e) => {
                  setIsrth_mode(e);
                }}
                checkedChildren="设定高度"
                unCheckedChildren="智能模式"
              />
            </Descriptions.Item>
            {isRth_mode && (
              <Descriptions.Item label="设定高度值(20米-1500米)">
                <InputNumber
                  min={20}
                  max={1500}
                  value={rth_mode}
                  onChange={(e) => {
                    setRth_mode(e.target.value);
                  }}
                  allowClear
                />
              </Descriptions.Item>
            )}
          </Descriptions>
        </>
      </Modal>
      <div style={{ margin: 0, height: getBodyH(56) }}>
        <Card
          title={doNotShowLastButton ? '航线列表' : <LastPageButton title="航线列表" />}
          bordered={false}
          extra={getExr()}
        >
          <div>
            {isEmpty(chartData) ? (
              <div />
            ) : (
              <>
                <DynamicDataTable
                  dataSource={currentPageData} // 使用当前页的数据
                  columns={columns}
                  rowKey={(record) => record.ID}
                  rowClassName={rowClassName}
                  pagination={false}
                  scroll={{ y: getBodyH(276), x: "auto" }}
                  // style={{whiteSpace: 'nowrap',overflow: 'hidden',textEllipsis: 'ellipsis'}}
                />
                <div style={{ display: "flex", justifyContent: "flex-end" }}>
                  <Pagination
                    style={{ marginTop: 16, textAlign: "center" }}
                    current={currentPage}
                    pageSize={pageSize}
                    total={filteredData.length} // 使用筛选后的数据总数
                    onChange={(page, size) => {
                      setCurrentPage(page);
                      setPageSize(size); // 更新每页显示条数
                    }}
                    showSizeChanger
                    // showTotal={(total) => `总共 ${total} 条`}
                    onShowSizeChange={(current, size) => {
                      setPageSize(size);
                      setCurrentPage(1); // 重置到第一页
                    }}
                    showQuickJumper
                  />
                </div>
              </>
            )}
            {/* <Modal  title={null} footer={null} onOk={null} open={canSee} style={{height:600.0,width:600.0}}  onCancel={()=>setCanSee(false)}>
       <Card title='航线信息'> {WayLineMap(600,index)}</Card>
    </Modal> */}
          </div>
        </Card>
      </div>
    </>
  );
};

export default WayLineListPage;
