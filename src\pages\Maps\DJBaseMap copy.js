import React, { useRef, useState, useEffect } from 'react'
import {
  MapContainer,
  LayersControl,
  Tooltip,
  LayerGroup,
  Polyline,

  TileLayer,
  GeoJSON, useMapEvents
} from 'react-leaflet'
import 'leaflet/dist/leaflet.css';
import { GetDiTuGps, GetDiTuUrl } from './ditu';

import { isEmpty, getBodyH } from './../../utils/utils'

import { Location_Market6, Location_Market5, WaylinePointMarker, WaylinePointMarker2 } from './dt_market';
import { useModel } from 'umi';
import MapClickPanel from './MapClickPanel';
import DrawWayLinePanel from './DrawWayLinePanel';

import { ConvertPList, aqqPoints, getPList } from './helper';
import getPlacements from 'antd/es/_util/placements';
import { getGuid } from '@/utils/helper';
import { Wgs84ToGcj02, out_of_china } from './gps_helper';
import {ZXLine,ZXXPoints} from './gcline';

const { Overlay } = LayersControl


const DJBaseMap = (props) => {
  const { h1, sn } = props;
  // console.log('DJBaseMap',props);
  const { gps, pList } = useModel('gpsModel');
  const { mapUrl,ifWayline } = useModel('mapModel');
 // const { jc } = useModel('dockModel');
  const device = JSON.parse(localStorage.getItem('device'))
  const [pL, setPL] = useState([])
  const [center, setCenter] = useState([device.Lat, device.Lng])
  //console.log('dock',ifWayline);

  const getP = (p1) => {
    const pb1 = GetDiTuGps(mapUrl);
    if (pb1) {
      return Wgs84ToGcj02(p1[0], p1[1]);
    }
    return p1;
  }

  const getLine=()=>{
    const list=[]
    let n=0;
    let v1=[];
    const ZL=ZXXPoints.split(",");
    ZL.forEach(e => {
        if(n==1){
          v1[0]=Number(e);
          list.push([...v1]);
          n=0;
        }else{
          v1[1]=Number(e);
          n=1;
        }
    });
    console.log('gcline',list);
    
}
  useEffect(() => {
  //if (!ifWayline) return;
    //getLine();
    let pL2 =getPList( localStorage.getItem("wayPoints"))
    if (GetDiTuGps(mapUrl)) {
      pL2 = ConvertPList(pL2);
    }
    setPL(pL2)
  }, [mapUrl]);

  useEffect(() => {
    setCenter(getP(gps))
  }, [gps]);


  if (isEmpty(device)) return <div></div>




  const getWayLine = () => {
    console.log('getWayLine',pL);
    const list = [];
    if (isEmpty(pL)) return list;
    if(!ifWayline) return list;
    list.push(<Polyline weight={2} color={'green'} positions={pL} />);
    let i = 1;
    pL.forEach(p => {
      if(out_of_china(p[0],p[1])){
        list.push(WaylinePointMarker2(p, '点位' + i));
        i++;
      }
     
    })
    return list;
  }

  const getPL=(data)=>{
    if (GetDiTuGps(mapUrl)) {
      return ConvertPList(data);
    }
    return data;
  }


  const getHomeIcon = () => {
    if (isEmpty(device)) return;
    let p2 = [device.Lat, device.Lng]
    if (isEmpty(p2)) return ;
   
    if (GetDiTuGps(mapUrl)) {
      p2 = Wgs84ToGcj02(p2[0],p2[1])
    }
    return Location_Market5({ Lat: p2[0], Lng: p2[1], DeviceName: device.DName })
  }

  const getFJIcon = () => {
    if (isEmpty(gps)) return ;

    const jcdata1=JSON.parse( localStorage.getItem('jcdata1'))
    if(jcdata1.mode_code==0) return;

    //if(jc.mode_code==0) return;
   
    let p2 = [gps[0], gps[1]]
    if (isEmpty(p2)) return ;
   
    if (GetDiTuGps(mapUrl)) {
      p2 = Wgs84ToGcj02(p2[0],p2[1])
    }

    return   Location_Market6({ Lat: p2[0], Lng: p2[1], DeviceName: '无人机' })
  }


  console.log('getline', pList);

  return (
    <div>

      <MapContainer layersOptions={null} attributionControl={null} zoomControl={null} preferCanvas={true} center={center} zoom={16} style={{ width: '100%', height: h1,borderRadius:5.0 }}>


        <TileLayer
          attribution={null}
          url={mapUrl}
        />
        
        {getHomeIcon()}
        {/* {gps[2]>device.Height+10?Location_Market6({Lat:center[0],Lng:center[1],DeviceName:'无人机'}):null} */}
        {/* {gps[2]>device.Height+10? <LocationMarker gps={center} sn={sn}></LocationMarker>:null} */}
        {getFJIcon()}
        {/* {<MapClickPanel gps={gps} sn={sn} baseMap={mapUrl}></MapClickPanel>} */}
        {<DrawWayLinePanel ifDraw={true} gps={gps} sn={sn} baseMap={mapUrl}></DrawWayLinePanel>}
        {getWayLine()}
        <Polyline weight={3} color={'red'} positions={ZXLine} />
        {ifWayline?<Polyline key={getGuid()} weight={2} color={'red'} positions={getPL( pList.current)} />:null}
      </MapContainer>

    </div>
  )
}


export default DJBaseMap;