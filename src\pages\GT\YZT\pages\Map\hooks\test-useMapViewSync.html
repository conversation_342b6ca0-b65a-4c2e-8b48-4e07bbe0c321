<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>useMapViewSync Hook 功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .map-container {
            width: 300px;
            height: 200px;
            border: 2px solid #ccc;
            margin: 10px;
            display: inline-block;
            position: relative;
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        .map-title {
            position: absolute;
            top: 5px;
            left: 5px;
            background: rgba(255,255,255,0.8);
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .virtual-cursor {
            position: absolute;
            font-size: 16px;
            pointer-events: none;
            z-index: 1000;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
            transition: transform 0.05s ease-out;
        }
        .virtual-cursor.dragging {
            font-size: 18px;
            filter: drop-shadow(0 3px 6px rgba(0,0,0,0.4));
        }
        .virtual-cursor.zooming {
            animation: pulse 0.5s ease-in-out infinite alternate;
        }
        @keyframes pulse {
            from { transform: scale(1); }
            to { transform: scale(1.2); }
        }
        #results {
            max-height: 400px;
            overflow-y: auto;
        }
        .sync-mode-selector {
            margin: 10px 0;
        }
        .sync-mode-selector label {
            margin-right: 15px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 useMapViewSync Hook 功能测试</h1>
        <p>这个页面用于测试重构后的 useMapViewSync Hook 的事件转发功能。</p>
        
        <div class="sync-mode-selector">
            <label>同步模式：</label>
            <label><input type="radio" name="syncMode" value="off" checked> 关闭</label>
            <label><input type="radio" name="syncMode" value="on_end"> 结束时同步</label>
            <label><input type="radio" name="syncMode" value="realtime"> 实时同步</label>
        </div>
        
        <button onclick="startTest()">开始测试</button>
        <button onclick="clearResults()">清空结果</button>
    </div>

    <div class="test-container">
        <h2>模拟地图容器</h2>
        <div class="map-container" id="map1">
            <div class="map-title">地图 1 (源)</div>
            <div id="virtualCursor1" class="virtual-cursor" style="display: none;">👆</div>
        </div>
        <div class="map-container" id="map2">
            <div class="map-title">地图 2 (目标)</div>
            <div id="virtualCursor2" class="virtual-cursor" style="display: none;">👆</div>
        </div>
    </div>

    <div class="test-container">
        <h2>测试结果</h2>
        <div id="results"></div>
    </div>

    <script type="module">
        // 模拟 useMapViewSync Hook 的核心逻辑
        class MockMapViewSync {
            constructor(mapId, syncMode = 'off') {
                this.mapId = mapId;
                this.syncMode = syncMode;
                this.virtualMousePos = null;
                this.isDragging = false;
                this.isZooming = false;
                this.eventListeners = new Map();
                this.isReceivingUpdate = false;
                
                // 模拟地图容器
                this.mapContainer = document.getElementById(mapId);
                this.virtualCursor = document.getElementById(`virtualCursor${mapId.slice(-1)}`);
                
                this.setupEventListeners();
            }

            setupEventListeners() {
                if (this.syncMode !== 'realtime') return;

                const onMouseDown = (e) => {
                    this.isDragging = true;
                    this.lastMousePos = { x: e.clientX, y: e.clientY };
                    
                    window.notifyMapEvent(this.mapId, {
                        type: 'mousedown',
                        payload: {
                            screenPoint: { x: e.clientX, y: e.clientY },
                            button: e.button
                        }
                    });
                };

                const onMouseUp = (e) => {
                    this.isDragging = false;
                    
                    window.notifyMapEvent(this.mapId, {
                        type: 'mouseup',
                        payload: {
                            screenPoint: { x: e.clientX, y: e.clientY },
                            button: e.button
                        }
                    });
                };

                const onMouseMove = (e) => {
                    const currentPos = { x: e.clientX, y: e.clientY };
                    
                    // 广播鼠标位置
                    window.notifyMapEvent(this.mapId, {
                        type: 'mousemove',
                        payload: { screenPoint: currentPos }
                    });

                    // 如果正在拖拽，计算并广播平移事件
                    if (this.isDragging && this.lastMousePos) {
                        const dx = currentPos.x - this.lastMousePos.x;
                        const dy = currentPos.y - this.lastMousePos.y;
                        
                        if (Math.abs(dx) > 1 || Math.abs(dy) > 1) {
                            window.notifyMapEvent(this.mapId, {
                                type: 'pan',
                                payload: { dx, dy, screenPoint: currentPos }
                            });
                            
                            this.lastMousePos = currentPos;
                        }
                    }
                };

                const onWheel = (e) => {
                    e.preventDefault();
                    
                    const delta = e.deltaY > 0 ? -1 : 1;
                    
                    window.notifyMapEvent(this.mapId, {
                        type: 'zoom',
                        payload: {
                            delta,
                            anchorPoint: { x: e.clientX, y: e.clientY },
                            screenPoint: { x: e.clientX, y: e.clientY }
                        }
                    });
                };

                const onMouseLeave = () => {
                    this.isDragging = false;
                    window.notifyMapEvent(this.mapId, {
                        type: 'mouseleave',
                        payload: {}
                    });
                };

                // 添加事件监听器
                this.mapContainer.addEventListener('mousedown', onMouseDown);
                this.mapContainer.addEventListener('mouseup', onMouseUp);
                this.mapContainer.addEventListener('mousemove', onMouseMove);
                this.mapContainer.addEventListener('wheel', onWheel, { passive: false });
                this.mapContainer.addEventListener('mouseleave', onMouseLeave);

                // 保存引用以便清理
                this.eventListeners.set('mousedown', onMouseDown);
                this.eventListeners.set('mouseup', onMouseUp);
                this.eventListeners.set('mousemove', onMouseMove);
                this.eventListeners.set('wheel', onWheel);
                this.eventListeners.set('mouseleave', onMouseLeave);
            }

            handleExternalMapEvent(eventData) {
                if (this.isReceivingUpdate) return;
                
                const { type, payload } = eventData;
                this.isReceivingUpdate = true;

                try {
                    // 更新虚拟鼠标位置
                    if (payload.screenPoint) {
                        this.updateVirtualMouse(payload.screenPoint);
                    }
                    if (type === 'mouseleave') {
                        this.hideVirtualMouse();
                    }

                    // 更新操作状态
                    if (type === 'mousedown') {
                        this.isDragging = true;
                        this.updateCursorState();
                    } else if (type === 'mouseup') {
                        this.isDragging = false;
                        this.updateCursorState();
                    } else if (type === 'zoom') {
                        this.isZooming = true;
                        this.updateCursorState();
                        setTimeout(() => {
                            this.isZooming = false;
                            this.updateCursorState();
                        }, 300);
                    }

                    // 模拟地图操作
                    this.simulateMapOperation(type, payload);
                } finally {
                    setTimeout(() => {
                        this.isReceivingUpdate = false;
                    }, 50);
                }
            }

            updateVirtualMouse(screenPoint) {
                if (!this.virtualCursor) return;
                
                const containerRect = this.mapContainer.getBoundingClientRect();
                const relativeX = screenPoint.x - containerRect.left;
                const relativeY = screenPoint.y - containerRect.top;
                
                if (relativeX >= 0 && relativeX <= containerRect.width &&
                    relativeY >= 0 && relativeY <= containerRect.height) {
                    this.virtualCursor.style.display = 'block';
                    this.virtualCursor.style.left = relativeX + 'px';
                    this.virtualCursor.style.top = relativeY + 'px';
                } else {
                    this.hideVirtualMouse();
                }
            }

            hideVirtualMouse() {
                if (this.virtualCursor) {
                    this.virtualCursor.style.display = 'none';
                }
            }

            updateCursorState() {
                if (!this.virtualCursor) return;
                
                this.virtualCursor.className = 'virtual-cursor';
                if (this.isDragging) {
                    this.virtualCursor.className += ' dragging';
                    this.virtualCursor.textContent = '✋';
                } else if (this.isZooming) {
                    this.virtualCursor.className += ' zooming';
                    this.virtualCursor.textContent = '🔍';
                } else {
                    this.virtualCursor.textContent = '👆';
                }
            }

            simulateMapOperation(type, payload) {
                // 模拟地图操作的视觉反馈
                const container = this.mapContainer;
                
                switch (type) {
                    case 'pan':
                        // 简单的视觉反馈 - 改变背景色
                        container.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
                        setTimeout(() => {
                            container.style.backgroundColor = '';
                        }, 100);
                        break;
                    case 'zoom':
                        // 缩放视觉反馈
                        container.style.backgroundColor = 'rgba(40, 167, 69, 0.1)';
                        setTimeout(() => {
                            container.style.backgroundColor = '';
                        }, 200);
                        break;
                }
            }

            cleanup() {
                // 清理事件监听器
                this.eventListeners.forEach((listener, eventType) => {
                    this.mapContainer.removeEventListener(eventType, listener);
                });
                this.eventListeners.clear();
                this.hideVirtualMouse();
            }
        }

        // 全局变量
        let map1Sync = null;
        let map2Sync = null;
        let currentSyncMode = 'off';

        // 事件转发系统
        window.notifyMapEvent = function(sourceId, eventData) {
            const targetSync = sourceId === 'map1' ? map2Sync : map1Sync;
            if (targetSync && targetSync.syncMode === 'realtime') {
                targetSync.handleExternalMapEvent(eventData);
                
                // 记录事件
                window.addResult(`📡 ${sourceId} → ${targetSync.mapId}: ${eventData.type} 事件`, 'info');
            }
        };

        // 测试函数
        window.startTest = function() {
            const resultsDiv = document.getElementById('results');
            
            window.addResult = function(message, type = 'info') {
                const div = document.createElement('div');
                div.className = `test-result ${type}`;
                div.textContent = message;
                resultsDiv.appendChild(div);
                resultsDiv.scrollTop = resultsDiv.scrollHeight;
            };

            // 获取当前选择的同步模式
            const selectedMode = document.querySelector('input[name="syncMode"]:checked').value;
            currentSyncMode = selectedMode;

            window.addResult('🧪 开始测试 useMapViewSync Hook 重构功能...', 'info');
            window.addResult(`当前同步模式: ${selectedMode}`, 'info');

            // 清理之前的实例
            if (map1Sync) map1Sync.cleanup();
            if (map2Sync) map2Sync.cleanup();

            // 创建新的模拟实例
            map1Sync = new MockMapViewSync('map1', selectedMode);
            map2Sync = new MockMapViewSync('map2', selectedMode);

            if (selectedMode === 'realtime') {
                window.addResult('✅ 实时同步模式已启用', 'success');
                window.addResult('💡 请在地图1上进行鼠标操作，观察地图2的虚拟鼠标和视觉反馈', 'info');
            } else if (selectedMode === 'on_end') {
                window.addResult('✅ 结束时同步模式已启用', 'success');
                window.addResult('💡 此模式下不会显示虚拟鼠标，仅在操作结束时同步', 'info');
            } else {
                window.addResult('✅ 同步已关闭', 'success');
                window.addResult('💡 地图将独立操作，不会进行同步', 'info');
            }

            window.addResult('🎯 测试要点:', 'info');
            window.addResult('  1. 鼠标移动 - 虚拟鼠标跟随', 'info');
            window.addResult('  2. 拖拽操作 - 平移事件转发', 'info');
            window.addResult('  3. 滚轮缩放 - 缩放事件转发', 'info');
            window.addResult('  4. 鼠标离开 - 虚拟鼠标消失', 'info');
        };

        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
        };

        // 同步模式切换监听
        document.addEventListener('change', function(e) {
            if (e.target.name === 'syncMode') {
                const newMode = e.target.value;
                if (map1Sync && map2Sync) {
                    // 重新初始化
                    window.startTest();
                }
            }
        });
    </script>
</body>
</html>
