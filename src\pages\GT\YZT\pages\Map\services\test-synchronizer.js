/**
 * MapViewSynchronizer 手动验证脚本
 * 验证增强后的事件转发功能
 */

// 由于这是Node.js环境，我们需要模拟一些浏览器API
global.console = console;
global.setTimeout = setTimeout;
global.clearTimeout = clearTimeout;

// 导入我们的同步器
const MapViewSynchronizer = require('./MapViewSynchronizer.js').default;

// 创建测试函数
function testMapViewSynchronizer() {
  console.log('🧪 开始测试 MapViewSynchronizer 增强功能...\n');

  // 创建同步器实例
  const synchronizer = new MapViewSynchronizer();
  synchronizer.setEnabled(true);
  synchronizer.setDebugMode(true);

  // 模拟地图实例和回调
  const mockMapInstance1 = { id: 'map1' };
  const mockMapInstance2 = { id: 'map2' };

  let receivedEvents = [];
  const mockCallbacks1 = {
    onMapEvent: (eventData) => {
      receivedEvents.push({ target: 'map1', event: eventData });
      console.log('📍 Map1 收到事件:', eventData.type);
    },
    onError: (error, eventData) => {
      console.error('❌ Map1 错误:', error.message, eventData);
    }
  };

  const mockCallbacks2 = {
    onMapEvent: (eventData) => {
      receivedEvents.push({ target: 'map2', event: eventData });
      console.log('📍 Map2 收到事件:', eventData.type);
    },
    onError: (error, eventData) => {
      console.error('❌ Map2 错误:', error.message, eventData);
    }
  };

  // 测试1: 订阅功能
  console.log('1️⃣ 测试订阅功能...');
  const subscribe1 = synchronizer.subscribe('map1', mockMapInstance1, mockCallbacks1);
  const subscribe2 = synchronizer.subscribe('map2', mockMapInstance2, mockCallbacks2);
  
  console.log(`   Map1 订阅结果: ${subscribe1 ? '✅ 成功' : '❌ 失败'}`);
  console.log(`   Map2 订阅结果: ${subscribe2 ? '✅ 成功' : '❌ 失败'}`);
  console.log(`   观察者数量: ${synchronizer.getObserverCount()}\n`);

  // 测试2: 事件数据验证
  console.log('2️⃣ 测试事件数据验证...');
  
  const validPanEvent = {
    type: 'pan',
    payload: { dx: 10, dy: 20, screenPoint: { x: 100, y: 200 } }
  };
  
  const invalidPanEvent = {
    type: 'pan',
    payload: { dx: 'invalid' }
  };
  
  const validZoomEvent = {
    type: 'zoom',
    payload: { delta: 1, anchorPoint: { x: 100, y: 200 } }
  };
  
  const validMouseEvent = {
    type: 'mousemove',
    payload: { screenPoint: { x: 150, y: 250 } }
  };

  console.log(`   有效 Pan 事件: ${synchronizer.isValidEventData(validPanEvent) ? '✅ 通过' : '❌ 失败'}`);
  console.log(`   无效 Pan 事件: ${synchronizer.isValidEventData(invalidPanEvent) ? '❌ 失败' : '✅ 正确拒绝'}`);
  console.log(`   有效 Zoom 事件: ${synchronizer.isValidEventData(validZoomEvent) ? '✅ 通过' : '❌ 失败'}`);
  console.log(`   有效 Mouse 事件: ${synchronizer.isValidEventData(validMouseEvent) ? '✅ 通过' : '❌ 失败'}\n`);

  // 测试3: 事件转发
  console.log('3️⃣ 测试事件转发...');
  
  receivedEvents = []; // 清空接收记录
  
  // 从 map1 发送 pan 事件
  const panResult = synchronizer.notifyMapEvent('map1', validPanEvent);
  console.log(`   Pan 事件转发结果: ${panResult ? '✅ 成功' : '❌ 失败'}`);
  
  // 从 map1 发送 zoom 事件
  const zoomResult = synchronizer.notifyMapEvent('map1', validZoomEvent);
  console.log(`   Zoom 事件转发结果: ${zoomResult ? '✅ 成功' : '❌ 失败'}`);
  
  // 从 map2 发送 mouse 事件
  const mouseResult = synchronizer.notifyMapEvent('map2', validMouseEvent);
  console.log(`   Mouse 事件转发结果: ${mouseResult ? '✅ 成功' : '❌ 失败'}`);

  // 等待异步操作完成
  setTimeout(() => {
    console.log(`   实际接收到的事件数量: ${receivedEvents.length}`);
    console.log('   接收详情:');
    receivedEvents.forEach((item, index) => {
      console.log(`     ${index + 1}. ${item.target} 收到 ${item.event.type} 事件`);
    });

    // 测试4: 统计信息
    console.log('\n4️⃣ 测试统计信息...');
    const stats = synchronizer.getStats();
    console.log(`   总同步次数: ${stats.totalSyncs}`);
    console.log(`   成功次数: ${stats.successfulSyncs}`);
    console.log(`   失败次数: ${stats.failedSyncs}`);
    console.log(`   最后同步时间: ${stats.lastSyncTime}`);

    // 测试5: 禁用同步
    console.log('\n5️⃣ 测试禁用同步...');
    synchronizer.setEnabled(false);
    receivedEvents = [];
    
    const disabledResult = synchronizer.notifyMapEvent('map1', validPanEvent);
    console.log(`   禁用状态下的事件转发: ${disabledResult ? '❌ 意外成功' : '✅ 正确拒绝'}`);
    
    setTimeout(() => {
      console.log(`   禁用状态下接收到的事件数量: ${receivedEvents.length} (应该为0)`);
      
      console.log('\n🎉 测试完成！');
      
      // 验证结果
      const allTestsPassed = 
        subscribe1 && subscribe2 &&
        synchronizer.isValidEventData(validPanEvent) &&
        !synchronizer.isValidEventData(invalidPanEvent) &&
        synchronizer.isValidEventData(validZoomEvent) &&
        synchronizer.isValidEventData(validMouseEvent) &&
        panResult && zoomResult && mouseResult &&
        !disabledResult;
      
      console.log(`\n📊 总体测试结果: ${allTestsPassed ? '✅ 全部通过' : '❌ 存在失败'}`);
      
      if (allTestsPassed) {
        console.log('🚀 MapViewSynchronizer 增强功能验证成功！可以进行下一步任务。');
      } else {
        console.log('⚠️  存在问题，需要进一步调试。');
      }
    }, 100);
  }, 100);
}

// 运行测试
testMapViewSynchronizer();
