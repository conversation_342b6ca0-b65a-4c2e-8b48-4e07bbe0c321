/**
 * MapViewSynchronizer.js
 * 
 * 一个使用观察者模式在多个地图实例之间同步地图视图的服务。
 * 这允许当一个地图的视图改变（缩放、平移等）时，多个地图保持同步。
 */

/**
 * MapViewSynchronizer 类
 * 实现观察者模式以在多个实例之间同步地图视图
 */
class MapViewSynchronizer {
  constructor() {
    // 观察者ID到其回调和地图实例的映射
    this.observers = new Map();
    
    // 启用/禁用同步的标志
    this.enabled = false;
    
    // 防止同步过程中出现无限循环的标志
    this.isUpdating = false;
    
    // 最后一次视图状态，用于防止重复更新
    this.lastViewState = null;
    
    // 调试模式标志
    this.debugMode = false;
    
    // 同步统计信息
    this.stats = {
      totalSyncs: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      lastSyncTime: null
    };
  }

  /**
   * 启用或禁用同步
   * @param {boolean} enabled - 是否应启用同步
   * @returns {boolean} 新的启用状态
   */
  setEnabled(enabled) {
    const newState = Boolean(enabled);
    if (this.enabled !== newState) {
      this.enabled = newState;
      this.log(`地图视图同步已${this.enabled ? '启用' : '禁用'}`);
    }
    return this.enabled;
  }
  
  /**
   * 设置调试模式
   * @param {boolean} enabled - 是否启用调试模式
   * @returns {boolean} 新的调试模式状态
   */
  setDebugMode(enabled) {
    this.debugMode = Boolean(enabled);
    this.log(`调试模式已${this.debugMode ? '启用' : '禁用'}`);
    return this.debugMode;
  }

  /**
   * 获取当前启用状态
   * @returns {boolean} 当前是否启用同步
   */
  isEnabled() {
    return this.enabled;
  }

  /**
   * 添加地图实例作为观察者
   * @param {string} id - 地图实例的唯一标识符
   * @param {Object} mapInstance - 地图实例的引用
   * @param {Object} callbacks - 包含回调函数的对象
   * @param {Function} callbacks.onViewChange - 视图变化时调用的函数
   * @returns {boolean} 成功状态
   */
  subscribe(id, mapInstance, callbacks) {
    if (!id || !mapInstance || !callbacks || !callbacks.onViewChange) {
      console.error('地图视图同步器：无效的订阅参数');
      return false;
    }

    // 存储地图实例及其回调
    this.observers.set(id, {
      mapInstance,
      callbacks
    });

    console.log(`地图视图同步器：地图 ${id} 已订阅`);
    return true;
  }

  /**
   * 从观察者中移除地图实例
   * @param {string} id - 要取消订阅的地图实例的唯一标识符
   * @returns {boolean} 成功状态
   */
  unsubscribe(id) {
    if (!id || !this.observers.has(id)) {
      console.warn(`地图视图同步器：未找到要取消订阅的地图 ${id}`);
      return false;
    }

    // 移除观察者
    this.observers.delete(id);
    console.log(`地图视图同步器：地图 ${id} 已取消订阅`);
    return true;
  }

  /**
   * 通知所有观察者关于视图变化
   * @param {string} sourceId - 发起变化的地图的ID
   * @param {Object} viewState - 包含视图状态信息的对象（缩放、中心点等）
   * @returns {boolean} 成功状态
   */
  /**
   * 通知所有观察者关于视图变化
   * @param {string} sourceId - 发起变化的地图的ID
   * @param {Object} viewState - 包含视图状态信息的对象（缩放、中心点等）
   * @returns {boolean} 成功状态
   */
  notifyViewChange(sourceId, viewState) {
    // 更新统计信息
    this.stats.totalSyncs++;
    this.stats.lastSyncTime = new Date();
    
    // 如果同步被禁用则跳过
    if (!this.enabled) {
      this.log(`同步已禁用，跳过通知 (源: ${sourceId})`);
      return false;
    }

    // 如果已经在更新中则跳过，防止无限循环
    if (this.isUpdating) {
      this.log(`已在更新中，跳过通知 (源: ${sourceId})`);
      return false;
    }
    
    // 验证视图状态
    if (!this.isValidViewState(viewState)) {
      console.error('地图视图同步器：无效的视图状态', viewState);
      this.stats.failedSyncs++;
      return false;
    }
    
    // 检查是否与上次视图状态相同（防止重复更新）
    if (this.lastViewState && this.isSameViewState(viewState, this.lastViewState)) {
      this.log(`视图状态未变化，跳过通知 (源: ${sourceId})`);
      return false;
    }

    // 设置isUpdating标志以防止循环
    this.isUpdating = true;
    this.lastViewState = { ...viewState };

    try {
      // 通知除源之外的所有观察者
      let notifiedCount = 0;
      this.observers.forEach((observer, id) => {
        // 跳过变化的源以防止循环
        if (id !== sourceId) {
          try {
            // 调用此观察者的onViewChange回调
            observer.callbacks.onViewChange(viewState);
            notifiedCount++;
          } catch (error) {
            console.error(`地图视图同步器：通知观察者 ${id} 时出错`, error);
          }
        }
      });
      
      this.log(`已同步视图状态到 ${notifiedCount} 个地图 (源: ${sourceId})`);
      
      if (notifiedCount > 0) {
        this.stats.successfulSyncs++;
        return true;
      } else {
        this.stats.failedSyncs++;
        return false;
      }
    } catch (error) {
      console.error('地图视图同步器：通知过程中出错', error);
      this.stats.failedSyncs++;
      return false;
    } finally {
      // 重置isUpdating标志（使用setTimeout确保视图更新完成）
      setTimeout(() => {
        this.isUpdating = false;
      }, 100);
    }
  }
  
  /**
   * 验证视图状态是否有效
   * @param {Object} viewState - 视图状态对象
   * @returns {boolean} 是否有效
   * @private
   */
  isValidViewState(viewState) {
    if (!viewState || typeof viewState !== 'object') {
      return false;
    }

    // 2D地图（Leaflet）视图状态验证
    if (viewState.center && typeof viewState.center === 'object' &&
        (typeof viewState.center.lat === 'number' && typeof viewState.center.lng === 'number') &&
        typeof viewState.zoom === 'number') {
      return true;
    }

    // 3D地图（Cesium）视图状态验证
    if (viewState.center && typeof viewState.center === 'object' &&
        (typeof viewState.center.lat === 'number' && typeof viewState.center.lng === 'number') &&
        typeof viewState.height === 'number' &&
        typeof viewState.heading === 'number' &&
        typeof viewState.pitch === 'number') {
      return true;
    }

    return false;
  }

  /**
   * 比较两个视图状态是否相同
   * @param {Object} state1 - 第一个视图状态
   * @param {Object} state2 - 第二个视图状态
   * @returns {boolean} 是否相同
   * @private
   */
  isSameViewState(state1, state2) {
    // 检查两个状态是否都有center属性
    if (!state1.center || !state2.center) {
      return false;
    }
    
    // 2D地图（Leaflet）视图状态比较
    if (typeof state1.zoom === 'number' && typeof state2.zoom === 'number') {
      const isSameZoom = Math.abs(state1.zoom - state2.zoom) < 0.01;
      const isSameCenter =
        Math.abs(state1.center.lat - state2.center.lat) < 0.000001 &&
        Math.abs(state1.center.lng - state2.center.lng) < 0.000001;
        
      return isSameZoom && isSameCenter;
    }

    // 3D地图（Cesium）视图状态比较
    if (typeof state1.heading === 'number' && typeof state2.heading === 'number') {
      const isSameHeading = Math.abs(state1.heading - state2.heading) < 0.01;
      const isSamePitch = Math.abs(state1.pitch - state2.pitch) < 0.01;
      const isSameCenter =
        Math.abs(state1.center.lat - state2.center.lat) < 0.000001 &&
        Math.abs(state1.center.lng - state2.center.lng) < 0.000001;
      const isSameHeight = Math.abs(state1.height - state2.height) < 1.0;
        
      return isSameHeading && isSamePitch && isSameCenter && isSameHeight;
    }

    return false;
  }
  
  /**
   * 记录日志（仅在调试模式下）
   * @param {string} message - 日志消息
   * @private
   */
  log(message) {
    if (this.debugMode) {
      console.log(`[MapViewSync] ${message}`);
    }
  }

  /**
   * 获取已订阅观察者的数量
   * @returns {number} 观察者数量
   */
  getObserverCount() {
    return this.observers.size;
  }

  /**
   * 检查特定地图是否已订阅
   * @param {string} id - 要检查的地图ID
   * @returns {boolean} 地图是否已订阅
   */
  hasObserver(id) {
    return this.observers.has(id);
  }

  /**
   * 清除所有观察者
   * @returns {boolean} 成功状态
   */
  /**
   * 清除所有观察者
   * @returns {boolean} 成功状态
   */
  clearAllObservers() {
    const count = this.observers.size;
    this.observers.clear();
    this.log(`已清除所有观察者 (共 ${count} 个)`);
    return true;
  }
  
  /**
   * 获取同步统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return { ...this.stats };
  }
  
  /**
   * 重置同步器状态
   * @returns {boolean} 成功状态
   */
  reset() {
    this.observers.clear();
    this.enabled = false;
    this.isUpdating = false;
    this.lastViewState = null;
    this.stats = {
      totalSyncs: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      lastSyncTime: null
    };
    this.log('地图视图同步服务已重置');
    return true;
  }
}

// 创建单例实例
const mapViewSynchronizer = new MapViewSynchronizer();

// 导出单例实例
export default mapViewSynchronizer;