export function getFontStyle(top,left,col1,size) {
    return { position: 'absolute',top,left, color: col1, fontFamily: 'MiSan', WebkitTextStroke: '0.5px #000', WebkitTextStrokeColor: 'black', fontSize: size, fontWeight: 'bold' }
} 

export function getGrayStyle(width,height,top,left,radius) {
    return {
        position: 'absolute',
        width,left,top,height,
        zIndex: 1000,
        display: 'block',
        background:'rgba(65, 65, 65, 0.8)',
        borderRadius:radius,
       // background:'white'
      }
} 

export const greyCol='rgba(65, 65, 65, 0.8)';

export function getAngle(lat1, lon1, lat2, lon2) {
    const dLon = ((lon2 - lon1) * Math.PI) / 180;
    const y = Math.sin(dLon) * Math.cos((lat2 * Math.PI) / 180);
    const x =
      Math.cos((lat1 * Math.PI) / 180) * Math.sin((lat2 * Math.PI) / 180) -
      Math.sin((lat1 * Math.PI) / 180) *
        Math.cos((lat2 * Math.PI) / 180) *
        Math.cos(dLon);
    const brng = (Math.atan2(y, x) * 180) / Math.PI;
    const angle = (brng + 360) % 360;
    return angle;
}

export  const getMinstr=(v)=>{
    let v1=Math.floor( v/60);
    let v2= v%60;
    if(v2<10){
      v2="0"+v2
    }
    return v1+":"+v2
}
