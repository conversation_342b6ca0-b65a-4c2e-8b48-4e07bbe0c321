 const JCStart2 = async () => {
    // if (ifJCRtmp) return;
     //await Get2(`/api/v1/RtmpSource/Close?sn=${device.SN}&device=${device.SN}&camera=165-0-7`)
     Get2(`/api/v1/RtmpSource/Start?sn=${device.SN}&device=${device.SN}&camera=165-0-7`)
   }
 
     const FJStart2 = async () => {
    // if (ifFJRtmp) return;
     //await Get2(`/api/v1/RtmpSource/Close?sn=${device.SN}&device=${device.SN2}&camera=${device.Camera2}`)
     // if (ifFJRtmp) return;
      await Get2(`/api/v1/RtmpSource/Start?sn=${device.SN}&device=${device.SN2}&camera=${device.Camera2}`)
   }
 
    const JCStart = async () => {
     // if (ifJCRtmp) return;
      await Get2(`/api/v1/Live/RtmpStart?sn=${device.SN}&device=${device.SN}&camera=165-0-7&quality=0`)
     // Get2(`/api/v1/RtmpSource/Start?sn=${device.SN}&device=${device.SN}&camera=165-0-7`)
    }
 
    const FJStart = async () => {
     // if (ifJCRtmp) return;
      await Get2(`/api/v1/Live/RtmpStart?sn=${device.SN}&device=${device.SN2}&camera=${device.Camera2}&quality=0`)
     // Get2(`/api/v1/RtmpSource/Start?sn=${device.SN}&device=${device.SN}&camera=165-0-7`)
    }


    export function getGrayStyle(width,height,top,left,radius) {
      return {
          position: 'absolute',
          width,left,top,height,
          zIndex: 1000,
          display: 'block',
          background:'rgba(65, 65, 65, 0.8)',
          borderRadius:radius,
         // background:'white'
        }
  } 