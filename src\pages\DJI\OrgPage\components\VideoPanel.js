import React, { useState, useEffect } from 'react';
import { Button, Dropdown } from 'antd';
import GetPlayer from '@/pages/DJI/DevicePage/PlayerPanel';
import { isEmpty } from '@/utils/utils';
import { JCStart, FJStart3, FJStart4 } from '@/pages/DJI/DRCPage/Panels/RtmpChange';
import styles from './VideoPanel.less';
import { useModel } from 'umi';
// 直接引入原有组件
import RtmpButton from '@/pages/DJI/DevicePage/RtmpButton';
// 引入地图和三维场景组件
import DJBaseMap from '@/pages/Maps/DJBaseMap';
import Map3D from '@/pages/Cesium';
import { CloseOutlined } from '@ant-design/icons';
import { isIndividual } from '@/utils/helper';

const VideoPanel = ({ device, visible, onClose }) => {
  const [videoSource, setVideoSource] = useState('机场镜头');
  const [playerComponent, setPlayerComponent] = useState(null);
  const [showPictureInPicture, setShowPictureInPicture] = useState(true);
  const [pipComponent, setPipComponent] = useState(null);
  const [isDanBin, setIsDanBin] = useState(false); // 是否为单兵设备
  const [selectedPipSource, setSelectedPipSource] = useState('飞机镜头'); // 当前选中的画中画镜头类型
  const { pData } = useModel('pageModel', state => ({
    pData: state.pData,
  }));

  useEffect(() => {
    if (device && visible) {
      // 设备信息存储到localStorage，供GetPlayer函数使用
      localStorage.setItem('device', JSON.stringify(device));

      // 判断是否为单兵设备
      setIsDanBin(device.SN && device.SN.startsWith('5'));

      // 开始直播
      if (!isEmpty(device)) {
        if (isDanBin) {
          // 如果是单兵，默认显示飞机镜头
          FJStart4(device);
          updatePipComponent('飞机镜头');
        } else {
          JCStart(device);
          // 如果设备状态为作业中，则显示飞机镜头
          if(device?.OsdData?.mode_code == 4){
            updatePipComponent('飞机镜头');
          }
          else{
            updatePipComponent('机场镜头');
          }
        }
      }
    }

    return () => {
      // 组件卸载时可以在这里添加清理代码
    };
  }, [device, visible, isDanBin]);

  // 直接使用DevicePage中的getPanel逻辑
  const getPanel = (tt, ww1, hh1) => {
    // 从localStorage获取最新的设备信息
    const currentDevice = JSON.parse(localStorage.getItem('device'));
    if (!currentDevice) return null;

    if (tt === '机场镜头') {
      return GetPlayer(ww1, hh1, 3, currentDevice.SN);
    }

    if (tt === '飞机镜头') {
      startLive();
      if (pData.current.ifAI) {
        return GetPlayer('100%', hh1, 12, currentDevice.SN2 + 'ai');
      }
      return GetPlayer(ww1, hh1, 12, currentDevice.SN2);
    }

    if (tt === '飞行地图') {
      if (hh1 === '120px') {
        return <DJBaseMap showBZBtn={false} device={currentDevice} sn={currentDevice.SN} h1={hh1} isDrc={true} />;
      }
      return <DJBaseMap showBZBtn={true} device={currentDevice} sn={currentDevice.SN} h1={hh1} />;
    }

    if (tt === '三维场景') {
      return <Map3D h1={hh1} />;
    }

    return null;
  };

  const updateVideoComponent = sourceType => {
    if (!device) return;

    const player = getPanel(sourceType, '100%', '420px');
    setPlayerComponent(player);
    setVideoSource(sourceType);
  };

  const updatePipComponent = sourceType => {
    if (!device) return;

    const player = getPanel(sourceType, '100%', '420px');
    setPipComponent(player);
    setSelectedPipSource(sourceType);
  };

  const startLive = () => {
    if (!device) return;

    if (isDanBin) {
      FJStart4(device);
    } else {
      FJStart3(device, device.SN2);
    }
  };

  // 画中画切换事件处理
  const onClickCHM = e => {
    const tt = e.target.innerText;
    startLive();
    pData.current.pNM2 = tt;
    updatePipComponent(tt);
  };
  // 按钮组
  const items = (handleClick, device) => {
    const list = [];
    list.push({
      key: '1',
      label: (
        <a 
          onClick={handleClick}
          style={{
            color: selectedPipSource === '飞机镜头' ? '#1890ff' : 'inherit',
            backgroundColor: selectedPipSource === '飞机镜头' ? 'rgba(24, 144, 255, 0.1)' : 'transparent',
            padding: '1px 4px',
            borderRadius: '2px',
            display: 'block'
          }}
        >
          飞机镜头
        </a>
      ),
    });
    if (!isIndividual(device)) {
      list.push({
        key: '2',
        label: (
          <a 
            onClick={handleClick}
            style={{
              color: selectedPipSource === '机场镜头' ? '#1890ff' : 'inherit',
              backgroundColor: selectedPipSource === '机场镜头' ? 'rgba(24, 144, 255, 0.1)' : 'transparent',
              padding: '1px 4px',
              borderRadius: '2px',
              display: 'block'
            }}
          >
            机场镜头
          </a>
        ),
      });
    }
    return list;
  };
  const PageButton = (title, handleClick) => {
    let device = JSON.parse(localStorage.getItem('device'));
    return (
      <Dropdown
        menu={{
          items: items(handleClick, device),
        }}
        placement="bottomRight"
        arrow
        style={{
          zIndex: 1005,
        }}
      >
        <Button type="text">{title}</Button>
      </Dropdown>
    );
  };

  // 控制面板部分 - 直接使用原有组件和处理逻辑
  const btnPanel = (
    <div className={styles.btnPanel}>
      {!isDanBin && PageButton('镜头切换', onClickCHM)}
      {!isDanBin && <RtmpButton sn={device?.SN} />}
      {/* {PageButton('主画面', onClickZHM, !isDanBin)} */}
      {/* {!isDanBin && (
        <Button type="text" onClick={onClickYY}>
          {showPictureInPicture ? '隐藏' : '显示'}
        </Button>
      )} */}
    </div>
  );

  if (!visible) return null;

  return (
    <div className={styles.videoPanel}>
      <div className={styles.videoHeader}>
        <span className={styles.title}>设备监控</span>
        <span type="text" onClick={onClose} style={{ fontSize: '16px', cursor: 'pointer' }}>
          <CloseOutlined />
        </span>
      </div>

      {btnPanel}

      {/* <div className={styles.videoContainer}>
        {playerComponent}
      </div> */}

      {!isDanBin && showPictureInPicture && <div className={styles.pipContainer}>{pipComponent}</div>}
    </div>
  );
};

export default VideoPanel;
