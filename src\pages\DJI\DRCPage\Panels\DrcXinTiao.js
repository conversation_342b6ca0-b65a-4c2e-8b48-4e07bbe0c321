import dayjs from "dayjs";
import { useEffect, useRef } from "react";

const DRCXinTiao=({device,DoCMD2})=>{
    const seq = useRef(0);
    const sendXT=()=>{
        const data = {
            "data": {
                "seq": seq.current,
                "timestamp": dayjs().valueOf()
            },
            "method": "heart_beat",
            "seq": 1
          }
      
          DoCMD2(`thing/product/${device.SN}/drc/down`, data);
          seq.current = seq.current + 1;
    }

    useEffect(()=>{
        setInterval(sendXT, 1000);
    },[])
    
      
    return <div></div>
}

export default DRCXinTiao;