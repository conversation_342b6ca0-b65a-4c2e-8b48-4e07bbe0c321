import { useState, useEffect, useRef, useMemo, useLayoutEffect } from 'react';
import { Cesium } from "umi";
import { Cartesian3_TO_Position, rotateCamera, rotateCameraHeading } from '@/utils/cesium_help';
import { Checkbox } from 'antd';
import mapImg from "@/assets/img/地图.png"
import gdlw from "@/assets/img/高德路网.png"
import arcgisDT from "@/assets/img/arcgis地图.png"
import compass from "@/assets/icon/compass.svg"
import compass_bg from "@/assets/icon/compass_bg.svg"
import styles from "./mapControl.less";
import AmapMercatorTilingScheme from '@/utils/cesiumtools/AmapMercatorTilingScheme'

const MapControl = ({ viewerData }) => {
    // 大地图viewer
    let viewer = useRef(null)
    const [heading, setHeading] = useState(0)
    const [pitch, setPitch] = useState(0)
    const [show, setShow] = useState(false)
    // 朝向转文字
    function directionToString(num) {
        if (0 <= num && num <= 22) {
            return 'N'
        } else if (22 < num && num <= 68) {
            return 'NE'
        } else if (68 < num && num <= 112) {
            return 'E'
        } else if (112 < num && num <= 158) {
            return 'SE'
        } else if (158 < num && num <= 202) {
            return 'S'
        } else if (158 < num && num <= 248) {
            return 'SW'
        } else if (248 < num && num <= 293) {
            return 'W'
        } else if (293 < num && num <= 337) {
            return 'WN'
        } else if (337 < num && num <= 360) {
            return 'N'
        }
    }
    // 恢复朝向
    function recoverHeading() {
        rotateCameraHeading(viewer.current, 0)
    }
    // 调整视角
    function angleOfViewChange(now) {
        if (now === '2D') {
            // 切到二维地图，如果设置到-90度，视角会自动跳转到正北，所以只设置到-88度
            rotateCamera(viewer.current, -90);
        } else {
            // 切到三维地图
            rotateCamera(viewer.current, -30);
        }
    }
    // 地图缩放
    function heightChange(direction) {
        //获取屏幕中心视点坐标
        var position = viewer.current.scene.globe.pick(viewer.current.camera.getPickRay(new Cesium.Cartesian2(
            viewer.current.canvas.clientWidth / 2,
            viewer.current.canvas.clientHeight / 2,
        )), viewer.current.scene);
        // 获取相机和视点距离
        var distance = Cesium.Cartesian3.distance(position, viewer.current.scene.camera.positionWC);
        viewer.current.camera.lookAt(position, new Cesium.HeadingPitchRange(viewer.current.scene.camera.heading, viewer.current.scene.camera.pitch, direction === '+' ? ((distance / 2)) :  (distance * 2)));
        //解除目标锁定
        viewer.current.camera.lookAtTransform(Cesium.Matrix4.IDENTITY)
    }
    // 页面载入
    useEffect(() => {
        if (viewerData) {
            viewer.current = viewerData
            viewer.current.scene.postRender.addEventListener((event) => {
                let newHeading = Cesium.Math.toDegrees(viewer.current.scene.camera.heading).toFixed(0)
                setPitch(Cesium.Math.toDegrees(viewer.current.scene.camera.pitch))
                setHeading(newHeading === '360' ? 0 : newHeading)
            })
            viewer.current.imageryLayers.addImageryProvider(new Cesium.WebMapTileServiceImageryProvider({
                url: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer',
                layer: 'world-imagery',
                style: 'default',
                format: 'image/jpeg',
                tileMatrixSetID: 'GoogleMapsCompatible',
                maximumLevel: 19,
            }));
            viewer.current.imageryLayers._layers[viewer.current.imageryLayers._layers.length - 1].show = false
            viewer.current.imageryLayers.addImageryProvider(new Cesium.UrlTemplateImageryProvider({
                url: "http://webst02.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scale=1&style=8",
                minimumLevel: 0,
                maximumLevel: 18,
                tilingScheme: new AmapMercatorTilingScheme(),
            }))
            // viewer.current.imageryLayers._layers[viewer.current.imageryLayers._layers.length - 1].show = false
        }
    }, [viewerData]);
    // 地图切换
    function CheckboxChange(e, name, index) {
        viewer.current.imageryLayers._layers[index].show = e.target.checked
    }
    return <div style={{ width: 30, color: '#595959', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }} >
        <div style={{ cursor: 'pointer', boxSizing: 'border-box', borderRadius: '50%', height: 56, width: 56, marginBottom: 10, display: 'flex', justifyContent: 'center', alignItems: 'center', position: 'relative' }} onClick={recoverHeading}>
            <img src={compass_bg} style={{ width: 56, height: 56, transform: `rotate(${-heading}deg)` }} />
            <img className={styles.compass} src={compass} style={{ width: 40, height: 43, borderRadius: '50%', position: 'absolute', left: '50%', top: '50%' }} />
            <div className={styles.compass} style={{ position: 'absolute', left: '50%', top: '40%' }}>{directionToString(Number(heading))}</div>
            <div className={styles.compass} style={{ position: 'absolute', left: '50%', top: '70%', fontSize: 11 }}>{heading < 10 ? ('00' + heading) : heading < 100 ? ('0' + heading) : heading}°</div>
        </div>
        <div style={{ cursor: 'pointer', borderRadius: 3, height: 30, width: 30, backgroundColor: '#ffffff', marginBottom: 10, display: 'flex', justifyContent: 'center', alignItems: 'center', fontSize: 18, fontWeight: 600 }} onClick={() => angleOfViewChange((pitch < -75) ? '3D' : '2D')}>{(pitch < -75) ? '3D' : '2D'}</div>
        <div style={{ cursor: 'pointer', boxSizing: 'border-box', borderBottom: '1px solid #dcdee2', borderRadius: '3px 3px 0 0', height: 30, width: 30, backgroundColor: '#ffffff', display: 'flex', justifyContent: 'center', alignItems: 'center', fontSize: 25, fontWeight: 800 }} onClick={() => heightChange('+')}>＋</div>
        <div style={{ cursor: 'pointer', borderRadius: '0 0 3px 3px', height: 30, width: 30, backgroundColor: '#ffffff', marginBottom: 10, display: 'flex', justifyContent: 'center', alignItems: 'center', fontSize: 25, fontWeight: 800 }} onClick={() => heightChange('-')}>－</div>
        <div style={{ cursor: 'pointer', boxSizing: 'border-box', borderRadius: '50%', border: '3px solid #ffffff', height: 40, width: 40, display: 'flex', justifyContent: 'center', alignItems: 'center' }} onClick={() => { setShow(!show) }}>
            <img src={mapImg} style={{ width: '100%', height: '100%', borderRadius: '50%' }} />
        </div>
        {show && <div style={{ width: 300, backgroundColor: '#ffffff', position: 'absolute', right: 50, bottom: 0, borderRadius: 5, padding: 5 }}>
            <div style={{ width: '100%', height: '60%', }}>
                <div style={{ width: '100%', height: '20%', display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }}>地图底图</div>
                <div style={{ width: '100%', height: '80%', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div style={{ width: '48%', height: 80, position: 'relative' }}>
                        <img src={gdlw} style={{ width: '100%', height: '100%' }} />
                        <div style={{ position: 'absolute', left: 0, top: 0, width: '100%', height: 25, backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
                            <Checkbox style={{ color: '#ffffff' }} defaultChecked={true} onChange={(e) => CheckboxChange(e, '高德路网', 2)}>高德路网</Checkbox>
                        </div>
                    </div>
                    <div style={{ width: '48%', height: 80, position: 'relative' }}>
                        <img src={arcgisDT} style={{ width: '100%', height: '100%' }} />
                        <div style={{ position: 'absolute', left: 0, top: 0, width: '100%', height: 25, backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
                            <Checkbox style={{ color: '#ffffff' }} defaultChecked={false} onChange={(e) => CheckboxChange(e, 'ArcGIS地图', 1)}>ArcGIS地图</Checkbox>
                        </div>
                    </div>
                </div>

            </div>

        </div>}
    </div>
};

export default MapControl;