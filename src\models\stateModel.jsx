
import { isEmpty } from "@/utils/utils";
import { GetMqttClient } from "@/utils/websocket";
import { useRef, useState } from "react";
import { useModel } from "umi";
export default function stateModel() {
   const [sdata,setSData]=useState({})
   const mqttC=useRef({})
   const [fjVideo,setFjVideo]=useState({})
   const modeReason=useRef(0)

   const StateMqttConn = (sn,sn2) => {
      if (!isEmpty(mqttC.current)) {
         mqttC.current.end();
       }
   
     mqttC.current = GetMqttClient()
     mqttC.current.on("message", (topic, message) => {
         updateVal(topic,message);
    });
      mqttC.current.unsubscribe('#')
       mqttC.current.subscribe("thing/product/"+sn+"/state");
       mqttC.current.subscribe("thing/product/"+sn2+"/state");
      
   }

   const updateCamera=async(xx)=>{
        localStorage.setItem('device',JSON.stringify(device));
        let device=JSON.parse( localStorage.getItem('device'))
        if(device.Camera==""||device.Camera2==""){
        
         
           device.Camera1=xx.data.live_capacity.device_list[0].camera_list[0].camera_index;
           device.Camera2=xx.data.live_capacity.device_list[1].camera_list[0].camera_index;
            console.log('live_capacity',device)
            await Post2('/api/v1/Device/Update', device);
        }
      
   }
   const updateVal=(t1,m1)=>{
       const xx=JSON.parse( m1);
       console.log('state', xx);
       
       if(m1.includes('firmware')){
          console.log('state', xx);
       }
       if(m1.includes('mode_code_reason')){
         console.log('mode_code_reason', xx);
         modeReason.current=xx.data['mode_code_reason']
         localStorage.setItem('mode_code_reason',xx)
       }
       if (!isEmpty( xx.data.live_status)) {
         console.log('live_status',xx.data["live_status"]);
        // 
        xx.data["live_status"].forEach(e => {
           if( e['video_id'].length>35){
               setFjVideo(e);
           }
         });
      }
   }
    return {sdata,setSData,modeReason,fjVideo, StateMqttConn};
};