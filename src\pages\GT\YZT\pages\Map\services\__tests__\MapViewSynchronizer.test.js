/**
 * MapViewSynchronizer 测试文件
 * 验证增强后的事件转发功能
 */

import MapViewSynchronizer from '../MapViewSynchronizer';

describe('MapViewSynchronizer 事件转发功能测试', () => {
  let synchronizer;
  let mockMapInstance1, mockMapInstance2;
  let mockCallbacks1, mockCallbacks2;

  beforeEach(() => {
    // 创建新的同步器实例
    synchronizer = new MapViewSynchronizer();
    synchronizer.setEnabled(true);
    synchronizer.setDebugMode(true);

    // 模拟地图实例
    mockMapInstance1 = { id: 'map1' };
    mockMapInstance2 = { id: 'map2' };

    // 模拟回调函数
    mockCallbacks1 = {
      onViewChange: jest.fn(),
      onMapEvent: jest.fn(),
      onError: jest.fn()
    };

    mockCallbacks2 = {
      onViewChange: jest.fn(),
      onMapEvent: jest.fn(),
      onError: jest.fn()
    };
  });

  afterEach(() => {
    synchronizer.reset();
  });

  describe('订阅功能增强测试', () => {
    test('应该支持仅有 onMapEvent 回调的订阅', () => {
      const result = synchronizer.subscribe('map1', mockMapInstance1, {
        onMapEvent: mockCallbacks1.onMapEvent
      });

      expect(result).toBe(true);
      expect(synchronizer.hasObserver('map1')).toBe(true);
    });

    test('应该支持仅有 onViewChange 回调的订阅', () => {
      const result = synchronizer.subscribe('map1', mockMapInstance1, {
        onViewChange: mockCallbacks1.onViewChange
      });

      expect(result).toBe(true);
      expect(synchronizer.hasObserver('map1')).toBe(true);
    });

    test('应该支持同时有两种回调的订阅', () => {
      const result = synchronizer.subscribe('map1', mockMapInstance1, {
        onViewChange: mockCallbacks1.onViewChange,
        onMapEvent: mockCallbacks1.onMapEvent
      });

      expect(result).toBe(true);
      expect(synchronizer.hasObserver('map1')).toBe(true);
    });

    test('应该拒绝没有任何回调的订阅', () => {
      const result = synchronizer.subscribe('map1', mockMapInstance1, {});

      expect(result).toBe(false);
      expect(synchronizer.hasObserver('map1')).toBe(false);
    });
  });

  describe('事件数据验证测试', () => {
    test('应该验证 pan 事件数据', () => {
      const validPanEvent = {
        type: 'pan',
        payload: { dx: 10, dy: 20, screenPoint: { x: 100, y: 200 } }
      };

      const invalidPanEvent = {
        type: 'pan',
        payload: { dx: 'invalid' }
      };

      expect(synchronizer.isValidEventData(validPanEvent)).toBe(true);
      expect(synchronizer.isValidEventData(invalidPanEvent)).toBe(false);
    });

    test('应该验证 zoom 事件数据', () => {
      const validZoomEvent = {
        type: 'zoom',
        payload: { delta: 1, anchorPoint: { x: 100, y: 200 } }
      };

      const invalidZoomEvent = {
        type: 'zoom',
        payload: { delta: 'invalid' }
      };

      expect(synchronizer.isValidEventData(validZoomEvent)).toBe(true);
      expect(synchronizer.isValidEventData(invalidZoomEvent)).toBe(false);
    });

    test('应该验证鼠标事件数据', () => {
      const validMouseEvent = {
        type: 'mousemove',
        payload: { screenPoint: { x: 100, y: 200 } }
      };

      const invalidMouseEvent = {
        type: 'mousemove',
        payload: { screenPoint: { x: 'invalid' } }
      };

      expect(synchronizer.isValidEventData(validMouseEvent)).toBe(true);
      expect(synchronizer.isValidEventData(invalidMouseEvent)).toBe(false);
    });

    test('应该拒绝无效的事件类型', () => {
      const invalidTypeEvent = {
        type: 'invalid_type',
        payload: { data: 'test' }
      };

      expect(synchronizer.isValidEventData(invalidTypeEvent)).toBe(false);
    });
  });

  describe('事件转发功能测试', () => {
    beforeEach(() => {
      // 订阅两个地图实例
      synchronizer.subscribe('map1', mockMapInstance1, mockCallbacks1);
      synchronizer.subscribe('map2', mockMapInstance2, mockCallbacks2);
    });

    test('应该成功转发 pan 事件', () => {
      const panEvent = {
        type: 'pan',
        payload: { dx: 10, dy: 20, screenPoint: { x: 100, y: 200 } }
      };

      const result = synchronizer.notifyMapEvent('map1', panEvent);

      expect(result).toBe(true);
      expect(mockCallbacks1.onMapEvent).not.toHaveBeenCalled(); // 源地图不应收到事件
      expect(mockCallbacks2.onMapEvent).toHaveBeenCalledWith(panEvent);
    });

    test('应该成功转发 zoom 事件', () => {
      const zoomEvent = {
        type: 'zoom',
        payload: { delta: 1, anchorPoint: { x: 100, y: 200 } }
      };

      const result = synchronizer.notifyMapEvent('map1', zoomEvent);

      expect(result).toBe(true);
      expect(mockCallbacks2.onMapEvent).toHaveBeenCalledWith(zoomEvent);
    });

    test('应该成功转发鼠标事件', () => {
      const mouseEvent = {
        type: 'mousemove',
        payload: { screenPoint: { x: 150, y: 250 } }
      };

      const result = synchronizer.notifyMapEvent('map1', mouseEvent);

      expect(result).toBe(true);
      expect(mockCallbacks2.onMapEvent).toHaveBeenCalledWith(mouseEvent);
    });

    test('应该拒绝无效的事件数据', () => {
      const invalidEvent = {
        type: 'invalid',
        payload: {}
      };

      const result = synchronizer.notifyMapEvent('map1', invalidEvent);

      expect(result).toBe(false);
      expect(mockCallbacks2.onMapEvent).not.toHaveBeenCalled();
    });

    test('同步禁用时应该跳过事件转发', () => {
      synchronizer.setEnabled(false);

      const panEvent = {
        type: 'pan',
        payload: { dx: 10, dy: 20 }
      };

      const result = synchronizer.notifyMapEvent('map1', panEvent);

      expect(result).toBe(false);
      expect(mockCallbacks2.onMapEvent).not.toHaveBeenCalled();
    });
  });

  describe('错误处理测试', () => {
    test('应该处理回调函数中的错误', () => {
      // 设置一个会抛出错误的回调
      const errorCallback = jest.fn().mockImplementation(() => {
        throw new Error('测试错误');
      });

      synchronizer.subscribe('map1', mockMapInstance1, mockCallbacks1);
      synchronizer.subscribe('map2', mockMapInstance2, {
        onMapEvent: errorCallback,
        onError: mockCallbacks2.onError
      });

      const panEvent = {
        type: 'pan',
        payload: { dx: 10, dy: 20 }
      };

      const result = synchronizer.notifyMapEvent('map1', panEvent);

      expect(result).toBe(false); // 因为有错误，所以返回 false
      expect(mockCallbacks2.onError).toHaveBeenCalled();
    });
  });

  describe('统计信息测试', () => {
    test('应该正确更新统计信息', () => {
      synchronizer.subscribe('map1', mockMapInstance1, mockCallbacks1);
      synchronizer.subscribe('map2', mockMapInstance2, mockCallbacks2);

      const panEvent = {
        type: 'pan',
        payload: { dx: 10, dy: 20 }
      };

      synchronizer.notifyMapEvent('map1', panEvent);

      const stats = synchronizer.getStats();
      expect(stats.totalSyncs).toBe(1);
      expect(stats.successfulSyncs).toBe(1);
      expect(stats.lastSyncTime).toBeInstanceOf(Date);
    });
  });
});
