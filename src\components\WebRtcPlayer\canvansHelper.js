const canvasHelper = {
  // 画线段
  drawLine(ctx, x1, y1, x2, y2, color, width) {
    ctx.beginPath();
    ctx.lineWidth = width;
    ctx.strokeStyle = color;
    ctx.moveTo(x1, y1);
    ctx.lineTo(x2, y2);
    ctx.stroke();
  },

  // 画数组线段
  drawLine2(ctx,pointList,lineWidth,strokeStyle){
    if(pointList.length < 2) return; //至少需要两点才能连线
    ctx.beginPath();
    ctx.lineWidth = lineWidth;  // 线条宽度
    ctx.strokeStyle = strokeStyle; // 线条颜色

    ctx.moveTo(pointList[0].x, pointList[0].y); // 起点
    for(let i = 1; i < pointList.length; i++){
      ctx.lineTo(pointList[i].x, pointList[i].y); // 路径
    }
    ctx.stroke(); // 画线
  },

  // 画带标注的点
  drawPointWithRemarks(ctx, x, y,color,text){
    this.drawPoint(ctx, x, y,color);
    this.drawRemarks(ctx,x,y,color,text);
  },

  // 画实心圆 // 画点
  drawPoint(ctx, x, y,color,radius) {
    // ctx.strokeStyle = color;                      //设置描边画笔颜色
    ctx.fillStyle = color;                        //设置填充画笔颜色
    ctx.beginPath();                              //开始绘制
    ctx.arc(x,y,radius,0,2*Math.PI,false);              //绘制圆弧
    ctx.closePath();                              //结束绘制
    // ctx.stroke();                                 //描边
    ctx.fill();                                   //填充
  },

  // 画标注
  drawRemarks(ctx,x,y,color,text){
    ctx.fillStyle = color;
    ctx.beginPath();
    ctx.lineWidth = 3;
    ctx.strokeStyle = color;
    ctx.moveTo(x,y);
    ctx.lineTo(x+20,y-40)
    ctx.lineTo(x+80,y-40)
    ctx.stroke();
    ctx.strokeRect(x+80,y-55,200,100);                         // 画边框
    ctx.clearRect(x+80,y-55,200,100);                          // 清除内里的线条 
    this.drawText(ctx,x+180,y-5,"red",text,20)
  },

  // 画文字
  drawText(ctx, x, y, color, text, fontSize) {
    ctx.font = `${fontSize}px Arial`;
    ctx.fillStyle = color;
    ctx.textBaseline = 'middle'; // 垂直居中
    ctx.textAlign = "center";    // 水平居中
    ctx.fillText(text,x,y);
  },

  // 画多边形
  drawPolygon(ctx,pointList,strokeStyle,lineWidth){
    if(pointList.length < 2) return; //至少需要两点才能连线
    ctx.beginPath();
    ctx.lineWidth = lineWidth;  // 线条宽度
    ctx.strokeStyle = strokeStyle; // 线条颜色

    ctx.moveTo(pointList[0].x, pointList[0].y); // 起点
    for(let i = 1; i < pointList.length; i++){
      ctx.lineTo(pointList[i].x, pointList[i].y); // 路径
    }
    ctx.closePath(); // 闭合路径
    ctx.stroke(); // 画线
  },

  // 通过视场角计算相机看到的地面范围 用相机的像素去除地面距离 得到像素/米
  calculatePixelPerMeter(fj,canvas){
    const fovRad = fj.data.cameras[0].liveview_world_region.bottom *  180; // 视场角弧度
    const groundDistance = 2 * fj.data.height * Math.tan(fovRad / 2);   // 计算地面范围
    return (3956 / groundDistance) * (canvas.height / 3956); // 计算像素/米
  },

  // 计算视角变化后的像素位移
  calculatePixelMovement(fj, canvas) {
    const ppm = this.calculatePixelPerMeter(fj, canvas);
    const speed = fj.data.horizontal_speed;
    const yaw_rad = fj.data.gimbal_yaw * Math.PI / 180; // 角度转弧度

    // 分解到画布坐标系
    return {
      dx: speed * ppm * Math.cos(yaw_rad), // X轴像素位移
      dy: -speed * ppm * Math.sin(yaw_rad) // Y轴像素位移（取反适应屏幕坐标系）
    };
  },

  // 返回缩放后的坐标数组
  scaleCoordinates(points, canvas) {

    const cameraWidth = 5280;
    const cameraHeight = 3956;

    const canvasAspect = canvas.width / canvas.height;
    const originalAspect = cameraWidth / cameraHeight;
    let scale, offsetX = 0, offsetY = 0;
  
    // 判断是否宽度方向需要裁剪
    if (originalAspect > canvasAspect) {
      // 高度撑满，宽度方向有裁剪
      scale = canvas.height / cameraHeight;
      const scaledWidth = cameraWidth * scale;
      offsetX = (canvas.width - scaledWidth) / 2;
    } else {
      // 宽度撑满，高度方向有裁剪
      scale = canvas.width / cameraWidth;
      const scaledHeight = cameraHeight * scale;
      offsetY = (canvas.height - scaledHeight) / 2;
    }
  
    return points.map(point => ({
      x: point[0] * scale + offsetX,
      y: point[1] * scale + offsetY
    }));
  },

  // 画图函数 传入渲染数据
  draw(pointList,canvas){
    if(pointList === null){
      return;
    }else{
      const ctx = canvas.getContext('2d');
      ctx.clearRect(0, 0, ctx.width, ctx.height); // 清空画布

      // 画点
      pointList.Point.forEach(point => {
        // 缩放坐标
        const scaled = this.scaleCoordinates([point.pixels_point], canvas);
        this.drawPoint(ctx, scaled[0].x, scaled[0].y, point.fillStyle, point.radius);
      });

      // 画线
      pointList.LineString.forEach(line => {
        // 缩放坐标
        const scaledPoints = this.sclaeCoordinates(line.pixels_point,canvas)
        this.drawLine2(ctx,scaledPoints,line.lineWidth,line.strokeStyle)
      });

      // 画多边形
      pointList.Polygon.forEach(polygon => {
        // 缩放坐标
        const scaledPoints = this.sclaeCoordinates(polygon.pixels_point,canvas)
        this.drawPolygon(ctx,scaledPoints,polygon.strokeStyle,polygon.lineWidth)
      });

    }
    
  }
};

export default canvasHelper;
