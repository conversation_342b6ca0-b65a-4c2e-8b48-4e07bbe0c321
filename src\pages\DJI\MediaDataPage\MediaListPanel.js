import { Clone, downloadFile2, downloadFile, getBodyH, isEmpty, getImgUrl, getImgSLTUrl } from '@/utils/utils';

import { useEffect, useRef, useState } from 'react';
import { Card, Select, List, Checkbox, DatePicker} from 'antd';

import { useModel,history } from 'umi';
import MediaInfoPanel from './MediaInfoPanel';

import LastPageButton from '@/components/LastPageButton';
import { timeFormat22 } from '@/utils/helper';
import dayjs from 'dayjs';
import DownLoadAndDel from '@/pages/DJI/MediaDataPage/DownLoadAndDel'


const { RangePicker } = DatePicker;

const getImgUrl2 = (obj) => {
  return `/${obj}`
}

const MediaListPanel = ({mList,refrush,doNotShowLastButton}) => {

  let para1={
  }
  const localPara=localStorage.getItem('PageParams');
  if(!isEmpty(localPara)){
    para1=JSON.parse( localPara);
  }

  //console.log('MediaListPanel', mList)
  const [sList, setSList] = useState([])
  const { setPage, lastPage, setModal, open, setOpen } = useModel("pageModel")
  const [params,setParams]=useState(para1)



  
  // const [sWay, setSWay] = useState('');
  // const [sDate, setSDate] = useState({});
  // const [sHangD, setHangD] = useState(-1);
  // const [sType, setSTpye] = useState(-1);

  const [xList, setXList] = useState([]);


  // eslint-disable-next-line react-hooks/exhaustive-deps

  // if (isEmpty(page1)) {
  //   page1 = 1
  // } else {
  //   page1 = Number(page1)
  // }
  // const IfSelected = (item) => {
  //   const xx = sList.indexOf(item.ID)
  //   return xx > -1;
  // }


  const ifPush = (e) => {
    let t1 = dayjs('1900/1/1');
    let t2 = dayjs('2900/1/1')
    if (!isEmpty(params.sDate)) {
      t1 = dayjs(params.sDate[0]);
      t2 = dayjs(params.sDate[1]);
    }

    if (!isEmpty(params.sWay)) {
      if (e.WayLineNM != params.sWay) {
        return false;
      }
    }
    if (!isEmpty(params.sDate)) {
      const t3 = dayjs(e.CreatedTime);
      if (t1.isAfter(t3) || (t2.isBefore(t3))) {
        return false;
      }
    }
    if (params.sHangD > -1) {
      if (e.HangDianIndex != params.sHangD) {
        return false;
      }
    }
    if (params.sType > -1) {
      if (e.MediaType != params.sType) {
        return false;
      }
    }
    return true;
  }

  const getXL=(data)=>{
    const xL = []
    data.forEach(e => {
      if (ifPush(e)) {
        xL.push(e);
      }
    });
    return xL;
  }

  const xx = () => {
    const xL=getXL(mList);
    setXList([...xL]);
  }
  useEffect(() => {
    xx();
    localStorage.setItem('PageParams',JSON.stringify(params));
   
  }, [params,refrush]);


  const downLoadClick = () => {

    mList.forEach(e => {
      if (IfSelected(e)) {
        downloadFile(getImgUrl2(e.ObjectName), e.FileName)
        // downloadFile(getImgUrl(e.ObjectName));
      }
    });
  }

  const selectClick = (item) => {
    const xx = sList.indexOf(item.ID)
    if (xx === -1) {
      sList.push(item.ID)

    } else {
      delete sList[xx]
    }
    const yy = Clone(sList)
    setSList(yy)
  }

  const [checkBoxList,setCheckBoxList] = useState([])
  const checkBoxChange = (e,item) => {
   if(!e){
      setCheckBoxList([])
    }
    if(e && e.target.checked && checkBoxList && !checkBoxList.includes(item.ID)){
      setCheckBoxList([...checkBoxList,item])
    } 
    if(e && !e.target.checked){
      setCheckBoxList(checkBoxList.filter((value)=>{
        return value.ID !== item.ID
      }))
    }
  };


  
  
  const getItem = (item) => {
    const onClick = (item) => {
      if(doNotShowLastButton){
        history.push("/gt/WRJ");
        // 显示返回按钮
        localStorage.setItem('showBackButton', 'true');
      }
      setPage(<MediaInfoPanel key={item.ID} mList={mList}  record={item} lastPage={lastPage} />)
    }
    if(item.SLTImg.length<5){
      item.SLTImg=item.ObjectName;
  }
    return <div  style={{ cursor: 'pointer', }} key={item}>
      <img style={{ width: '100%', cursor: 'pointer', height: '100%', borderRadius: 5.0 }}
          src={getImgUrl(item.SLTImg)}
          onClick={() => {onClick(item);}}/>
      <div style={{ margin: 4.0, cursor: 'pointer', fontWeight: 'bold', height: 36.0,}}>
        <span><Checkbox key={item.ID} onChange={(e)=>checkBoxChange(e,item)} style={{margin:'0 15px 0 -5px'}}></Checkbox></span>
        <span>{item.WayLineNM + "-航点" + item.HangDianIndex}</span>
      </div>
      <div style={{ position: 'absolute', cursor: 'pointer', top: 2, bottom: 40, right: 12, fontWeight: 'bold', color: 'white' }}>{timeFormat22(item.CreatedTime)}</div>
    </div>
  }


  const getWaySelect = (wayList, getLabel) => {

    const list = []
    wayList.forEach(e => {
      list.push(<Select.Option key={e} data={e}>{getLabel(e)}</Select.Option>)
    });
    return list;
  }


  const getMediaType = (e) => {
    if (e == 2) return '广角照片';
    if (e == 3) return '红外照片';
    if (e == 4) return '变焦照片';
    if (e == 5) return '可见光';
    return 'xx'
  }
  const getExr = () => {
    const wList = []
    const dList = []
    const tList = []

    mList.forEach(e => {
      if (!wList.includes(e.WayLineNM)) {
        wList.push(e.WayLineNM);
      }
      if (!tList.includes(e.MediaType)) {
        tList.push(e.MediaType);
      }
      if (!isEmpty(params.sWay)) {
      if (!dList.includes(e.HangDianIndex)&&e.WayLineNM == params.sWay) {
          dList.push(e.HangDianIndex);
      }}else{
        if (!dList.includes(e.HangDianIndex)) {
          dList.push(e.HangDianIndex);
      }
      }
    });


    return <div>

      <span style={{ marginLeft: 6.0 }}>  <Select defaultValue={params.sWay}  allowClear={true} style={{ width: 200 }} onClear={() =>setParams({...params,sWay:''})  }
        placeholder={'选择飞行航线'}

        onSelect={(e)=>setParams({...params,sWay:e})}>
        {getWaySelect(wList, (e) => e)}
      </Select></span>

      <span style={{ marginLeft: 6.0 }}>      <Select  defaultValue={params.sHangD} allowClear={true} style={{ width: 200 }} onClear={() => setParams({...params,sHangD:-1}) }
        placeholder={'选择拍摄航点'}
        onSelect={(e)=>setParams({...params,sHangD:e})}>
        {getWaySelect(dList, (e) => '航点' + e)}
      </Select></span>


      <span style={{ marginLeft: 6.0 }}>      <Select defaultValue={params.sType} allowClear={true} style={{ width: 200 }} onClear={() => setParams({...params,sType:-1})}
        placeholder={'选择照片类型'}
        onSelect={(e)=>setParams({...params,sType:e})}>
        {getWaySelect(tList, (e) => (getMediaType(e)))}
      </Select></span>



      <span style={{ marginLeft: 6.0 }}> <RangePicker onChange={(e)=>setParams({...params,sDate:e})} /></span>

      <span>
     <DownLoadAndDel selectImgList={mList} checkBoxList={checkBoxList} refrush={refrush} checkBoxChange={checkBoxChange}></DownLoadAndDel> 
      </span>

    </div>
  }


  return <Card title={ doNotShowLastButton ? '航拍图片' : <LastPageButton title="航拍照片"/> }  extra={getExr()}>
    <List
    grid={{ gutter: 15, column: 5 }}
    dataSource={xList}
    pagination={{ defaultCurrent: params.page1,
       showSizeChanger: false, 
       showQuickJumper: true,
       onChange: (e) => setParams({...params,page1:e}), 
       pageSize: 10,style:{marginTop:'-40px'} }}
    style={{ height: getBodyH(300)}}
    renderItem={item => (
      <List.Item>
        {getItem(item)}
      </List.Item>
    )}
  />
  </Card>
}

export default MediaListPanel;
