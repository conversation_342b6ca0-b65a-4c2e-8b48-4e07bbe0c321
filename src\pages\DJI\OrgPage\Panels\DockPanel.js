import BlockTitle from '@/components/BlockPanel/BlockTitle';
import './DataPanel.less';
import { useEffect, useState } from 'react';
import icon4 from '@/assets/icons/device.png';
import { BorderBox7 } from '@jiaminghi/data-view-react';
import { Badge, Progress, Row, Col, Button, Card, message, Spin, Timeline } from 'antd';
import { getGuid, isIndividual } from '@/utils/helper';
import { getBodyH, isEmpty } from '@/utils/utils';
import { axiosApi } from '@/services/general';
import styles from './dock.less';
import { useModel } from 'umi';
import DevicePage from '../../DevicePage';
import WayLineMap from '@/pages/DJI/WayLine/WayLineMap';
import { DownOutlined, UpOutlined, VideoCameraOutlined, LoadingOutlined } from '@ant-design/icons';
import LastPageButton from "@/components/LastPageButton";
import { CheckIfCanFly } from "@/pages/DJI/FlyToPage/helper";
import { HGet2 } from "@/utils/request";


const getColor = zt => {
  if (zt == '在线') return 'green';
  if (zt == '离线') return 'red';
  if (zt == '作业') return 'orange';
  if (zt == '空闲中') return '#1ABC9C';
};
const ModeCodeJson = {
  0: '空闲中',
  1: '现场调试',
  2: '远程调试',
  3: '固件升级中',
  4: '作业中',
  true: '在线',
  false: '离线',
  null: '设备未注册',
};

// 模拟数据
const mockDockData = [
  {
    ID: '001',
    DName: '机场001-智慧巡检',
    IfOnLine: true,
    OsdData: {
      mode_code: 0,
      capacity_percent: 85,
      is_flying: false,
      drone_charge_state: {
        capacity_percent: 85
      }
    },
    routes: [
      {
        id: 1,
        name: "围栏巡检航线",
        description: "每日围栏安全检查",
        startTime: "2023-05-20 08:30",
        endTime: "2023-05-20 09:15",
        status: "待执行"
      },
      {
        id: 2,
        name: "电力设备巡检",
        description: "变电站设备检查",
        startTime: "2023-05-20 10:00",
        endTime: "2023-05-20 10:45",
        status: "已计划"
      },
      {
        id: 3,
        name: "水域巡查航线",
        description: "水库安全监测",
        startTime: "2023-05-20 14:30",
        endTime: "2023-05-20 15:15",
        status: "待执行"
      }
    ]
  },
  {
    ID: '002',
    DName: '机场002-高空监测',
    IfOnLine: true,
    OsdData: {
      mode_code: 4,
      capacity_percent: 62,
      is_flying: true,
      drone_charge_state: {
        capacity_percent: 62
      }
    },
    routes: [
      {
        id: 4,
        name: "高空建筑巡检",
        description: "高楼外墙安全检查",
        startTime: "2023-05-20 09:00",
        endTime: "2023-05-20 09:45",
        status: "执行中"
      }
    ]
  },
  {
    ID: '003',
    DName: '机场003-森林防火',
    IfOnLine: true,
    OsdData: {
      mode_code: 0,
      capacity_percent: 92,
      is_flying: false,
      drone_charge_state: {
        capacity_percent: 92
      }
    },
    routes: [
      {
        id: 5,
        name: "林区巡查航线A",
        description: "森林防火监测",
        startTime: "2023-05-20 07:00",
        endTime: "2023-05-20 08:30",
        status: "待执行"
      },
      {
        id: 6,
        name: "林区巡查航线B",
        description: "野生动物监测",
        startTime: "2023-05-20 15:00",
        endTime: "2023-05-20 16:30",
        status: "待执行"
      }
    ]
  },
  {
    ID: '004',
    DName: '机场004-农田监测',
    IfOnLine: false,
    OsdData: null,
    routes: []
  },
  {
    ID: '005',
    DName: '机场005-应急救援',
    IfOnLine: true,
    OsdData: {
      mode_code: 1,
      capacity_percent: 45,
      is_flying: false,
      drone_charge_state: {
        capacity_percent: 45
      }
    },
    routes: [
      {
        id: 7,
        name: "应急搜救航线",
        description: "山区搜救任务",
        startTime: "2023-05-20 11:00",
        endTime: "2023-05-20 12:30",
        status: "待执行"
      }
    ]
  }
];
// 测试数据
const orgData = {
  "ID": 44,
  "DName": "石棉机场",
  "SN": "8UUXN3D00A02NS",
  "BindCode": "qwe",
  "OrgName": "",
  "OrgCode": "bdzl",
  "Lat": 29.383435357647933,
  "Lng": 102.22486797970919,
  "Height": 1007.1735229492188,
  "SN2": "1581F8HHD24CL0010078",
  "Location": "",
  "DName2": "石棉无人机",
  "Camera1": "165-0-7",
  "Camera2": "98-0-0",
  "Remark": "",
  "Model": "DOCK3",
  "Model2": "M4D",
  "OsdData": {
      "air_conditioner": {
          "air_conditioner_state": 1,
          "switch_time": 0
      },
      "alarm_state": 0,
      "alternate_land_point": {
          "height": 0,
          "is_configured": 1,
          "latitude": 29.3834399680616,
          "longitude": 102.22484729766343,
          "safe_land_height": 30
      },
      "battery_store_mode": 2,
      "cover_state": 0,
      "drone_charge_state": {
          "capacity_percent": 95,
          "state": 0
      },
      "drone_in_dock": 1,
      "emergency_stop_state": 0,
      "environment_temperature": 22.2,
      "first_power_on": 1631945855969,
      "height": 1007.1735229492188,
      "humidity": 90,
      "latitude": 29.383435357647933,
      "live_status": null,
      "longitude": 102.22486797970919,
      "mode_code": 0,
      "network_state": {
          "quality": 4,
          "rate": 0,
          "type": 1
      },
      "position_state": {
          "gps_number": 5,
          "is_calibration": 1,
          "is_fixed": 2,
          "quality": 5,
          "rtk_number": 35
      },
      "putter_state": 0,
      "rainfall": 0,
      "storage": {
          "total": 53082240,
          "used": 0
      },
      "sub_device": {
          "device_model_key": "0-100-0",
          "device_online_status": 0,
          "device_paired": 1,
          "device_sn": "1581F8HHD24CL0010078"
      },
      "supplement_light_state": 0,
      "temperature": 20.1,
      "wind_speed": 1.1
  },
  "IfOnLine": true
}
const DockStatePanel = ({ data, onAirportClick, onVideoClick, onShowRoute }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { setPage } = useModel('pageModel');
  // 使用模拟数据而不是传入数据
  const [deviceList, setDeviceList] = useState([]);
  // 存储展开状态的对象
  const [expandedDevices, setExpandedDevices] = useState({});
  // 存储各设备的航线数据
  const [deviceRoutesMap, setDeviceRoutesMap] = useState({});
  // 存储加载状态
  const [loadingMap, setLoadingMap] = useState({});

  useEffect(() => {
    if (data) {
      // 如果需要使用传入的真实数据，取消下面的注释
      const sortedData = [...data].sort((a, b) => b.IfOnLine - a.IfOnLine);
      setDeviceList(sortedData);
    } else {
      // 确保即使没有传入数据也使用模拟数据
      setDeviceList(mockDockData);
    }
  }, [data]);

  // 获取航线任务
  const getFlightTask = async (sn) => {
    try {
      const res = await axiosApi('/api/v1/FlightTask/GetSurveyFlightTaskByAirportSn', 'GET', {sn});
      return res?.data || [];
    }
    catch (error) {
      console.log(error);
      return [];
    }
  };

  // 加载设备的航线数据
  const loadDeviceRoutes = async (deviceId, sn) => {
    console.log('loadDeviceRoutes', deviceId, sn);
    
    if (!sn || deviceRoutesMap[deviceId]?.length > 0) return;
    
    setLoadingMap(prev => ({
      ...prev,
      [deviceId]: true
    }));
    
    try {
      const routes = await getFlightTask(sn);
      // 转换字段名称
      const formattedRoutes = routes.map(item => {
        return {
          ...item,
          SN: item.sn || sn,
          PointList: item.pointList
        };
      });
      
      setDeviceRoutesMap(prev => ({
        ...prev,
        [deviceId]: formattedRoutes
      }));
    } catch (error) {
      console.error("获取航线任务失败:", error);
    } finally {
      setLoadingMap(prev => ({
        ...prev,
        [deviceId]: false
      }));
    }
  };

  const getDCColor = dc => {
    if (dc < 30) return 'red';
    if (dc < 60) return 'orange';
    return 'green';
  };
  const getColor = d1 => {
    if (isEmpty(d1.OsdData)) return 'red';
    if (d1.OsdData.mode_code == 0) return 'green';
    return 'orange';
  };

  const getZT = d1 => {
    if (isEmpty(d1.OsdData)) return '离线';

    return ModeCodeJson[d1.OsdData.mode_code] || ModeCodeJson[d1.IfOnLine];
  };

  const onDClick = e => {
    console.log(history, 'history');
    localStorage.setItem('device', JSON.stringify(e));
    setPage(<DevicePage device={e} />);
  };

  // 让地图中心移动到点击对应的位置
  const handleAirportClick = (e, itemOrigin) => {
    e.stopPropagation();
    // 将点击事件和设备信息传递给父组件
    if (onAirportClick && itemOrigin) {
      // 如果设备有经纬度信息，传递给父组件
      if (itemOrigin.OsdData && itemOrigin.OsdData.latitude && itemOrigin.OsdData.longitude) {
        onAirportClick({
          lat: itemOrigin.OsdData.latitude,
          lng: itemOrigin.OsdData.longitude,
          name: itemOrigin.DName,
          device: itemOrigin
        });
      } else if (itemOrigin.Lat && itemOrigin.Lng) {
        // 兼容不同的数据格式
        onAirportClick({
          lat: itemOrigin.Lat,
          lng: itemOrigin.Lng,
          name: itemOrigin.DName,
          device: itemOrigin
        });
      }
    }
  };
  // 查看航线
  const fhBtn = <LastPageButton title="航线" />;
  const showMap = (record) => {
    setPage(
      <Card title={fhBtn}>
        <WayLineMap h1={getBodyH(180)} data={record} key={getGuid()} />
      </Card>
    );
  };

  const AirportItem = ({ itemOrigin, name, status, location, isFlying, isOnline, routes = [] }) => {
    // 使用父组件中的展开状态
    const deviceId = itemOrigin.ID || itemOrigin.SN;
    const expanded = expandedDevices[deviceId] || false;
    const isLoading = loadingMap[deviceId] || false;
    const routesData = deviceRoutesMap[deviceId] || [];
    
    // 处理展开/折叠图标点击
    const handleExpandToggle = async (e) => {
      e.stopPropagation();
      
      // 如果设备离线，禁用展开功能
      if (!isOnline) {
        return;
      }
      
      const newExpanded = !expanded;
      
      // 更新父组件中的展开状态
      setExpandedDevices(prev => ({
        ...prev,
        [deviceId]: newExpanded
      }));
      
      // 如果是展开且还没有获取过航线数据
      if (newExpanded && !isLoading && routesData.length === 0 && itemOrigin?.SN) {
        loadDeviceRoutes(deviceId, itemOrigin.SN);
      }
    };

    const getStatusText = (statusCode) => {
      const statusMap = {
        0: '空闲',
        1: '现场调试',
        2: '远程调试',
        3: '固件升级中',
        4: '作业中',
      };
      return statusMap[statusCode] || '空闲';
    };

    const getStatusColor = (statusCode) => {
      if (statusCode === 0) return '#1ABC9C'; // 空闲状态使用#1ABC9C
      if (statusCode === 4) return 'orange';  // 作业中使用orange
      return '';
    };

    const handleViewRoute = (e, route) => {
      e.stopPropagation();
      // 不再调用showMap打开新页面，而是使用传入的onShowRoute函数在地图上绘制航线
      if (onShowRoute) {
        onShowRoute(route);
      }
    };

    const handleExecute = async (e, route) => {
      e.stopPropagation();
      // 调用接口执行航线 taskId
      const res = await axiosApi('/api/v1/FlightTask/ExecuteFlightTask', 'PUT', {
        taskId: route.taskID,
      });
      if (res.code === 200) {
        message.success('航线执行成功');
        handleViewVideo(e);
      } else {
        message.error(res.msg || '航线执行失败');
      }
    };
    // 立即执行 航线
    const toFly = async (record, e) => {
      if (e) {
        e.stopPropagation();
      }
      
      if (isEmpty(record)) return;
      const pb1 = await CheckIfCanFly(record.SN);
      if (!pb1) return;
    
      localStorage.setItem("wayPoints", record.PointList);
      localStorage.removeItem("gpsPoints");
      // 调用接口执行航线 taskId
      const res = await axiosApi('/api/v1/FlightTask/ExecuteFlightTask', 'PUT', {
        taskId: record.taskID,
      });
      if (res.code === 1) {
        message.success(res?.data?.message || '航线执行成功');
      } else {
        message.error(res.msg || '航线执行失败');
      }
      const d1 = await HGet2("/api/v1/Device/GetBySN?sn=" + record.SN);
      if (isEmpty(d1)) {
        return;
      }
      localStorage.setItem("device", JSON.stringify(d1));
      // 将航线ID放到localStorage中
      localStorage.setItem("WanLineId", record.WanLineId);
      setPage(<DevicePage device={d1} />);
    };

    const handleViewVideo = (e) => {
      e.stopPropagation();
      
      // 如果设备离线，禁用视频查看功能
      if (!isOnline) {
        // message.warning('设备离线，无法查看视频');
        return;
      }
      
      if (onVideoClick && itemOrigin) {
        onVideoClick(itemOrigin);
      }
    };

    // 处理机场名称点击
    const handleAirportNameClick = (e) => {
      e.stopPropagation();
      
      // 如果设备离线，禁用点击功能
      if (!isOnline) {
        // message.warning('设备离线，无法定位');
        return;
      }
      
      handleAirportClick(e, itemOrigin);
    };

    // 处理驾驶舱点击
    const handleCockpitClick = (e) => {
      e.stopPropagation();
      
      // 如果设备离线，禁用驾驶舱功能
      if (!isOnline) {
        // message.warning('设备离线，无法进入驾驶舱');
        return;
      }
      
      onDClick(itemOrigin);
    };

    // 使用保存在父组件中的航线数据
    const displayRoutes = routesData;

    return (
      <div className={`${styles.airportContainer} ${expanded ? styles.expanded : ''}`}>
        <div 
          className={`${styles.airportItem} ${!isOnline ? styles.offline : ''}`}
        >
          {/* <span className={styles.airportIcon} /> */}
          <span className={styles.airportName} title={name}
            onClick={handleAirportNameClick}
          >{name}</span>
          <span className={`${styles.airportOnline} ${!isOnline ? styles.offline : ''}`} title={isOnline ? '在线' : '离线'}>
            {isOnline ? '在线' : '离线'}
          </span>
          <span 
            className={`${styles.airportStatus} ${isFlying ? styles.flying : ''}`} 
            title={getStatusText(status)}
            style={{color: getStatusColor(status)}}
          >
            {getStatusText(status)}
          </span>
          <span 
            className={`${styles.airportLocation} ${!isOnline ? styles.offline : ''}`}
            onClick={handleCockpitClick}
            title={isOnline ? "进入驾驶舱" : "设备离线"}
            style={{ cursor: isOnline ? 'pointer' : 'not-allowed' }}
          />
          <span 
            className={`${styles.videoIcon} ${!isOnline ? styles.offline : ''}`} 
            onClick={handleViewVideo} 
            title={isOnline ? "打开摄像头" : "设备离线"}
            style={{ cursor: isOnline ? 'pointer' : 'not-allowed' }}
          />
          <span className={`${styles.expandIcon} ${!isOnline ? styles.offline : ''}`}
            onClick={handleExpandToggle}
            style={{ cursor: isOnline ? 'pointer' : 'not-allowed' }}
          >
            {expanded ? <UpOutlined /> : <DownOutlined />}
          </span>
        </div>
        
        {expanded && isOnline && (
          <div className={styles.routesList} onClick={(e) => e.stopPropagation()}>
            {isLoading ? (
              <div className={styles.loadingContainer}>
                <Spin 
                  indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />}
                  spinning={true}
                >
                  <div style={{ minHeight: 60, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <span style={{ color: 'rgba(255, 255, 255, 0.65)', marginTop: 8 }}>加载中...</span>
                  </div>
                </Spin>
              </div>
            ) : displayRoutes.length > 0 ? (
              <Timeline
                items={displayRoutes.map((route, index) => ({
                  children: (
                    <div className={styles.routeInfo} onClick={(e) => e.stopPropagation()}>
                      <div className={styles.routeHeader}>
                        <span className={styles.routeName} title={route.taskName}>{route.taskName}</span>
                        <div className={styles.routeBtns}>
                          <Button 
                            type="primary" 
                            size="small" 
                            className={styles.viewRouteBtn}
                            onClick={(e) => handleViewRoute(e, route)}
                          >
                            查看航线
                          </Button>
                          {status === 0 && (
                            <Button 
                              type="primary" 
                              size="small" 
                              className={styles.executeBtn}
                              onClick={(e) => toFly(route, e)}
                            >
                              立即执行
                            </Button>
                          )}
                        </div>
                      </div>
                      <div className={styles.routeDetails}>
                        <div className={styles.routeDescRow}>
                          <span className={styles.routeLabel}>任务描述:</span>
                          <span className={styles.routeDesc}>{route.remark}</span>
                        </div>
                        <div className={styles.routeTimeRow}>
                          <span className={styles.routeLabel}>计划时间:</span>
                          <span className={styles.routeTime}>
                            {route.execTime}
                          </span>
                        </div>
                      </div>
                    </div>
                  )
                }))}
              />
            ) : (
              <div className={styles.noRoutes}>
                <p>暂无航线任务</p>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };
  const getItem = d1 => {
    return (
      <div
        onClick={() => {
          onDClick(d1);
        }}
        key={d1.ID}
        style={{
          cursor: 'pointer',
          background: '',
          marginTop: 8.0,
          height: 80.0,
          width: '100%',
          padding: 8.0,
        }}
      >
        <div
          style={{
            display: 'flex',
            height: 36.0,
            width: '100%',
            fontSize: 14.0,
          }}
        >
          <div style={{ width: 50, marginLeft: 8.0 }}>
            <img src={icon4} height={36.0} width={36.0} />
          </div>
          <div style={{ flex: 'auto' }}>
            <p style={{ lineHeight: '36px', color: 'white' }}>{d1.DName}</p>
          </div>
        </div>

        <div
          style={{
            height: 36.0,
            marginTop: 4.0,
            width: '100%',
            marginLeft: 8.0,
            fontSize: 11.0,
            color: 'white',
          }}
        >
          <span style={{ width: 40.0 }}>
            <Badge color={getColor(d1)} status={'success'} text={''} />
          </span>
          <span style={{ width: 60.0, marginLeft: 4.0 }}>{getZT(d1)} </span>
          {isEmpty(d1.OsdData) ? null : (
            <span style={{ marginLeft: 8.0 }}>
              <Progress
                strokeColor={getDCColor(isIndividual(d1) ? d1.OsdData.capacity_percent : d1.OsdData.drone_charge_state?.capacity_percent)}
                steps={6}
                percent={isIndividual(d1) ? d1.OsdData.capacity_percent : d1.OsdData.drone_charge_state?.capacity_percent}
                format={percent => <span style={{ color: 'white', fontSize: 12.0 }}>{percent + '%'}</span>}
                size="small"
              />
            </span>
          )}
        </div>
      </div>
    );
  };

  const getL = () => {
    const list = [];
    if (isEmpty(deviceList)) return list;
    deviceList.forEach(e => {
      list.push(getItem(e));
    });
    return list;
  };

  const toggleCollapse = () => {
    setIsCollapsed(prevState => !prevState);
  };

  const jcNoDiv = (
    <div
      onClick={toggleCollapse}
      style={{
        zIndex: 1010,
        background: 'rgba(44, 131, 183)',
        userSelect: 'none',
        cursor: 'pointer',
        color: 'white',
        padding: 8.0,
        writingMode: 'vertical-rl',
        borderRadius: '0px 5px 5px 0px',
        position: 'absolute',
        top: 8,
        left: 0,
      }}
    >
      机场状态监控
    </div>
  );
  return (
    <>
      {isCollapsed ? (
        jcNoDiv
      ) : (
        <div style={{ height: 'calc(100vh - 47px)', width: '500px', paddingTop: 20.0, paddingBottom: 5.0,
          background: 'linear-gradient(to left, rgb(10 19 28 / 80%) 0%, rgb(10 19 28 / 30%) 70%, rgb(10 19 28 / 0%) 100%)',
          position: 'relative'
         }}>
          <div style={{height: 'calc(100% - 25px)', width: '400px', position: 'absolute', right: 0}}>
            <BorderBox7 style={{ background: `rgba(0,45,139,0.3)` }}>
            <div className={styles.section + ' ' + styles.airportSection}>
              <div className={styles.sectionTitle}>设备列表</div>
              <div className={`${styles.sectionContent} ${styles.airportList} ${styles.wrapper}`}>
                {deviceList.map(item => {
                  return (
                    <AirportItem 
                      itemOrigin={item}
                      key={item.ID} 
                      name={item.DName} 
                      status={item?.OsdData?.mode_code} 
                      location={item?.IfOnLine} 
                      isFlying={item?.OsdData?.is_flying} 
                      isOnline={item?.IfOnLine}
                      routes={item.routes || []}
                    />
                  )
                })}
              </div>
            </div>
          </BorderBox7>
          </div>
        </div>
      )}
    </>
  );
};

export default DockStatePanel;
