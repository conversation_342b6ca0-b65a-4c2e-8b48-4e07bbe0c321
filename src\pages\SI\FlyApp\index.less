.fly-app-container {
  height: 100%;
  width: 100%;
  color: #fff;
  padding: 40px 0;
  background-color: none;
  padding: 0 10vw;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  .content-wrapper {
    width: 100%;
    padding: 0 20px;
  }

  .app-list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 24px;

    .ant-col {
      flex: 0 1 calc(25% - 24px); // 4列布局
      min-width: 280px; // 卡片最小宽度
      margin-bottom: 0;

      @media (max-width: 1200px) {
        flex-basis: calc(33.33% - 24px); // 3列布局
      }

      @media (max-width: 992px) {
        flex-basis: calc(50% - 24px); // 2列布局
      }

      @media (max-width: 576px) {
        flex-basis: 100%; // 1列布局
      }

      // &:hover {
      //   .app-title {
      //     background: rgba(11, 26, 54, 0.95);
      //     transform: translateY(-5px);
      //     box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      //   }
      // }
    }

    .app-card {
      width: 100%;
      height: 100%;
      border-radius: 7px;
      /* border: 2px solid rgba(87, 121, 193, 0.7) !important; */
      /* background: rgba(11, 26, 54, 0.9); */
      overflow: hidden;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);

        // border-color: rgba(87, 121, 193, 1) !important;
        .ant-card-cover {
          border: 3px solid rgb(48, 172, 116) !important;
        }
      }

      .app-image {
        width: 100%;
        height: auto;
        aspect-ratio: 16/12;
        object-fit: cover;
        max-height: 35vh;
      }

      .app-title {
        padding: 8px 12px;
        border-radius: 4px;
        color: #fff;
        font-size: 1rem;
        text-align: center; // 文字居中
        white-space: nowrap;
        overflow: visible;
        text-overflow: clip;
        z-index: 1;
        transition: all 0.3s ease;

        @media (max-width: 1600px) {
          font-size: 0.9rem;
          padding: 6px 10px;
        }

        @media (max-width: 1200px) {
          white-space: normal;
          line-height: 1.4;
          bottom: -32px;
        }
      }

      .ant-card-cover {
        margin-top: 0px;
        margin-inline-start: 0px;
        margin-inline-end: 0px;
        padding: 10px;
        border: 3px solid rgba(87, 121, 193, 0.7) !important;
        border-radius: 7px;
        transition: all 0.3s ease;
        // box-sizing: border-box;
      }

      .ant-card-body {
        padding: 24px 0 0 0;
      }
    }
  }
}

:global {
  .ant-card {
    background: transparent;

    .ant-card-body {
      padding: 0 !important;
      background: transparent;
    }

    .ant-card-hoverable:hover {
      border-color: rgba(24, 144, 255, 0.7);
    }
  }
}