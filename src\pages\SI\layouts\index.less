.si-container {
  display: flex;
  flex: 1;
  position: relative;
  overflow: hidden;
  height: calc(100vh - 47px); /* 减去头部高度 MyHead.less 47px*/
  // .nfzPageForSI {
  //   background: none !important;
  //   color: #fff !important;
  //   .Label_Class {
  //     color: #fff !important;
  //   }
  //   .splitterOne_Topdiv{
  //     color: #fff !important;
  //   }
  //   // antd
  //   .ant-card-head {
  //     display: none !important;
  //   }
  //   .ant-empty-description{
  //     color: #fff !important;
  //   }
  // }
  // .noDataColor{
  //     .ant-empty-description{
  //     color: #fff !important;
  //   }
  // }
}
// 滚动条样式修改
// ::-webkit-scrollbar {
//   width: 5px;
// }
// ::-webkit-scrollbar-thumb {
//   background-color: rgba(255, 255, 255, 0.2);
//   border-radius: 2px;
// }
// ::-webkit-scrollbar-track {
//   background-color: rgba(255, 255, 255, 0.1);
//   border-radius: 2px;
// }