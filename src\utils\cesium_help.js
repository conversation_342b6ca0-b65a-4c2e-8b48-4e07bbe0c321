import { isEmpty } from "@/utils/utils";
import { ImageryLayer, UrlTemplateImageryProvider, WebMercatorTilingScheme } from "cesium";
import { Cesium } from "umi";
import * as turf from '@turf/turf';
import useTerrainStore from '@/stores/terrainStore';

export const TDT_URL = "https://t{s}.tianditu.gov.cn/";
export const TDT_TOKEN = "6be044a675016cd9bc182716c89f042c";

export function GetCesiumViewer(id, timeline = false) {
  // const   imgPro=new Cesium.WebMapTileServiceImageryProvider({
  //   url: "https://{s}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={TileMatrix}&TILEROW={TileRow}&TILECOL={TileCol}&tk=d26935ae77bbc1fb3a6866a5b6ff573f",
  //   layer: "img_w",
  //   style: "default",
  //   format: "tiles",
  //   tileMatrixSetID: "w",
  //   subdomains:["t0","t1","t2","t3","t4","t5","t6","t7"],
  //   tilingScheme: new Cesium.WebMercatorTilingScheme(),
  // })
  const subdomains = ["0", "1", "2", "3", "4", "5", "6", "7"];
  const imgMapP = new UrlTemplateImageryProvider({
    url: TDT_URL + "DataServer?T=img_w&x={x}&y={y}&l={z}&tk=" + TDT_TOKEN,
    subdomains: subdomains,
    tilingScheme: new WebMercatorTilingScheme(),
    maximumLevel: 18,
  });

  const baseLayer = new ImageryLayer(imgMapP);

  // 获取地形 store 实例
  const terrainStore = useTerrainStore.getState();

  // 初始化并获取默认地形提供者
  const initialTerrainProvider = terrainStore.initDefaultTerrain();
  console.log('使用全局缓存的地形提供者创建 viewer');

  const viewer2 = new Cesium.Viewer(id, {
    // terrain: Cesium.Terrain.fromWorldTerrain(),
    // 内网环境设置
    terrainProvider: initialTerrainProvider,

    // 其他配置保持不变
    infoBox: false,
    imageryProvider: false, // 如果没有离线影像，地球会是黑色的，可以暂时用一个在线影像或纯色代替
    baseLayerPicker: false,
    sceneModePicker: false,
    homeButton: false,
    fullscreenButton: false,
    timeline: false,
    navigationHelpButton: false,
    navigationInstructionsInitiallyVisible: false,
    animation: timeline,
    geocoder: false,
    sceneMode: 3,
    baseLayer: baseLayer,
    selectionIndicator: false,
    shouldAnimate: false,
  });
  console.log('viewer2.terrainProvider (初始):', viewer2.terrainProvider);

  // 异步加载最佳可用的地形提供者并替换
  terrainStore.getBestAvailableTerrain().then((bestTerrainProvider) => {
    if (bestTerrainProvider && viewer2 && !viewer2.isDestroyed()) {
      viewer2.terrainProvider = bestTerrainProvider;
      console.log('viewer2.terrainProvider (替换后):', viewer2.terrainProvider);
    }
  }).catch((error) => {
    console.warn('最佳地形提供者加载失败，继续使用默认地形:', error);
  });


  viewer2.scene.sun.show = false;
  viewer2.scene.moon.show = false;
  viewer2.scene.skyBox.show = false; //关闭天空盒，否则会显示天空颜色
  viewer2.scene.globe.show = true;
  // 隐藏版权
  viewer2.cesiumWidget.creditContainer.style.display = "none";

  if (Cesium.FeatureDetection.supportsImageRenderingPixelated()) { //判断是否支持图像渲染像素化处理
    viewer2.resolutionScale = window.devicePixelRatio;
  }
  return viewer2;
}

export function OnClickLeftHandle(viewer, setP) {
  if (isEmpty(viewer)) return;
  const onLeftClick = (event) => {
    const pickedObject = viewer.scene.pick(event.position)
    if (
      Cesium.defined(pickedObject) &&
      pickedObject.id instanceof Cesium.Entity
    ) {
      const entity = pickedObject.id

      if (entity.hasOwnProperty('shootId')) {
        setP(pickedObject.id['checkPoint'])
      }
    }
  }

  const handler = new Cesium.ScreenSpaceEventHandler(viewer.canvas)
  handler.setInputAction(
    onLeftClick,
    Cesium.ScreenSpaceEventType.LEFT_CLICK
  )
}

//添加3DTiles格式数据
export function AddTile3DByUrl(viewer, url) {
  // 
  if (isEmpty(viewer)) return;
  const xxx = async () => {
    try {
      const tileset = await Cesium.Cesium3DTileset.fromUrl(url, {
        skipLevelOfDetail: true, // 启用
        baseScreenSpaceError: 100, // 基础屏幕错误阈值
        skipScreenSpaceErrorFactor: 16, // 跳过屏幕错误因子
        skipLevels: 1, // 跳过级别
        immediatelyLoadDesiredLevelOfDetail: false, // 是否立即加载所需细节级别
        loadSiblings: false, // 是否加载兄弟节点
        maximumScreenSpaceError: 2.0,
        optimizeForCesium: true,
      });
      //  
      viewer.scene.primitives.add(tileset);
      viewer.flyTo(tileset);
    } catch (error) {
      console.error(`Error creating tileset: ${error}`);
    }
  };
  xxx();
}

//添加3DTiles格式数据
export function AddTile3DByUrl2(viewer, url) {
  // 
  if (isEmpty(viewer)) return;
  const xxx = async () => {
    try {
      const tileset = await Cesium.Cesium3DTileset.fromUrl(url, {
        skipLevelOfDetail: true, // 启用
        baseScreenSpaceError: 100, // 基础屏幕错误阈值
        skipScreenSpaceErrorFactor: 16, // 跳过屏幕错误因子
        skipLevels: 82, // 跳过级别
        immediatelyLoadDesiredLevelOfDetail: true, // 是否立即加载所需细节级别
        loadSiblings: false, // 是否加载兄弟节点
        maximumScreenSpaceError: 1.0,
        optimizeForCesium: true,
      });
      //  
      viewer.scene.primitives.add(tileset);
      viewer.flyTo(tileset);
    } catch (error) {
      console.error(`Error creating tileset: ${error}`);
    }
  };
  xxx();
}

//设置照相机位置
export function SetCamera(viewer, lat, lng, h1, yaw, pitch, roll, focal) {
  var center = Cesium.Cartesian3.fromDegrees(lat, lng, h1);

  viewer.scene.camera.setView({
    destination: center,
    orientation: {
      heading: yaw,
      pitch: pitch,
      roll: roll,
    },
  });
  viewer.scene.camera.frustum.scale = focal / 24 * viewer.scene.camera.frustum.scale
  // viewer.scene.camera.setFocalLength(focal)
}

export function SetCameraControl(viewer) {
  let { scene, camera, screenSpaceCameraController } = viewer
  // 禁止默认的事件
  scene.screenSpaceCameraController.enableRotate = false;//禁止旋转
  scene.screenSpaceCameraController.enableTranslate = false;// 禁止移动
  scene.screenSpaceCameraController.enableZoom = false;//禁止缩放
  scene.screenSpaceCameraController.enableTilt = false;//禁止倾斜相机
  scene.screenSpaceCameraController.enableLook = false;
}
export function PointImg(i) {
  let svgString = `<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 26' width='32' height='26'> <g fill='none' fill-rule='evenodd'> <path fill='#2D8CF0' d='M16.8320503 24.75192456L30.9635332 3.5547002c.3063525-.4595287.1821786-1.080398-.2773501-1.3867505C30.5219156 2.058438 30.3289079 2 30.1314829 2H1.86851709c-.55228475 0-1 .4477153-1 1 0 .197425.05843803.3904327.16794971.5547002l14.1314829 21.19722436c.3063525.45952869.9272218.58370256 1.3867505.2773501.1098523-.07323486.2041152-.16749781.2773501-.2773501z'/> <text fill='#FFF' font-size='14' font-weight='500'> <tspan x='50%' y='50%' dy='.1em' text-anchor='middle'> 15 </tspan> </text> </g> </svg>`;
  svgString = svgString.replace("15 </tspan>", i + " </tspan>")
  // 使用base64编码SVG字符串
  var svgDataUrl = 'data:image/svg+xml;base64,' + btoa(svgString);
  return svgDataUrl;
}
export function PointImg2(i) {
  let svgString = `<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 26' width='32' height='26'> <g fill='none' fill-rule='evenodd'> <path fill='#00d690' d='M16.8320503 24.75192456L30.9635332 3.5547002c.3063525-.4595287.1821786-1.080398-.2773501-1.3867505C30.5219156 2.058438 30.3289079 2 30.1314829 2H1.86851709c-.55228475 0-1 .4477153-1 1 0 .197425.05843803.3904327.16794971.5547002l14.1314829 21.19722436c.3063525.45952869.9272218.58370256 1.3867505.2773501.1098523-.07323486.2041152-.16749781.2773501-.2773501z'/> <text fill='#FFF' font-size='14' font-weight='500'> <tspan x='50%' y='50%' dy='.1em' text-anchor='middle'> 15 </tspan> </text> </g> </svg>`;
  svgString = svgString.replace("15 </tspan>", i + " </tspan>")
  // 使用base64编码SVG字符串
  var svgDataUrl = 'data:image/svg+xml;base64,' + btoa(svgString);
  return svgDataUrl;
}
export function PointImg3(i) {
  let svgString = `<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 26' width='32' height='26'> <g fill='none' fill-rule='evenodd'> <path fill='#5cadff' d='M16.8320503 24.75192456L30.9635332 3.5547002c.3063525-.4595287.1821786-1.080398-.2773501-1.3867505C30.5219156 2.058438 30.3289079 2 30.1314829 2H1.86851709c-.55228475 0-1 .4477153-1 1 0 .197425.05843803.3904327.16794971.5547002l14.1314829 21.19722436c.3063525.45952869.9272218.58370256 1.3867505.2773501.1098523-.07323486.2041152-.16749781.2773501-.2773501z'/> <text fill='#FFF' font-size='14' font-weight='500'> <tspan x='50%' y='50%' dy='.1em' text-anchor='middle'> 15 </tspan> </text> </g> </svg>`;
  svgString = svgString.replace("15 </tspan>", i + " </tspan>")
  // 使用base64编码SVG字符串
  var svgDataUrl = 'data:image/svg+xml;base64,' + btoa(svgString);
  return svgDataUrl;
}
export function DrawLookCone(viewer, p1, zoom) {
  let positions = p1.positions
  let spotLightCamera = new Cesium.Camera(viewer.scene)

  spotLightCamera.setView({
    destination: positions,
    orientation: p1.headingPitchRoll,
  })
  spotLightCamera.debugShowFrustums = true;

  let scratchRight = new Cesium.Cartesian3()
  let scratchRotation = new Cesium.Matrix3()
  var scratchOrientation = new Cesium.Quaternion()

  let position = spotLightCamera.positionWC
  let directions = spotLightCamera.directionWC
  let up = spotLightCamera.upWC
  let right = spotLightCamera.rightWC
  right = Cesium.Cartesian3.negate(right, scratchRight)
  Cesium.Matrix3.setColumn(scratchRotation, 0, right, scratchRotation)
  Cesium.Matrix3.setColumn(scratchRotation, 1, up, scratchRotation)
  Cesium.Matrix3.setColumn(scratchRotation, 2, directions, scratchRotation)
  //计算视锥姿态
  let orientation = Cesium.Quaternion.fromRotationMatrix(
    scratchRotation,
    scratchOrientation
  )
  spotLightCamera.frustum.fov = Cesium.Math.toRadians(15 / zoom);
  spotLightCamera.frustum.aspectRatio = (4 / 3)
  spotLightCamera.frustum.near = 0.1
  spotLightCamera.frustum.far = 200
  //视锥轮廓线图形
  let instanceOutline = new Cesium.GeometryInstance({
    geometry: new Cesium.FrustumGeometry({
      frustum: spotLightCamera.frustum,
      origin: position,
      orientation: orientation,
    }),
    material: Cesium.Color.RED.withAlpha(1),
    id: 'pri' + viewer.scene.primitives.length + 1,
    attributes: {
      color: Cesium.ColorGeometryInstanceAttribute.fromColor(
        new Cesium.Color(0.0, 1.0, 0.0, 0.1)
      ),
      show: new Cesium.ShowGeometryInstanceAttribute(true),
    },
    properties: {
      isClickable: false
    }
  })

  let instance = new Cesium.GeometryInstance({
    geometry: new Cesium.FrustumOutlineGeometry({
      frustum: spotLightCamera.frustum,
      origin: position,
      orientation: orientation,
    }),
    material: Cesium.Color.RED.withAlpha(0.1),
    id: 'pri0' + viewer.scene.primitives.length + 1,
    attributes: {
      color: Cesium.ColorGeometryInstanceAttribute.fromColor(
        new Cesium.Color(0.0, 0.8, 0.0, 0.8)
      ),
      show: new Cesium.ShowGeometryInstanceAttribute(true),
    },
    properties: {
      isClickable: false
    }
  })

  const primitivesone = viewer.scene.primitives.add(
    new Cesium.Primitive({
      geometryInstances: instance,
      appearance: new Cesium.PerInstanceColorAppearance({
        translucent: true,
        flat: true,
      }),
      asynchronous: false,
      properties: {
        isClickable: false
      }
    })
  )

  const primitivestwo = viewer.scene.primitives.add(
    new Cesium.Primitive({
      geometryInstances: instanceOutline,
      appearance: new Cesium.PerInstanceColorAppearance({
        translucent: true,
        flat: true,
      }),
      asynchronous: false,
      properties: {
        isClickable: false
      }
    })
  )
  return { c1: primitivesone, c2: primitivestwo }
}
export function Cartesian3_TO_Position(Cartesian3, viewer) {
  const cartographic = Cesium.Cartographic.fromCartesian(Cartesian3);
  const longitude = Cesium.Math.toDegrees(cartographic.longitude);
  const latitude = Cesium.Math.toDegrees(cartographic.latitude);
  const height = cartographic.height;
  let globeHeight = viewer.scene.globe.getHeight(cartographic)
  return { longitude, latitude, height, globeHeight };
}
/**
* @description 计算贴地形距离
* @param {Cesium.Viewer} viewer
* @param {Cesium.Cartesian3} start
* @param {Cesium.Cartesian3} end
* @returns {number}
*/
export function getTerrainDistance(viewer, edit_polyline_position) {
  let res = 0
  for (let i = 0; i < edit_polyline_position.length - 3; i += 3) {
    let point1 = [edit_polyline_position[i], edit_polyline_position[i + 1], edit_polyline_position[i + 2]]
    let point2 = [edit_polyline_position[i + 3], edit_polyline_position[i + 4], edit_polyline_position[i + 5]]
    res += distanceOfline(viewer, point1, point2)
  }
  return res
}
function distanceOfline(viewer, point1, point2) {
  let step_lng = (point2[0] - point1[0]) / 20
  let step_lat = (point2[1] - point1[1]) / 20
  let res = 0
  for (let i = 0; i < 20; i++) {
    var pointA = new Cesium.Cartesian3.fromDegrees(point1[0] + (i * step_lng), point1[1] + (i * step_lat), viewer.scene.globe.getHeight(Cesium.Cartographic.fromCartesian(Cesium.Cartesian3.fromDegrees(point1[0] + (i * step_lng), point1[1] + (i * step_lat), 0)))); // 第一个点的坐标（x、y、z）
    var pointB = new Cesium.Cartesian3.fromDegrees(point1[0] + ((i + 1) * step_lng), point1[1] + ((i + 1) * step_lat), viewer.scene.globe.getHeight(Cesium.Cartographic.fromCartesian(Cesium.Cartesian3.fromDegrees(point1[0] + ((i + 1) * step_lng), point1[1] + ((i + 1) * step_lat), 0)))); // 第二个点的坐标（x、y、z）
    res += Cesium.Cartesian3.distance(pointA, pointB);
  }
  return res
}
// 生成三角网
export function GenerateTriangularMesh(viewer, edit_rectangle_polygon_position, type, Referenc_plane_height, precision) {
  let res = createPolygonBounds(edit_rectangle_polygon_position, 3)
  let center_position = [res.center_lng, res.center_lat]
  let surfaceArea = 0
  let wf = 0
  let tf = 0
  let itemLng = (res.maxLng - res.minLng) / precision
  let itemLat = (res.maxLat - res.minLat) / precision
  let pointPosiotion = []
  let new_polygon_position = []
  for (let i = 0; i < edit_rectangle_polygon_position.length; i += 3) {
    let array1 = [edit_rectangle_polygon_position[i], edit_rectangle_polygon_position[i + 1]]
    new_polygon_position.push(array1)
  }
  new_polygon_position.push(new_polygon_position[0])
  const polygon = turf.polygon([new_polygon_position]);
  let points = []
  for (let i = 0; i < precision + 1; i++) {
    for (let j = 0; j < precision + 1; j++) {
      pointPosiotion.push([res.minLng + i * itemLng, res.minLat + j * itemLat])
      let globeHeight = viewer.scene.globe.getHeight(Cesium.Cartographic.fromCartesian(Cesium.Cartesian3.fromDegrees(res.minLng + i * itemLng, res.minLat + j * itemLat, 0)))
      points.push(turf.point([res.minLng + i * itemLng, res.minLat + j * itemLat, globeHeight], { z: globeHeight }))// 将高度值保存到名为 'z' 的属性中); 
    }
  }
  // 创建一个FeatureCollection
  var featureCollection = turf.featureCollection(points);
  var tin = turf.tin(featureCollection, 'z');
  var triangleList = [];  // 用于存放所有的三角形GeometryInstance
  var GeometryInstanceList = [];
  // 遍历每个TIN三角形
  tin.features.forEach(function (feature, i) {
    var coordinates = feature.geometry.coordinates[0];
    if (coordinates.length >= 4) {
      var poly2 = turf.polygon([coordinates]);
      var intersection = turf.intersect(turf.featureCollection([polygon, poly2]));
      if (intersection) {
        let fromDegreesArray = []
        let fromDegreesArrayHeights = []
        var positions = intersection.geometry.coordinates[0].map(function (coord, index) {
          fromDegreesArray.push(coord[0], coord[1], 0)
          fromDegreesArrayHeights.push(coord[0], coord[1], viewer.scene.globe.getHeight(Cesium.Cartographic.fromCartesian(Cesium.Cartesian3.fromDegrees(coord[0], coord[1], 0))));
          return Cesium.Cartesian3.fromDegrees(coord[0], coord[1], viewer.scene.globe.getHeight(Cesium.Cartographic.fromCartesian(Cesium.Cartesian3.fromDegrees(coord[0], coord[1], 0))));
        });
        let newPositions = positions
        newPositions.splice(-1)

        const point1 = positions[0];
        const point2 = positions[1];
        const point3 = positions[2];
        //使用我们新定义的函数计算每个三角形的坡度
        var analysisResult = calculateSlopeAndAspect(point1, point2, point3);
        surfaceArea += squareMeasure(point1, point2, point3)
        let projectedArea = ProjectedArea(fromDegreesArray)
        let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(analysisResult.centroid, viewer)
        if (Referenc_plane_height !== null) {
          if (height > Referenc_plane_height) {
            wf += projectedArea * (height - Referenc_plane_height)
          } else {
            tf += projectedArea * (Referenc_plane_height - height)
          }
        }
        var slope = analysisResult.slope;
        var hue = slope / 90.0; // 将坡度从0到90度映射到色调从0到1
        var saturation = 1.0;   // 全饱和度
        var lightness = 0.5;    // 正常亮度
        var alpha = 0.5;        // 完全不透明
        //将HSL颜色转换为RGBA，当坡度为0度时，hue变为0，颜色是红色；当坡度为90度时，hue变为1，颜色是绿色；在0到90度之间的坡度将映射到从红色到绿色之间的颜色。
        var color = Cesium.Color.fromHsl(hue, saturation, lightness).withAlpha(alpha);
        triangleList.push({
          position: newPositions,
          color: color
        })
        if (type === 'square') {
          var instance3 = new Cesium.GeometryInstance({
            geometry: new Cesium.PolygonOutlineGeometry({
              polygonHierarchy: new Cesium.PolygonHierarchy(
                Cesium.Cartesian3.fromDegreesArrayHeights(fromDegreesArrayHeights)
              ),
              extrudedHeight: Referenc_plane_height,
              perPositionHeight: true,
              // closeTop: true,
              // closeBottom:true
            }),
            attributes: {
              color: Cesium.ColorGeometryInstanceAttribute.fromColor(Cesium.Color.fromCssColorString(height <= Referenc_plane_height ? "#05B1FA" : "#D505FA").withAlpha(0.5)),
            },
          });
          GeometryInstanceList.push(instance3)
        }
      }
    }
  });

  return { center_position, surfaceArea, wf, tf, GeometryInstanceList, triangleList }
}
/**@method 创建多边形外接矩形
* @param {Array} positions_array - 格式为[lat,lng]的经纬度数组，多边形的顶点集合
* @param {number} num - positions_array是否含有高度，有的话是3，没有是2
* @return {Object} .center - 返回外接矩形的中心坐标和四至点
*/
function createPolygonBounds(positions_array, num) {
  let lats = [];
  let lngs = [];
  for (let i = 0; i < positions_array.length; i++) {
    if (i % num === 0) {
      lngs.push(positions_array[i]);
    }
    if (i % num === 1) {
      lats.push(positions_array[i]);
    }
  }
  let maxLat = Math.max(...lats)
  let maxLng = Math.max(...lngs)
  let minLat = Math.min(...lats);
  let minLng = Math.min(...lngs);
  let center_lng = (maxLng + minLng) / 2
  let center_lat = (maxLat + minLat) / 2
  return {
    maxLng, maxLat, minLng, minLat, center_lng, center_lat
  }
}
/**
   * 计算三角形的坡度
   * @param {*} point1 
   * @param {*} point2 
   * @param {*} point3 
   * @returns 
   */
function calculateSlopeAndAspect(point1, point2, point3) {
  // 计算两个向量，它们位于三角形的两边
  var v1 = Cesium.Cartesian3.subtract(
    point1,
    point2,
    new Cesium.Cartesian3()
  );
  var v2 = Cesium.Cartesian3.subtract(
    point2,
    point3,
    new Cesium.Cartesian3()
  );
  // 计算这两个向量的叉积，得到三角形的法向量
  var normal = new Cesium.Cartesian3();
  Cesium.Cartesian3.cross(v1, v2, normal);

  // 计算三角形的中心点
  var centroid = new Cesium.Cartesian3(
    (point1.x + point2.x + point3.x) / 3,
    (point1.y + point2.y + point3.y) / 3,
    (point1.z + point2.z + point3.z) / 3
  );

  // 检查法向量的模长平方是否过小 (接近零向量)
  // 如果是，则认为这是一个退化三角形，坡度为0
  if (Cesium.Cartesian3.magnitudeSquared(normal) < Cesium.Math.EPSILON12) { // 使用 EPSILON12 作为阈值
    return { slope: 0, centroid: centroid };
  }

  Cesium.Cartesian3.normalize(normal, normal); // 现在可以安全地归一化

  // 得到从地球中心到三角形中心的单位向量（这里不应该使用垂直向量(0,0,1)，如果使用(0,0,1)会导致面南的坡度大，面北的坡度小）
  var centerToCentroid = new Cesium.Cartesian3();
  Cesium.Cartesian3.normalize(centroid, centerToCentroid);

  // 再次检查 centerToCentroid 是否为零向量 (虽然对于地球表面的点不太可能，但增加健壮性)
  if (Cesium.Cartesian3.magnitudeSquared(centerToCentroid) < Cesium.Math.EPSILON12) {
    return { slope: 0, centroid: centroid }; // 如果中心点接近原点，也返回0坡度
  }


  // 确保法线向量指向地球外部
  if (Cesium.Cartesian3.dot(normal, centerToCentroid) < 0) {
    Cesium.Cartesian3.negate(normal, normal);
  }

  // 计算坡度：法向量和从地球中心到三角形中心的径向向量之间的夹角
  var dotProduct = Cesium.Cartesian3.dot(normal, centerToCentroid);
  // 钳制 dotProduct 到 [-1.0, 1.0] 区间，防止 acos 出错
  dotProduct = Cesium.Math.clamp(dotProduct, -1.0, 1.0);
  var slopeRadians = Math.acos(dotProduct);

  var slopeDegrees = Cesium.Math.toDegrees(slopeRadians);
  // 如果坡度大于90度，则将其减少到90度以下
  if (slopeDegrees > 90) {
    slopeDegrees = 180 - slopeDegrees;
  }
  // 返回坡度值
  return { slope: slopeDegrees, centroid: centroid };
}
// 计算表面积
function squareMeasure(point1, point2, point3) {
  var a = Cesium.Cartesian3.distance(point1, point2);
  var b = Cesium.Cartesian3.distance(point2, point3);
  var c = Cesium.Cartesian3.distance(point3, point1);
  var s = (a + b + c) / 2
  return Math.sqrt(s * (s - a) * (s - b) * (s - c))
}
// 计算投影面积
function ProjectedArea(fromDegreesArray) {
  var point1 = new Cesium.Cartesian3.fromDegrees(fromDegreesArray[0], fromDegreesArray[1], fromDegreesArray[2]); // 第一个点的坐标（x、y、z）
  var point2 = new Cesium.Cartesian3.fromDegrees(fromDegreesArray[3], fromDegreesArray[4], fromDegreesArray[5]); // 第二个点的坐标（x、y、z）
  var point3 = new Cesium.Cartesian3.fromDegrees(fromDegreesArray[6], fromDegreesArray[7], fromDegreesArray[8]); // 第三个点的坐标（x、y、z）
  var distance1 = Cesium.Cartesian3.distance(point1, point2);
  var distance2 = Cesium.Cartesian3.distance(point2, point3);
  var distance3 = Cesium.Cartesian3.distance(point3, point1);
  return (distance1 + distance2 + distance3) / 2
}
/**
   * 地图二三维视角切换
   * @param {*} viewer 
   * @returns {*} num
   */
export function rotateCamera(viewer, num) {
  //获取屏幕中心视点坐标
  var position = viewer.scene.globe.pick(viewer.camera.getPickRay(new Cesium.Cartesian2(
    viewer.canvas.clientWidth / 2,
    viewer.canvas.clientHeight / 2,
  )), viewer.scene);
  var initialPitch = viewer.camera.pitch;
  var pitch3d = Cesium.Math.toRadians(num);
  var angle = (pitch3d - initialPitch) / 10;//每次转动的度数
  // 获取相机和视点距离
  var distance = Cesium.Cartesian3.distance(position, viewer.scene.camera.positionWC);
  // 相机的当前heading
  var initialHeading = viewer.camera.heading;
  (function () {
    let count = 0;
    const intervalId = setInterval(() => {
      viewer.camera.lookAt(position, new Cesium.HeadingPitchRange(initialHeading, angle * (++count) + initialPitch, distance));
      if (count === 10) {
        clearInterval(intervalId);
        //解除目标锁定
        viewer.camera.lookAtTransform(Cesium.Matrix4.IDENTITY)
        viewer.scene.camera.setView({
          destination: viewer.scene.camera.positionWC,
          orientation: {
            heading: initialHeading,
            pitch: viewer.camera.pitch,
            roll: viewer.camera.roll,
          },
        });

      }
    }, 50);
  })()
}
/**@method 相机朝向切换
* @param {*} viewer 
* @returns {*} num
*/
export function rotateCameraHeading(viewer, num) {
  //获取屏幕中心视点坐标
  var position = viewer.scene.globe.pick(viewer.camera.getPickRay(new Cesium.Cartesian2(
    viewer.canvas.clientWidth / 2,
    viewer.canvas.clientHeight / 2,
  )), viewer.scene);

  var initialHeading = viewer.camera.heading === 6.283185307179586 ? 0 : viewer.camera.heading;
  var Heading = Cesium.Math.toRadians(num);
  if (initialHeading === Heading) {
    return
  }
  var angle = (Heading - initialHeading) / 10;//每次转动的度数
  // 获取相机和视点距离
  var distance = Cesium.Cartesian3.distance(position, viewer.scene.camera.positionWC);
  // 相机的当前heading
  var initialPitch = viewer.camera.pitch;

  (function () {
    let count = 0;
    const intervalId = setInterval(() => {
      viewer.camera.lookAt(position, new Cesium.HeadingPitchRange(angle * (++count) + initialHeading, initialPitch, distance));
      if (count === 10) {
        clearInterval(intervalId);
        viewer.scene.camera.setView({
          destination: viewer.scene.camera.positionWC,
          orientation: {
            heading: 0,
            pitch: viewer.camera.pitch,
            roll: viewer.camera.roll,
          },
        });
        //解除目标锁定
        viewer.camera.lookAtTransform(Cesium.Matrix4.IDENTITY)
      }
    }, 50);
  })()
}
/**@method 计算缓冲区
* @param {Array} positions_array - 格式为[lat,lng]的经纬度数组，多边形的顶点集合
* @param {number} num - 缓冲区半径
* @return {Object} center - 返回缓冲区点位
*/
export function computeBuffer(positions_array, left, right) {
  let new_positions_array = []
  for (let i = 0; i < positions_array.length; i += 3) {
    let array1 = [positions_array[i], positions_array[i + 1]]
    new_positions_array.push(array1)
  }
  let lines = []
  for (let i = 0; i < new_positions_array.length - 1; i++) {
    lines.push({
      Feature: turf.lineString([new_positions_array[i], new_positions_array[i + 1]]),
      position: [new_positions_array[i], new_positions_array[i + 1]],
      angle: turf.rhumbBearing(new_positions_array[i], new_positions_array[i + 1])
    })
  }
  let leftLines = []
  let rightLines = []
  for (let i = 0; i < lines.length; i++) {
    let newLeftLine = turf.transformTranslate(lines[i].Feature, (left / 1000), lines[i].angle - 90)
    let newRightLine = turf.transformTranslate(lines[i].Feature, (right / 1000), lines[i].angle + 90)
    leftLines.push([...newLeftLine.geometry.coordinates[0], ...newLeftLine.geometry.coordinates[1]])
    rightLines.push([...newRightLine.geometry.coordinates[0], ...newRightLine.geometry.coordinates[1]])
  }
  for (let i = 0; i < leftLines.length - 1; i++) {
    haveIntersection(leftLines, i)
    haveIntersection(rightLines, i)
  }
  let leftFlat = [...leftLines].flat(Infinity)
  let data = [...leftFlat]
  for (let i = rightLines.length - 1; i >= 0; i--) {
    data.push(rightLines[i][2], rightLines[i][3], rightLines[i][0], rightLines[i][1])
  }
  data = [...new Set(data)]
  let leftPosition = [...new Set([...leftLines].flat(Infinity))]
  let rightPosition = [...new Set([...rightLines].flat(Infinity))]
  return { data, leftPosition, rightPosition }
}
// 延长线
function prolongLine(oldpPosition, direction, distance) {
  let position = [...oldpPosition]
  var point
  var bearing
  if (direction === 'last') {
    point = turf.point([position[1][0], position[1][1]]);
    bearing = turf.rhumbBearing(position[0], position[1])
  } else if (direction === 'before') {
    point = turf.point([position[0][0], position[0][1]]);
    bearing = turf.rhumbBearing(position[1], position[0])
  }
  var destination = turf.destination(point, distance, bearing);
  if (direction === 'last') {
    position[1] = destination.geometry.coordinates
  } else if (direction === 'before') {
    position[0] = destination.geometry.coordinates
  }
  return position
}
// 判断两条线是否有交点
function haveIntersection(Lines, i) {
  var line1 = turf.lineString([
    [Lines[i][0], Lines[i][1]],
    [Lines[i][2], Lines[i][3]],
  ]);
  var line2 = turf.lineString([
    [Lines[i + 1][0], Lines[i + 1][1]],
    [Lines[i + 1][2], Lines[i + 1][3]],
  ]);
  var intersects = turf.lineIntersect(line1, line2)
  if (intersects.features.length) {
    Lines[i][2] = intersects.features[0].geometry.coordinates[0]
    Lines[i][3] = intersects.features[0].geometry.coordinates[1]
    Lines[i + 1][0] = intersects.features[0].geometry.coordinates[0]
    Lines[i + 1][1] = intersects.features[0].geometry.coordinates[1]
  } else {
    let res1 = prolongLine([[Lines[i][0], Lines[i][1]], [Lines[i][2], Lines[i][3]]], 'last', 50 / 1000)
    Lines[i][2] = res1[1][0]
    Lines[i][3] = res1[1][1]
    let res2 = prolongLine([[Lines[i + 1][0], Lines[i + 1][1]], [Lines[i + 1][2], Lines[i + 1][3]]], 'before', 50 / 1000)
    Lines[i + 1][0] = res2[0][0]
    Lines[i + 1][1] = res2[0][1]
    haveIntersection(Lines, i)
  }
}
// 计算航线
export function computeRoute(viewer, LP, CP, RP, INT, LD, RD, FH) {
  return new Promise(async (calllback) => {
    let N = Math.round((LD / INT))
    let M = Math.round((RD / INT))
    let LSD = (LD - ((N - 1) * INT) - (INT / 2))
    let RSD = (INT / 2)
    let res = []
    for (let j = 0; j < N; j++) {
      let item = []
      for (let i = 0; i < LP.length; i += 2) {
        let X = turf.distance(turf.point([LP[i], LP[i + 1]]), turf.point([CP[i], CP[i + 1]]))
        let Y = turf.rhumbBearing([LP[i], LP[i + 1]], [CP[i], CP[i + 1]])
        let P = turf.destination(turf.point([LP[i], LP[i + 1]]), (j === 0 ? (LSD * (X * 1000 / LD) / 1000) : ((LSD * (X * 1000 / LD) / 1000) + (INT * (X * 1000 / LD) / 1000) * j)), Y);
        item.push(P.geometry.coordinates)
      }
      res.push(item)
    }
    for (let j = 0; j < M; j++) {
      let item = []
      for (let i = 0; i < RP.length; i += 2) {
        let X = turf.distance(turf.point([RP[i], RP[i + 1]]), turf.point([CP[i], CP[i + 1]]))
        let Y = turf.rhumbBearing([CP[i], CP[i + 1]], [RP[i], RP[i + 1]])
        let P = turf.destination(turf.point([CP[i], CP[i + 1]]), (j === 0 ? (RSD * (X * 1000 / RD) / 1000) : ((RSD * (X * 1000 / RD) / 1000) + (INT * (X * 1000 / RD) / 1000) * j)), Y);
        item.push(P.geometry.coordinates)
      }
      res.push(item)
    }
    for (let i = 0; i < res.length; i++) {
      if ((i + 1) % 2 !== 0) {
        res[i].reverse()
      }
      const Promises = [];
      for (let j = 0; j < res[i].length; j++) {
        const promise = Cesium.sampleTerrainMostDetailed(viewer.terrainProvider, [Cesium.Cartographic.fromDegrees(res[i][j][0], res[i][j][1])]);
        Promises.push(promise);
      }
      const PromisesResults = await Promise.all(Promises);
      for (let j = 0; j < res[i].length; j++) {
        if (PromisesResults[j] && PromisesResults[j].length > 0) {
          res[i][j].push(PromisesResults[j][0].height + FH);
        }
      }
    }
    calllback(res)
  })
}
// 计算航线间隔
export function computeInterval(model, GSD, OverlapRate) {
  if (model === 'M4TD') {
    return ((GSD * 4032) / 100) * (1 - ((OverlapRate) / 100))
  } else if (model === 'M4D') {
    return ((GSD * 5280) / 100) * (1 - ((OverlapRate) / 100))
  } else if (model === 'M4E') {
    return ((GSD * 5280) / 100) * (1 - ((OverlapRate) / 100))
  } else if (model === 'M4T') {
    return ((GSD * 4032) / 100) * (1 - ((OverlapRate) / 100))
  } else if (model === 'M3TD') {
    return ((GSD * 4032) / 100) * (1 - ((OverlapRate) / 100))
  } else if (model === 'M3D') {
    return ((GSD * 5280) / 100) * (1 - ((OverlapRate) / 100))
  }
}
// 计算拍照间隔时间
export function computeIntervalTime(model, GSD, OverlapRate, Speed) {
  if (model === 'M4TD') {
    return ((GSD * 3023) / 100) * (1 - ((OverlapRate) / 100)) / Speed
  } else if (model === 'M4D') {
    return ((GSD * 3956) / 100) * (1 - ((OverlapRate) / 100)) / Speed
  } else if (model === 'M4E') {
    return ((GSD * 3956) / 100) * (1 - ((OverlapRate) / 100)) / Speed
  } else if (model === 'M4T') {
    return ((GSD * 3023) / 100) * (1 - ((OverlapRate) / 100)) / Speed
  } else if (model === 'M3TD') {
    return ((GSD * 3023) / 100) * (1 - ((OverlapRate) / 100)) / Speed
  } else if (model === 'M3D') {
    return ((GSD * 3956) / 100) * (1 - ((OverlapRate) / 100)) / Speed
  }
}
// 机型转参数
export function modelToEnum(model) {
  if (model === 'M4TD') {
    return {
      drone_enum: '100-1',
      payload_enum: '99-0-0'
    }
  } else if (model === 'M4D') {
    return {
      drone_enum: '100-0',
      payload_enum: '98-0-0'
    }
  } else if (model === 'M4E') {
    return {
      drone_enum: '99-0',
      payload_enum: '88-0-0'
    }
  } else if (model === 'M4T') {
    return {
      drone_enum: '99-1',
      payload_enum: '89-0-0'
    }
  } else if (model === 'M3TD') {
    return {
      drone_enum: '91-1',
      payload_enum: '81-0-0'
    }
  } else if (model === 'M3D') {
    return {
      drone_enum: '91-0',
      payload_enum: '80-0-0'
    }
  }
}
// 参数转机型
export function EnumTomodel(drone_enum, payload_enum) {
  if (drone_enum === '100-1' && payload_enum === '99-0-0') {
    return 'M4TD'
  } else if (drone_enum === '100-0' && payload_enum === '98-0-0') {
    return 'M4D'
  } else if (drone_enum === '99-0' && payload_enum === '88-0-0') {
    return 'M4E'
  } else if (drone_enum === '99-1' && payload_enum === '89-0-0') {
    return 'M4T'
  } else if (drone_enum === '91-1' && payload_enum === '81-0-0') {
    return 'M3TD'
  } else if (drone_enum === '91-0' && payload_enum === '80-0-0') {
    return 'M3D'
  }
}
// 计算GSD
export function computeGSD(model, height) {
  if (model === 'M4TD') {
    return height * 0.0356
  } else if (model === 'M4D') {
    return height * 0.0269
  } else if (model === 'M4E') {
    return height * 0.0269
  } else if (model === 'M4T') {
    return height * 0.0356
  } else if (model === 'M3TD') {
    return height * 0.0352
  } else if (model === 'M3D') {
    return height * 0.0269
  }
}
// 计算高度
export function computeHeight(model, GSD) {
  if (model === 'M4TD') {
    return GSD * 100 / 3.56
  } else if (model === 'M4D') {
    return GSD * 100 / 2.69
  } else if (model === 'M4E') {
    return GSD * 100 / 2.69
  } else if (model === 'M4T') {
    return GSD * 100 / 3.56
  } else if (model === 'M3TD') {
    return GSD * 100 / 3.52
  } else if (model === 'M3D') {
    return GSD * 100 / 2.69
  }
}
// 计算朝向
export function computeAngle(list, isFlat) {
  let newList = JSON.parse(JSON.stringify(list))
  if (isFlat) {
    newList = newList.flat(1)
  }
  newList.forEach((item, index) => {
    if (index !== newList.length - 1) {
      //通过 turf 计算两个点的方向向量
      let angle = turf.rhumbBearing([newList[index][0], newList[index][1]], [newList[index + 1][0], newList[index + 1][1]])
      item.push(angle)
    } else {
      item.push(0)
    }
  })
  return newList
}
// 计算面状航线
export function computePlanarRoute(viewer, positions, interval, fly_height, angle) {
  return new Promise(async (calllback) => {
    let result = []
    let res = createPolygonBounds(positions, 3)
    let center_position = [res.center_lng, res.center_lat]
    let rotatedPoly_position = transform(positions, center_position, - 90 - angle)
    let res3 = flightLineInterval(viewer, rotatedPoly_position, interval)
    let new_rotatedPoly_position = []
    for (let i = 0; i < rotatedPoly_position.length; i += 2) {
      let array1 = [rotatedPoly_position[i], rotatedPoly_position[i + 1]]
      new_rotatedPoly_position.push(array1)
    }
    new_rotatedPoly_position.push(new_rotatedPoly_position[0])
    const polygon = turf.polygon([new_rotatedPoly_position]);
    let polygon_center = turf.centerOfMass(polygon);
    let Intersection_point_array = []
    for (let i = res3.length - 1; i >= 0; i--) {
      let line = turf.lineString([
        res3[i][0],
        res3[i][1]
      ])
      const intersections = turf.lineIntersect(line, polygon);
      if ((intersections.features.length >= 2) && (i % 2 === 0)) {
        intersections.features.reverse()
      }
      intersections.features.forEach((item, index) => {
        Intersection_point_array.push(...item.geometry.coordinates, 0)
      })
    }
    let Intersection_point_transform = transform(Intersection_point_array, center_position, 90 + angle)
    for (let i = 0; i < Intersection_point_transform.length; i += 2) {
      result.push([Intersection_point_transform[i], Intersection_point_transform[i + 1]])
    }
    const Promises = [];
    for (let i = 0; i < Intersection_point_transform.length; i += 2) {
      const promise = Cesium.sampleTerrainMostDetailed(viewer.terrainProvider, [Cesium.Cartographic.fromDegrees(Intersection_point_transform[i], Intersection_point_transform[i + 1])]);
      Promises.push(promise);
    }
    const PromisesResults = await Promise.all(Promises);
    for (let j = 0; j < PromisesResults.length; j++) {
      if (PromisesResults[j] && PromisesResults[j].length > 0) {
        result[j].push(PromisesResults[j][0].height + fly_height);
      }
    }
    let center_height = await Cesium.sampleTerrainMostDetailed(viewer.terrainProvider, [Cesium.Cartographic.fromDegrees(polygon_center.geometry.coordinates[0], polygon_center.geometry.coordinates[1])])
    result.push([polygon_center.geometry.coordinates[0], polygon_center.geometry.coordinates[1], center_height[0].height + fly_height])
    calllback(result)
  })
}
/**@method 旋转多边形
* @param {Array} positions_array - 格式为[lat,lng]的经纬度数组，多边形的顶点集合
* @param {Array} center_position - 格式为[lat,lng]的经纬度数组，旋转中点的坐标
* @param {number} angle - 旋转角度
* @return {number[]} .center - 返回旋转后的多边形点位
*/
function transform(positions_array, center_position, angle) {
  let options = { pivot: center_position };
  let new_positions_array = []
  for (let i = 0; i < positions_array.length; i += 3) {
    new_positions_array.push([positions_array[i], positions_array[i + 1]])
  }
  new_positions_array.push(new_positions_array[0])
  let poly = turf.polygon([new_positions_array]);
  let rotatedPoly = turf.transformRotate(poly, angle, options);
  let rotatedPoly_position = rotatedPoly.geometry.coordinates[0]
  rotatedPoly_position.pop()
  let new_rotatedPoly_position = rotatedPoly_position.flat()
  return new_rotatedPoly_position
}

/**@method 计算切割线
* @param {number[]} rotatedPoly_position - 格式为[lat,lng]的经纬度数组，旋转后的多边形的顶点集合
* @param {number} interval - 间隔
* @return {number[][]} .center - 返回所有的切割线
*/
function flightLineInterval(viewer, rotatedPoly_position, interval) {
  let res2 = createPolygonBounds(rotatedPoly_position, 2)
  let from = turf.point([res2.maxLng, res2.maxLat]);
  let to = turf.point([res2.maxLng, res2.minLat]);
  let distance = turf.distance(from, to) * 1000;
  let lines = Math.round(distance / interval)
  let SD = (distance - ((lines - 1) * interval)) / 2
  let right = []
  for (let i = 0; i < lines; i++) {
    let point = turf.point([res2.maxLng, res2.maxLat]);
    let distance1 = SD + (i * interval);
    let bearing = -180;
    let destination = turf.destination(point, distance1 / 1000, bearing);
    right.push(destination.geometry.coordinates)
  }
  let left = []
  for (let i = 0; i < lines; i++) {
    let point = turf.point([res2.minLng, res2.maxLat]);
    let distance1 = SD + (i * interval);
    let bearing = -180;
    let destination = turf.destination(point, distance1 / 1000, bearing);
    left.push(destination.geometry.coordinates)
  }
  let lines_position = []
  for (let i = 0; i < left.length; i++) {
    let line = [left[i], right[i]]
    lines_position.push(line)
  }
  return lines_position
}
// 加载禁飞区
export function addNotFlyZone(geojson, viewer) {
  // 禁飞区实体合集
  let nfzList = []
  // 围栏实体合集
  let dfenceList = []
  console.log(geojson.features);
  nfzList.length && nfzList.forEach((item, index) => {
    viewer.current.entities.remove(item)
  })
  nfzList = []
  dfenceList.length && dfenceList.forEach((item, index) => {
    viewer.current.entities.remove(item)
  })
  dfenceList = []
  geojson.features.forEach((item, index) => {
    if (item.geofence_type === 'nfz') {//禁飞区
      nfzList.push(item.geometry.type === 'Polygon' ?
        polygonEntity(viewer, item.id, item.geometry.coordinates, '#ff0000', 'nfz') :
        ellipseEntity(viewer, item.id, item.geometry.coordinates, '#ff0000', item.properties.radius, 'nfz'))
    } else if (item.geofence_type = "dfence") {//作业区
      dfenceList.push(item.geometry.type === 'Polygon' ?
        polygonEntity(viewer, item.id, item.geometry.coordinates, '#0000ff', 'dfence') :
        ellipseEntity(viewer, item.id, item.geometry.coordinates, '#0000ff', item.properties.radius, 'dfence'))
    }
  })
  return { nfzList, dfenceList }
}
// 添加多边形
function polygonEntity(viewer, id, coordinates, color, type) {
  let material = null
  if (type === 'nfz') {
    material = Cesium.Color.fromCssColorString(color)
  } else {
    material = new Cesium.PolylineDashMaterialProperty({
      color: Cesium.Color.fromCssColorString(color)
    })
  }
  return viewer.entities.add({
    id: id,
    name: `禁飞区`,
    polygon: {
      hierarchy: new Cesium.Cartesian3.fromDegreesArray(coordinates.flat(Infinity)),
      material: Cesium.Color.fromCssColorString(color).withAlpha(0.3),
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
    },
    polyline: {
      show: true,
      positions: new Cesium.Cartesian3.fromDegreesArray(coordinates.flat(Infinity)),
      // 宽度
      width: 3,
      // 线的颜色
      material: material,
      clampToGround: true,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
    }
  })
}
// 添加圆形
function ellipseEntity(viewer, id, coordinates, color, radius, type) {
  let material = null
  if (type === 'nfz') {
    material = Cesium.Color.fromCssColorString(color)
  } else {
    material = new Cesium.PolylineDashMaterialProperty({
      color: Cesium.Color.fromCssColorString(color)
    })
  }
  return viewer.entities.add({
    position: Cesium.Cartesian3.fromDegrees(...coordinates.flat(Infinity)),
    id: id,
    name: "围栏",
    ellipse: {
      semiMinorAxis: radius,
      semiMajorAxis: radius,
      material: Cesium.Color.fromCssColorString(color).withAlpha(0.3),
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
    },
    polyline: {
      show: true,
      positions: CircleOutline(coordinates, radius),
      // 宽度
      width: 3,
      // 线的颜色
      material: material,
      clampToGround: true,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
    }
  })
}
// 计算圆形经纬度
function CircleOutline(coordinates, radius) {
  // 创建一个圆
  var geometry = Cesium.CircleOutlineGeometry.createGeometry(new Cesium.CircleOutlineGeometry({
    center: Cesium.Cartesian3.fromDegrees(...coordinates.flat(Infinity)),
    radius: radius,
    extrudedHeight: Cesium.HeightReference.CLAMP_TO_GROUND
  }));
  let Cartesian3Array = []
  for (let i = 0; i < geometry.attributes.position.values.length; i += 3) {
    Cartesian3Array.push(new Cesium.Cartesian3(geometry.attributes.position.values[i], geometry.attributes.position.values[i + 1], geometry.attributes.position.values[i + 2]))
  }
  return Cartesian3Array
}