import React, { useState } from 'react';
import { Space, Tag, Button, message, Card, Divider } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons';
import DynamicDataTable from './index';

const TableExample = () => {
  // 模拟数据
  const [dataSource] = useState([
    {
      key: '1',
      name: '张三',
      age: 32,
      address: '北京市朝阳区',
      status: 'active',
      tags: ['开发者', '技术'],
      description: '这是一个详细描述信息...',
    },
    {
      key: '2',
      name: '李四',
      age: 28,
      address: '上海市浦东新区',
      status: 'inactive',
      tags: ['设计师'],
      description: '另一个详细描述信息...',
    },
    {
      key: '3',
      name: '王五',
      age: 35,
      address: '广州市天河区',
      status: 'active',
      tags: ['产品经理', '策划'],
      description: '第三个详细描述信息...',
    },
    {
      key: '4',
      name: '赵六',
      age: 29,
      address: '深圳市南山区',
      status: 'active',
      tags: ['运营'],
      description: '第四个详细描述信息...',
    },
    {
      key: '5',
      name: '钱七',
      age: 26,
      address: '杭州市西湖区',
      status: 'inactive',
      tags: ['测试', '质量'],
      description: '第五个详细描述信息...',
    },
  ]);

  // 列配置
  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 100,
      fixed: 'left',
    },
    {
      title: '年龄',
      dataIndex: 'age',
      key: 'age',
      width: 80,
      sorter: (a, b) => a.age - b.age,
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '活跃' : '不活跃'}
        </Tag>
      ),
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      render: (tags) => (
        <Space>
          {tags.map(tag => (
            <Tag key={tag} color="blue">{tag}</Tag>
          ))}
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => message.info(`查看 ${record.name}`)}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => message.info(`编辑 ${record.name}`)}
          >
            编辑
          </Button>
        </Space>
      ),
    },
  ];

  // 工具栏按钮配置
  const toolbarButtons = [
    {
      key: 'add',
      text: '新增',
      type: 'primary',
      icon: <PlusOutlined />,
      onClick: () => message.success('点击了新增按钮'),
    },
    {
      key: 'delete',
      text: '批量删除',
      icon: <DeleteOutlined />,
      needsSelection: true,
      selectionMessage: '请选择要删除的数据',
      onClick: (selectedKeys) => message.success(`删除了 ${selectedKeys.length} 条数据`),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <h2>DynamicDataTable 样式示例</h2>
      
      <Card title="1. 默认样式 (斑马线条纹)" style={{ marginBottom: '24px' }}>
        <p>默认无边框，带斑马线条纹，表头无分割线</p>
        <DynamicDataTable
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 800 }}
        />
      </Card>

      <Card title="2. 带边框样式" style={{ marginBottom: '24px' }}>
        <p>设置 bordered={true} 显示边框</p>
        <DynamicDataTable
          dataSource={dataSource}
          columns={columns}
          bordered={true}
          scroll={{ x: 800 }}
        />
      </Card>

      <Card title="3. 紧凑型表格" style={{ marginBottom: '24px' }}>
        <p>设置 size="small" 显示紧凑型表格</p>
        <DynamicDataTable
          dataSource={dataSource}
          columns={columns}
          size="small"
          scroll={{ x: 800 }}
        />
      </Card>

      <Card title="4. 带工具栏和选择功能" style={{ marginBottom: '24px' }}>
        <p>显示工具栏、行选择功能和选择信息</p>
        <DynamicDataTable
          dataSource={dataSource}
          columns={columns}
          showToolbar={true}
          toolbarButtons={toolbarButtons}
          showSelectionInfo={true}
          rowSelection={{}}
          scroll={{ x: 800 }}
        />
      </Card>

      <Card title="5. 带分页的表格" style={{ marginBottom: '24px' }}>
        <p>启用分页功能</p>
        <DynamicDataTable
          dataSource={dataSource}
          columns={columns}
          pagination={{
            pageSize: 3,
            total: dataSource.length,
          }}
          scroll={{ x: 800 }}
        />
      </Card>

      <Card title="6. 中等尺寸 + 边框 + 工具栏" style={{ marginBottom: '24px' }}>
        <p>组合配置示例</p>
        <DynamicDataTable
          dataSource={dataSource}
          columns={columns}
          size="middle"
          bordered={true}
          showToolbar={true}
          toolbarButtons={toolbarButtons}
          showSelectionInfo={true}
          rowSelection={{}}
          pagination={{
            pageSize: 4,
            total: dataSource.length,
          }}
          scroll={{ x: 800 }}
        />
      </Card>

      <Divider />
      
      <h3>样式说明：</h3>
      <ul>
        <li><strong>深色主题斑马线条纹：</strong>奇数行背景 #0B2222，偶数行背景 #081618</li>
        <li><strong>深色背景：</strong>表格采用深色主题设计，白色文字</li>
        <li><strong>青绿色表头：</strong>#08E7CB 背景（10%透明度），#08E7CB 文字（60%透明度）</li>
        <li><strong>悬停效果：</strong>鼠标悬停时显示深色高亮背景</li>
        <li><strong>选中效果：</strong>选中行显示深绿色背景</li>
        <li><strong>完全可配置：</strong>所有样式都可通过属性控制</li>
      </ul>
    </div>
  );
};

export default TableExample; 