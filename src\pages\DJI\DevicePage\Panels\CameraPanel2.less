.tipsBar {
  color: aqua
}

.camerasBar {
  display: flex;
  position: absolute;
  top:20px;
  left: auto;
  right: 340px;

  z-index: 1010;
  gap: 1px;
  
}

.camerasBarItem {
  padding: 4px 20px;
  color: #fff;
  font-size: 85%;
  // background: url('@/assets/img/line.png') bottom center no-repeat, linear-gradient(0deg, #253759, #33466d84);
  // background-size: auto 100%;
  background-image: url('../../../SI/assets/image/btn_primary.png');
  background-size: 100% 100%;
  position: relative;
  cursor: pointer;
  // text-shadow: #150062 2px 0 6px;
  z-index: 1001;
}

.camerasBarItem::after,
.camerasBarItem ::before {
  content: "";
  position: absolute;

}

// .camerasBarItem::after {
//   left: 0px;
//   top: 3px;
//   width:0;
// height:0;
// border-right:4px solid transparent;
// border-left:4px solid transparent;
// border-bottom:4px solid #5883d9;
// transform: rotate(-45deg);
// }
.camerasBarItem:hover{filter: brightness(1.5);} 