import { getBodyH, getDevice, getDeviceName, isEmpty } from "@/utils/utils";
import { useEffect, useState } from "react";
import {
  Card,
  List,
  Image,
  Row,
  Col,
  Descriptions,
  Divider,
  Radio,
  Table
} from "antd";

import WayLineMap from "./TaskLineMap";
import { HGet2 } from "@/utils/request";
import { timeFormat } from "@/utils/helper";
import LastPageButton from "@/components/LastPageButton";
import { getImgUrl } from "@/utils/utils";
import { GetImgItemPanel, GetVideoItemPanel } from "../MediaDataPage/helper";
import { useModel,history } from 'umi';
import { getVideoDuration, downLoadClick } from "./help";
import { getPStrArea } from "@/pages/Maps/helper.js";
import AddButton from "@/components/AddButton";
import FlightTaskFileAddForm from "../FlightFilePage/form_add";
import DownLoadBtn from "./DownLoadBtn";
import { axiosApi } from "@/services/general";
import { queryPage } from '@/utils/MyRoute';

const TaskDetailPage = ({ data2,doNotShowLastButton,noShow = false }) => {
  const [mList, setMList] = useState([]);
  const [pohtoRules, setPohtoRules] = useState([
    ".jpeg",
    ".jpg",
    ".png",
    ".gif",
    ".bmp",
    ".tiff",
    ".webp"
]);
 const [videoRules, setVideoRules] = useState([".mp4"])
 const [mediaType, setMediaType] = useState("photo"); // 当前选择的媒体类型
  const [data, setData] = useState(data2); //飞行记录详情
  const [data3, setData3] = useState({});
  const [tableData,setTableData] = useState([])
  const [pageData,setPageData] = useState({
    pageSize:4,
    page:1
  })

  const { setPage, lastPage, setModal, open, setOpen } = useModel("pageModel");

      const columns = [
      {
        title: "模型名称",
        dataIndex: "ModelName",
        key: "ModelName",
        align: "center",
      },
      {
        title: "识别项目",
        dataIndex: "Aclsname",
        key: "Aclsname",
        align: "center",
      },
      {
        title: "任务状态",
        dataIndex: 'State',
        // key: 'TaskState',
        align: "center",
          render:(value)=>{
        if(value==0){
            return <span>未执行</span>
        }else if(value==1){
          return <span style={{color:"#1C50A8"}}>执行中</span>
        }else if(value==2){
          return <span style={{color:"#16a085"}}>执行成功</span>
        }else if(value==-1){
          return <span style={{color:"red"}}>执行失败</span>
        }
        return ""
      }
      },
      {
        title: "创建时间",
        // dataIndex: "CreateTM",
        key: "CreateTM",
        align: "center",
        render: (_, record) => (
          <span>
            {isEmpty(record.CreateTime)
              ? "-"
              : timeFormat(record.CreateTime)
            }
          </span>
        )
      },
      {
        title: "结束时间",
        // dataIndex: "CreateTM",
        key: "CreateTM",
        align: "center",
        render: (_, record) => (
          <span>
            {isEmpty(record.EndTime)
              ? "-"
              : timeFormat(record.EndTime)
            }
          </span>
        )
      },
      {
        title: "操作",
        align: "center",
        render: (_, record) => (
          <a style={{color:"#17AF91"}} onClick={()=>{
              localStorage.setItem('record', JSON.stringify(record));
              localStorage.setItem('isDJPage', true);
              setPage(queryPage('识别详情'));
          }}>详情</a>
        ),
      }
      ]

  useEffect(()=>{
    console.log('mediaType====',mediaType)
  },[mediaType])

  useEffect(() => {
    const getData = async () => {
      //TaskID和ID都是飞行记录的ID，TaskID是guid形式，ID是int形式
      const yy = await HGet2(`/api/v1/Media/GetListByTaskId?id=${data.TaskID}`); //  与Media/相关的返回媒体文件
      setMList(yy);
    };
    const getData2 = async () => {
      await new Promise((resolve) => setTimeout(resolve, 500)); // 延迟 500ms
      const yy2 = await HGet2(`/api/v1/Task/GetById?id=${data2.ID}`); //  与Task/相关的返回飞行记录详情
      setData(yy2);
    };

    getData();
    getData2();
    
  }, []);

  const onPageChange = (page,pageSize)=>{
    setPageData({
      page,
      pageSize
    })
  }

  // 获取目标识别结果
  const getTargetRecognitionData =async ()=>{
      try {
          const res = await axiosApi('/api/v1/FlightTask/GetAITargetList', 'GET', {taskID:data2.TaskID});
          // const res = await axiosApi('/api/v1/FlightTask/GetAITargetList', 'GET', {taskID:"dbab2d6b-b740-4611-a77b-b490073fad57"});
          // let arr =[]
          // arr.push({...res.data[0],ID:1})
          // arr.push({...res.data[0],ID:2})
          // arr.push({...res.data[0],ID:3})
          // arr.push({...res.data[0],ID:4})
          // arr.push({...res.data[0],ID:5})
          // arr.push({...res.data[0],ID:6})
          // arr.push({...res.data[0],ID:7})
          // arr.push({...res.data[0],ID:8})
          // arr.push({...res.data[0],ID:9})
          // arr.push(res.data[1])
          if(res.data){
              setTableData(res.data)
              // setTableData(arr)
          }
      } catch (error) {   
          console.log('识别error')
      }
  }

  useEffect(()=>{
    getTargetRecognitionData()
  },[pageData])

  useEffect(() => {
    const getRecordTime = async (L2) => {
      let recordTime = 0;
      for (let i = 0; i < L2.length; i++) {
        const x1 = await getVideoDuration(getImgUrl(L2[i].ObjectName));
        recordTime = recordTime + x1;
      }
      return recordTime;
    };
    const getData3 = async () => {
      if (isEmpty(data) || isEmpty(mList)) return;
      const xx = getPStrArea(data.Remark);
      const L1 = getImgList(pohtoRules);
      const L2 = getImgList(videoRules);
      let recordTime = 0;
      recordTime = await getRecordTime(L2);
      setData3({
        FlyArea: xx,
        FlyTime: (data.FlyTM / 60).toFixed(1),
        FlyLength: data.Distance.toFixed(2),
        Media: "视频：" + L2.length + "段,照片：" + L1.length + "张",
        RecordTime: (recordTime / 60).toFixed(1),
        FImgCount: L1.length,
        FVideoCount: L2.length,
      });
    };

    getData3();
  }, [data, mList]);

  // const getImgList = (hzm) => {
  //   if (isEmpty(hzm)) {
  //     hzm = exr;
  //   }

  //   const list = [];
  //   if (isEmpty(mList)) return list;
  //   mList.forEach((e) => {
  //     if (e.FileName.indexOf(hzm) != -1) {
  //       list.push(e);
  //     }
  //   });
  //   return list;
  // };
  const getImgList = (value) => {
    if (isEmpty(mList)) return [];
    let format = mediaType === "photo" ? pohtoRules : videoRules;
    //  value 是一个数组
    if (Array.isArray(value) && value.length > 0) {
        format = value;
    }
    let list = [];
    for(let i=0;i<mList.length;i++){
      let sliceItem = (mList[i].FileName.slice(mList[i].FileName.lastIndexOf("."), mList[i].FileName.length)).toLowerCase()
      for(let j=0;j<format.length;j++){
        if(sliceItem == format[j]){
          list.push(mList[i])
        }
      }
      
    }
    return list;
};


  const dPanel = (danger) => {
    return (
      <Card title={danger.FlightLineName}>
        <Descriptions style={{ textAlign: "center", padding: 12.0 }} column={2}>
          <Descriptions.Item label="执行机场">
            {getDeviceName(danger.DeviceSN)}
          </Descriptions.Item>
          <Descriptions.Item label="开始时间">
            {timeFormat(danger.CreateTime)}
          </Descriptions.Item>
          {/* <Descriptions.Item label="结束时间">{timeFormat(danger.TaskInvokeTime)}</Descriptions.Item> */}
          <Descriptions.Item label="飞行时间">
            {(danger.FlyTM / 60).toFixed(0) + " 分钟"}
          </Descriptions.Item>
          <Descriptions.Item label="飞行距离">
            {danger.Distance.toFixed(1) + " 公里"}
          </Descriptions.Item>
          <Descriptions.Item label="拍摄文件">
            {danger.PhotoCount + " 个"}
          </Descriptions.Item>
          <Descriptions.Item label="上传文件">
            {danger.PhotoUpload + " 个"}
          </Descriptions.Item>
        </Descriptions>
      </Card>
    );
  };

  // const getImgDiv = (item, mList) => {
    
  //   if (item.FileName.indexOf(".jpeg") != -1) {
  //     return GetImgItemPanel(item, mList, setPage, lastPage);
  //   }

  //   if (item.FileName.indexOf(".mp4") != -1) {
  //     return GetVideoItemPanel(item, setModal, setOpen);
  //   }
  // };
  const getImgDiv = (item, mList) => {
    const fileName = (item.FileName.slice(item.FileName.lastIndexOf("."), item.FileName.length)).toLowerCase();
    // 判断是否是图片类型
    const isImage = pohtoRules.some(rule => fileName.endsWith(rule));
    // 判断是否是视频类型
    const isVideo = videoRules.some(rule => fileName.endsWith(rule));

    if (isImage) {
      return GetImgItemPanel(item, mList, setPage, lastPage, doNotShowLastButton);
    }
  
    if (isVideo) {
      return GetVideoItemPanel(item, setModal, setOpen, doNotShowLastButton);
    }  
  
    return null; 
  };

  const sButton = (
    <div>
      <Radio.Group
        onChange={(e) => setMediaType(e.target.value)}
        defaultValue="photo"
      >
        <Radio.Button style={{ borderRadius: "5px 0px 0px 5px" }} value="photo">
          照片
        </Radio.Button>
        <Radio.Button style={{ borderRadius: "0px 5px 5px 0px" }} value="video">
          视频
        </Radio.Button>
        <Radio.Button style={{ borderRadius: "0px 5px 5px 0px" }} value="AIRecognition">
          AI识别
        </Radio.Button>
      </Radio.Group>
    </div>
  );

  const imgPanel = (h1) => {
    return (
      <div style={{ height: "100%", width: "100%" }}>
        <List
          pagination={{
            defaultPageSize: 4,
            defaultCurrent: 1,
            showQuickJumper: true,
            pageSizeOptions: [4,10, 20, 30, 40, 50],
            showSizeChanger: true,
            style: {
              position: "absolute",
              top: null,
              bottom: 4,
              right: 10,
              height: 30,
            },
          }}
          grid={{
            column: 4,
          }}
          dataSource={getImgList()}
          renderItem={(item) => (
            <List.Item style={{ margin: 4.0, marginBottom: 24.0 }}>
              {getImgDiv(item, getImgList())}
            </List.Item>
          )}
        >
          {/* {getPanel(mList)}  */}
        </List>
      </div>
    );
  };


 const GetAiRecognitionPanel =()=>{
    return ( 
    <Col span={24}>
       <Table
          // 自定义分页
          pagination={{
            defaultPageSize: pageData.pageSize,
            defaultCurrent: pageData.page,
            // showSizeChanger: true,
            showQuickJumper: true,
            // pageSizeOptions:[4,10, 20, 30, 40, 50],
            total: tableData.length,
            onChange:onPageChange,
          }}
          rowKey={(record) => record.ID}
          bordered
          dataSource={tableData}
          columns={columns}
          size='small'
    
        /> 
    </Col>
   )
}

  const exrButton = (
    <div>
      <span>
        <DownLoadBtn record={data2}>下载图片文件</DownLoadBtn>
      </span>
      {/* <span style={{marginLeft:24.0}}><AddButton onClick={()=>{
      setModal(<FlightTaskFileAddForm fdata={{...data3}}></FlightTaskFileAddForm>)
      setOpen(true)
    }}>飞行记录归档</AddButton></span> */}
    </div>
  );

  return (
    <Card
      title={ noShow?null: doNotShowLastButton ? '飞行记录' : <LastPageButton title="飞行记录"/>}
      extra={noShow?null:exrButton}
    >
      <Row justify="space-evenly">
        <Col span={12}>
           <div style={{ height: 230,border:noShow?"1px solid white":"" }}>{dPanel(data)}</div>
        </Col>
        <Col span={12}>
          {" "}
          <div style={{ maxWidth: 420, margin: "0 auto", height: 220 }}>
            {WayLineMap(240, {
              device: getDevice(data2.DeviceSN),
              PointList: data.Remark,
            })}
          </div>
        </Col>
      </Row>
      <Row>
        {sButton}
      </Row>
      <Row style={{ height: "166px", marginTop: 24.0 }}>
        {mediaType=="AIRecognition"?GetAiRecognitionPanel(): imgPanel(getBodyH(560)) }
      </Row>
    </Card>
  );
};

export default TaskDetailPage;
