// AIModelSelector.jsx
import { useState, useEffect } from 'react';
import { Modal, List, Checkbox, Descriptions, Select, Spin, message, Radio, Table, Button } from 'antd';
import { Get2 } from '@/services/general'; // 根据实际路径调整
import './AImodelSelector.less';
import { CloseOutlined } from '@ant-design/icons';


const AIModelSelector = ({
    selectedModels,
    cls,
    onChange
}) => {
    const [modelList, setModelList] = useState([]);
    const [loading, setLoading] = useState(false);
    const [tempSelectedModels, setTempSelectedModels] = useState([]);
    const [currentEditModel, setCurrentEditModel] = useState(null);
    const [modalVisible, setModalVisible] = useState(false);
    const [tempCls, setTempCls] = useState({});
    const [Fcatalog, setFcatalog] = useState([]);
    const [selectedFcatalog, setSelectedFcatalog] = useState('目标识别'); // 默认选中目标识别
    const [Ftype, setFtype] = useState([]);
    const [selectedFtype, setSelectedFtype] = useState('图片类'); // 默认选中图片类

    // 获取模型数据
    const fetchModels = async () => {
        try {
            setLoading(true);
            const pst = await Get2('/api/v1/AI/GetModelList', {});
            setModelList(pst || []);
            if (pst && pst.length > 0) {
                const allFcatalog = [... new Set(
                    pst.flatMap(item =>
                        (item.Fcatalog || '') //处理空值
                            .split(',') // 按逗号分割
                            .map(tag => tag.trim()) // 去除空格
                            .filter(tag => tag) // 去除空值
                    )
                )];
                const allFtype = [... new Set(
                    pst.flatMap(item =>
                        (item.Ftype || '')
                            .split(',')
                            .map(tag => tag.trim())
                            .filter(tag => tag)
                    )
                )];
                setFcatalog(allFcatalog);
                setFtype(allFtype);


            }
        } catch (error) {
            message.error('模型列表加载失败');
        } finally {
            setLoading(false);
        }
    };

    // 当打开模态框时获取数据
    useEffect(() => {
        if (modalVisible) {
            fetchModels();
        }
    }, [modalVisible]);

    // 同步临时状态
    useEffect(() => {
        setTempSelectedModels(selectedModels);
        setTempCls(cls);
    }, [selectedModels, cls]);

    const handleConfirm = () => {
        onChange({
            selectedModels: tempSelectedModels,
            cls: tempCls
        });
        setModalVisible(false);
    };

    // 生成识别内容选项
    const getAList = (selectedModel) => {
        const labels = selectedModel?.AList?.split(',') || [];
        return (
            <Checkbox.Group
                value={cls[selectedModel.Guid] || []}
                onChange={values => onChange({
                    cls: { ...cls, [selectedModel.Guid]: values }
                })}
                options={labels.map((label, index) => ({
                    label,
                    value: index,
                }))}
            />
        );
    };

    const handleDeleteModel = (model) => {
        const newModels = selectedModels.filter(m => m.Guid !== model.Guid);
        const newCls = { ...cls };
        delete newCls[model.Guid];

        onChange({
            selectedModels: newModels,
            cls: newCls
        });
    };

    return (
        <Descriptions column={2} colon={false} bordered style={{ marginTop: 16 }} labelStyle={{
            width: '20%',
            paddingRight: 16
        }}
            contentStyle={{
                width: '80%'
            }}>
            <Descriptions.Item
                label="选择AI模型"
                span={24}
            >
                <Button
                    type="primary"
                    onClick={() => setModalVisible(true)}
                >
                    请选择模型
                </Button>
                <Modal
                    title="选择AI模型"
                    open={modalVisible}
                    width="50vw"
                    onCancel={() => setModalVisible(false)}
                    onOk={handleConfirm}
                >
                    <Spin spinning={loading}>
                        {/* 大类筛选区域 */}
                        <div className="filter-section">
                            <div className="filter-label">大类筛选：</div>
                            <Radio.Group
                                value={selectedFtype}
                                onChange={e => setSelectedFtype(e.target.value)}
                                optionType="button"
                                buttonStyle="solid"
                                style={{ flex: 1 }} // 占据剩余空间
                            >
                                {Ftype.map(Ftype => (
                                    <Radio.Button
                                        key={Ftype}
                                        value={Ftype}
                                        style={{ margin: 4 }}
                                    >
                                        {Ftype}
                                    </Radio.Button>
                                ))}
                            </Radio.Group>
                        </div>
                        {/* 小类筛选区域 */}
                        <div className="filter-section">
                            <div className="filter-label">小类筛选：</div>
                            <Radio.Group
                                value={selectedFcatalog}
                                onChange={e => setSelectedFcatalog(e.target.value)}
                                optionType="button"
                                buttonStyle="solid"
                                style={{ flex: 1 }} // 占据剩余空间
                            >
                                {Fcatalog.map(Fcatalog => (
                                    <Radio.Button
                                        key={Fcatalog}
                                        value={Fcatalog}
                                        style={{ margin: 4 }}
                                    >
                                        {Fcatalog}
                                    </Radio.Button>
                                ))}
                            </Radio.Group>
                        </div>
                        <div className="model-table">
                            <Table
                                rowKey="Guid"
                                dataSource={modelList.filter(model => {
                                    const modelCatalogs = (model.Fcatalog || '').split(',').map(t => t.trim()).filter(t => t)
                                    const modelTypes = (model.Ftype || '').split(',').map(t => t.trim()).filter(t => t)
                                    return modelCatalogs.includes(selectedFcatalog) && modelTypes.includes(selectedFtype)
                                })}
                                pagination={false}
                                scroll={{ y: '30vh' }}
                                columns={[
                                    {
                                        title: '模型名称',
                                        dataIndex: 'AName',
                                        width: 200,
                                        render: (text, record) => (
                                            <Checkbox
                                                checked={tempSelectedModels.some(m => m.Guid === record.Guid)}
                                                onChange={e => {
                                                    const checked = e.target.checked;
                                                    setTempSelectedModels(prev =>
                                                        checked
                                                            ? [...prev, record]
                                                            : prev.filter(m => m.Guid !== record.Guid)
                                                    );
                                                }}
                                            >
                                                {text}
                                            </Checkbox>
                                        )
                                    },
                                    {
                                        title: '识别内容',
                                        render: (_, record) => {
                                            const labels = record.AList?.split(',') || [];
                                            return (
                                                <Checkbox.Group
                                                    value={tempCls[record.Guid] || []}
                                                    onChange={values => setTempCls(prev => ({
                                                        ...prev,
                                                        [record.Guid]: values
                                                    }))}
                                                    options={labels.map((label, index) => ({
                                                        label,
                                                        value: index
                                                    }))}
                                                />
                                            );
                                        }
                                    }
                                ]}
                                rowClassName={record =>
                                    tempSelectedModels.some(m => m.Guid === record.Guid)
                                        ? 'selected-row'
                                        : ''
                                }
                            />
                        </div>
                    </Spin>
                </Modal>

            </Descriptions.Item>

            {selectedModels.length > 0 && (
                <Descriptions.Item
                    label="选择内容"
                    span={24}
                    styles={{
                        label: { width: 150, whiteSpace: 'nowrap', paddingRight: 12 },
                        content: { width: 'calc(100% - 150px)' }
                    }}
                >
                    <div className="selection-content">
                        {selectedModels.map(model => (
                            <div key={model.Guid}>
                                <div className="model-item">
                                    <span className="model-name">{model.AName}</span>
                                    <Button
                                        type="link"
                                        danger
                                        onClick={() => handleDeleteModel(model)}
                                        style={{ padding: 0 }}
                                    >
                                        <CloseOutlined />
                                    </Button>
                                </div>
                                {getAList(model)}
                            </div>
                        ))}
                    </div>
                </Descriptions.Item>
            )}
        </Descriptions>
    );
};

export default AIModelSelector;
