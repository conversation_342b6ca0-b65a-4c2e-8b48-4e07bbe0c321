import { useState, useEffect, useRef } from 'react';
import { Cesium } from "umi";
import { message } from 'antd';
import { GetCesiumViewer, computeBuffer, Cartesian3_TO_Position, computeRoute, computeInterval, PointImg3, computeAngle, computeGSD, computeIntervalTime, modelToEnum } from '@/utils/cesium_help';
const useBeltRouteHooks = (getWayline, setviewer, setModel) => {
    // 大地图viewer
    const viewer = useRef(null);
    // 鼠标移动点位置
    let move_position = useRef([]);
    // 实体集合
    let CesiumEntitys = useRef({
        positions: [],
        polyline_CesiumEntity: null,
        points: []
    });
    // cesium事件
    let handlerPoint = useRef(null)
    // 移动选中的点
    let move_entity_index = useRef(null);
    // 延时器
    let timer = useRef(null)
    // 鼠标移动方式
    let cursor = useRef('')
    // 正在拖动的航点索引
    let move_index = useRef(null)
    // 测区实体
    let area = useRef(null)
    // 航线实体
    let wayline_polyline = useRef(null);
    let wayline_position = useRef([]);
    let model = useRef('M4TD');
    // 航线信息
    let wayline = useRef({
        PList: [],
        WaylineName: '航线名称',
        WaylineType: '4',
        WanLineId: '',
        SN: null,
        GlobalSpeed: 10,
        GlobalHeadingAngle: '',
        payload_lens_index: ["visable"],
        line_string_point: [],
        shoot_time: 0,
        line_rate: 80,
        side_rate: 70,
        positions: [],
        GSD: 3.56,
        left_distance: 100,
        right_distance: 100,
        fly_height: 100,
        drone_enum: '100-1',
        payload_enum: '99-0-0'
    })
    // 禁飞区实体合集
    let nfzList = useRef([])
    // 围栏实体合集
    let dfenceList = useRef([])
    // 页面载入
    useEffect(() => {
        viewer.current = GetCesiumViewer('cesisss')
        viewer.current.cesiumWidget.screenSpaceEventHandler.removeInputAction(
            Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
        );
        viewer.current.scene.camera.setView({
            destination: Cesium.Cartesian3.fromDegrees(103.99092644903143, 30.76325334137705, 5000),
            orientation: {
                heading: 0,
                pitch: Cesium.Math.toRadians(-90),
                roll: 0,
            },
        });
        setviewer(viewer.current)
        document.getElementById('cesisss').oncontextmenu = function () {//地图上取消浏览器默认的右击事件
            return false;
        }
        // 添加cesium事件
        handlerPoint.current = new Cesium.ScreenSpaceEventHandler(viewer.current.scene.canvas)
        //鼠标事件
        LEFT_DOWN()
        LEFT_UP()
        MOUSE_MOVE()
        RIGHT_CLICK()
        return () => {
            destroy()
        };
    }, []);
    // 鼠标左键按下事件
    function LEFT_DOWN() {
        handlerPoint.current.setInputAction(function (e) {
            var pickedObject = viewer.current.scene.pick(e.position);
            if (move_entity_index.current !== null) {
                timer.current = setTimeout(() => {
                    if (pickedObject.id.point) {
                        viewer.current._container.style.cursor = "move";
                        cursor.current = "move"
                        move_index.current = Number(pickedObject.id.index)
                        viewer.current.scene.screenSpaceCameraController.enableRotate = false;//禁止旋转
                    }
                    clearTimeout(timer.current)
                    timer.current = null
                }, 80)
            } else {
                timer.current = setTimeout(() => {
                    clearTimeout(timer.current)
                    timer.current = null
                }, 80)
            }
        }, Cesium.ScreenSpaceEventType.LEFT_DOWN);
    }
    // 鼠标左键抬起事件
    function LEFT_UP() {
        handlerPoint.current.setInputAction(function (e) {
            if (timer.current) {
                clearTimeout(timer.current)
                if (move_entity_index.current === null) {
                    var ray = viewer.current.camera.getPickRay(e.position);
                    var cartesian = viewer.current.scene.globe.pick(ray, viewer.current.scene);
                    if (!cartesian) {//underfind说明地图还没加载成功
                        return
                    }
                    let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(cartesian, viewer.current)
                    CesiumEntitys.current.positions.push(longitude, latitude, height)
                    CesiumEntitys.current.points.push(viewer.current.entities.add({
                        name: `节点`,
                        index: CesiumEntitys.current.points.length,
                        position: new Cesium.Cartesian3.fromDegrees(longitude, latitude, height),
                        point: {
                            show: true,
                            pixelSize: 10,
                            color: Cesium.Color.fromCssColorString('#fff'), // 点的颜色
                            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                        },
                    }))
                    area.current && creatArea()
                    if (!CesiumEntitys.current.polyline_CesiumEntity) {
                        CesiumEntitys.current.polyline_CesiumEntity = viewer.current.entities.add({
                            polyline: {
                                show: true,
                                positions: new Cesium.CallbackProperty(() => {
                                    return new Cesium.Cartesian3.fromDegreesArrayHeights([...CesiumEntitys.current.positions, ...move_position.current]);
                                }, false),
                                // 宽度
                                width: 5,
                                // 线的颜色
                                material: Cesium.Color.fromCssColorString('#409EFF'),
                                clampToGround: true,
                                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                            },
                        })
                    }
                } else {
                }
            } else {
                console.log('长按事件');
                viewer.current._container.style.cursor = "";
                cursor.current = ""
                move_index.current = null
                viewer.current.scene.screenSpaceCameraController.enableRotate = true;//开启旋转
            }
        }, Cesium.ScreenSpaceEventType.LEFT_UP);
    }
    //鼠标移动事件
    function MOUSE_MOVE() {
        handlerPoint.current.setInputAction(function (e) {
            const cartesian = viewer.current.scene.pickPosition(e.endPosition) //获取点击位置的地理坐标
            var pickedObject = viewer.current.scene.pick(e.endPosition);
            if (!cartesian) {//underfind说明地图还没加载成功
                return
            }
            let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(cartesian, viewer.current)
            move_position.current = [longitude, latitude, height]
            if (pickedObject && pickedObject.id && pickedObject.id.name === '节点') {
                if (move_entity_index.current === null) {
                    move_entity_index.current = pickedObject.id.index
                    CesiumEntitys.current.points[move_entity_index.current].point.color = Cesium.Color.RED
                } else {
                    CesiumEntitys.current.points[move_entity_index.current].point.color = Cesium.Color.WHITE
                    move_entity_index.current = pickedObject.id.index
                    CesiumEntitys.current.points[move_entity_index.current].point.color = Cesium.Color.RED
                }
            } else {
                if (move_entity_index.current !== null) {
                    CesiumEntitys.current.points[move_entity_index.current].point.color = Cesium.Color.WHITE
                    move_entity_index.current = null
                }
            }
            if (cursor.current === "move") {
                viewer.current._container.style.cursor = "move";
                CesiumEntitys.current.points[move_index.current].position.setValue(new Cesium.Cartesian3.fromDegrees(longitude, latitude, globeHeight))
                CesiumEntitys.current.positions.splice((move_index.current * 3), 3, longitude, latitude, globeHeight)
                creatArea()
            }
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    }
    //右击事件
    function RIGHT_CLICK() {
        handlerPoint.current.setInputAction(async function (e) {
            var pickedObject = viewer.current.scene.pick(e.position);
            if (pickedObject && pickedObject.id && pickedObject.id.name === '节点') {
                delete_node(pickedObject.id.index)
            } else {
                if (CesiumEntitys.current.positions.length < 6) {
                    message.error(`带状航线至少两个点`);
                    return
                }
                viewer.current.entities.remove(CesiumEntitys.current.polyline_CesiumEntity)
                CesiumEntitys.current.polyline_CesiumEntity = viewer.current.entities.add({
                    name: `标注线`,
                    polyline: {
                        show: true,
                        positions: new Cesium.CallbackProperty(() => {
                            return new Cesium.Cartesian3.fromDegreesArrayHeights([...CesiumEntitys.current.positions]);
                        }, false),
                        // 宽度
                        width: 5,
                        // 线的颜色
                        material: Cesium.Color.fromCssColorString('#409EFF'),
                        clampToGround: true,
                        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                    }
                })
                creatArea()
            }
        }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    }
    // 销毁函数
    function destroy() {
        viewer.current.entities.removeAll();
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    }
    // 创建或更新Area
    async function creatArea() {
        let line_string = []
        let centerPosition = []
        for (let i = 0; i < CesiumEntitys.current.positions.length; i += 3) {
            line_string.push([CesiumEntitys.current.positions[i], CesiumEntitys.current.positions[i + 1], CesiumEntitys.current.positions[i + 2]])
            centerPosition.push(CesiumEntitys.current.positions[i], CesiumEntitys.current.positions[i + 1])
        }
        let res = computeBuffer(CesiumEntitys.current.positions, wayline.current.left_distance, wayline.current.right_distance)
        let interval = computeInterval(model.current, wayline.current.GSD, wayline.current.side_rate)
        let res3 = await computeRoute(viewer.current, res.leftPosition, centerPosition, res.rightPosition, interval, wayline.current.left_distance, wayline.current.right_distance, wayline.current.fly_height)
        waylineChange({
            ...wayline.current,
            positions: res.data,
            line_string_point: line_string,
            shoot_time: computeIntervalTime(model.current, wayline.current.GSD, wayline.current.line_rate, wayline.current.GlobalSpeed),
            PList: computeAngle(res3, true).map((item, index) => {
                return {
                    Index: index,
                    Lat: item[1],
                    Lng: item[0],
                    Height: item[2],
                    Action: '',
                    ActionList: [],
                    PName: ``,
                    PType: '',
                    Speed: wayline.current.GlobalSpeed,
                    IfStop: true,
                    global_height: '',
                    ground_height: '',
                    waypoint_heading_angle: item[3],
                }
            })
        })
        wayline_position.current = [...res3].flat(Infinity)
        if (!wayline_polyline.current) {
            wayline_polyline.current = viewer.current.entities.add({
                polyline: {
                    positions: new Cesium.CallbackProperty(() => {
                        return new Cesium.Cartesian3.fromDegreesArrayHeights(wayline_position.current);
                    }, false),
                    // 宽度
                    width: 3,
                    // 线的颜色
                    material: Cesium.Color.fromCssColorString('#0aed8a'),
                    clampToGround: new Cesium.CallbackProperty(() => {
                        return Cesium.Math.toDegrees(viewer.current.scene.camera.pitch) <= -80 ? true : false;
                    }, false),
                    show: new Cesium.CallbackProperty(() => {
                        return cursor.current === "move" ? false : true
                    }, false),
                },
                position: new Cesium.CallbackProperty(() => {
                    return Cesium.Cartesian3.fromDegrees(wayline_position.current[0], wayline_position.current[1], wayline_position.current[2]);
                }, false),
                billboard: {
                    image: PointImg3('s'),
                    color: Cesium.Color.WHITE,
                    scale: 1,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,  //垂直位置
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,//水平位置
                    heightReference: new Cesium.CallbackProperty(() => {
                        return Cesium.Math.toDegrees(viewer.current.scene.camera.pitch) <= -80 ? Cesium.HeightReference.CLAMP_TO_GROUND : Cesium.HeightReference.NONE;
                    }, false),
                    show: new Cesium.CallbackProperty(() => {
                        return cursor.current === "move" ? false : true
                    }, false),
                },
            })
        }
        if (!area.current) {
            area.current = viewer.current.entities.add({
                polyline: {
                    positions: new Cesium.CallbackProperty(() => {
                        return new Cesium.Cartesian3.fromDegreesArray([...wayline.current.positions, wayline.current.positions[0], wayline.current.positions[1]])
                        // return new Cesium.Cartesian3.fromDegreesArray([...res.data, res.data[0], res.data[1]])
                    }, false),
                    // 宽度
                    width: 3,
                    // 线的颜色
                    material: new Cesium.PolylineDashMaterialProperty({
                        color: Cesium.Color.fromCssColorString('#409EFF')
                    }),
                    clampToGround: true,
                    heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                },
                polygon: {
                    hierarchy: new Cesium.CallbackProperty(() => {
                        return new Cesium.PolygonHierarchy(new Cesium.Cartesian3.fromDegreesArray(wayline.current.positions))
                        // return new Cesium.PolygonHierarchy(new Cesium.Cartesian3.fromDegreesArray(res.data))
                    }, false),
                    // 宽度
                    width: 5,
                    // 线的颜色
                    material: Cesium.Color.fromCssColorString('#409EFF').withAlpha(0.3),
                    heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                },
            })
        }
    }
    // 删除节点
    function delete_node(index) {
        if (CesiumEntitys.current.points.length <= 2) {
            message.error(`带状航线至少需要两个点`);
            return
        }
        viewer.current.entities.remove(CesiumEntitys.current.points[index])
        CesiumEntitys.current.points.splice(index, 1)
        CesiumEntitys.current.positions.splice(index * 3, 3)
        CesiumEntitys.current.points.forEach((item, index) => {
            item.index = index
        })
        move_entity_index.current = null
        move_index.current = null
        creatArea()
    }
    // 航线信息改变
    function waylineChange(data) {
        wayline.current = data
        getWayline && getWayline(wayline.current)
    }
    // 修改航线信息
    function setWayline(data) {
        wayline.current = data
        getWayline && getWayline(wayline.current)
        wayline_polyline.current && area.current && creatArea()
    }
    // 加载禁飞区
    function addNotFlyZone(geojson) {
        console.log(geojson.features);
        nfzList.current.length && nfzList.current.forEach((item, index) => {
            viewer.current.entities.remove(item)
        })
        nfzList.current = []
        dfenceList.current.length && dfenceList.current.forEach((item, index) => {
            viewer.current.entities.remove(item)
        })
        dfenceList.current = []
        geojson.features.forEach((item, index) => {
            if (item.geofence_type === 'nfz') {//禁飞区
                nfzList.current.push(item.geometry.type === 'Polygon' ?
                    polygonEntity(item.id, item.geometry.coordinates, '#ff0000', 'nfz') :
                    ellipseEntity(item.id, item.geometry.coordinates, '#ff0000', item.properties.radius, 'nfz'))
            } else if (item.geofence_type = "dfence") {//作业区
                dfenceList.current.push(item.geometry.type === 'Polygon' ?
                    polygonEntity(item.id, item.geometry.coordinates, '#0000ff', 'dfence') :
                    ellipseEntity(item.id, item.geometry.coordinates, '#0000ff', item.properties.radius, 'dfence'))
            }
        })
    }
    // 添加多边形
    function polygonEntity(id, coordinates, color, type) {
        let material = null
        if (type === 'nfz') {
            material = Cesium.Color.fromCssColorString(color)
        } else {
            material = new Cesium.PolylineDashMaterialProperty({
                color: Cesium.Color.fromCssColorString(color)
            })
        }
        return viewer.current.entities.add({
            id: id,
            name: `禁飞区`,
            polygon: {
                hierarchy: new Cesium.Cartesian3.fromDegreesArray(coordinates.flat(Infinity)),
                material: Cesium.Color.fromCssColorString(color).withAlpha(0.3),
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            },
            polyline: {
                show: true,
                positions: new Cesium.Cartesian3.fromDegreesArray(coordinates.flat(Infinity)),
                // 宽度
                width: 3,
                // 线的颜色
                material: material,
                clampToGround: true,
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            }
        })
    }
    // 添加圆形
    function ellipseEntity(id, coordinates, color, radius, type) {
        let material = null
        if (type === 'nfz') {
            material = Cesium.Color.fromCssColorString(color)
        } else {
            material = new Cesium.PolylineDashMaterialProperty({
                color: Cesium.Color.fromCssColorString(color)
            })
        }
        return viewer.current.entities.add({
            position: Cesium.Cartesian3.fromDegrees(...coordinates.flat(Infinity)),
            id: id,
            name: "围栏",
            ellipse: {
                semiMinorAxis: radius,
                semiMajorAxis: radius,
                material: Cesium.Color.fromCssColorString(color).withAlpha(0.3),
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            },
            polyline: {
                show: true,
                positions: CircleOutline(coordinates, radius),
                // 宽度
                width: 3,
                // 线的颜色
                material: material,
                clampToGround: true,
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            }
        })
    }
    // 计算圆形经纬度
    function CircleOutline(coordinates, radius) {
        // 创建一个圆
        var geometry = Cesium.CircleOutlineGeometry.createGeometry(new Cesium.CircleOutlineGeometry({
            center: Cesium.Cartesian3.fromDegrees(...coordinates.flat(Infinity)),
            radius: radius,
            extrudedHeight: Cesium.HeightReference.CLAMP_TO_GROUND
        }));
        let Cartesian3Array = []
        for (let i = 0; i < geometry.attributes.position.values.length; i += 3) {
            Cartesian3Array.push(new Cesium.Cartesian3(geometry.attributes.position.values[i], geometry.attributes.position.values[i + 1], geometry.attributes.position.values[i + 2]))
        }
        return Cartesian3Array
    }
    // 飞机型号改变
    function modelChange(data) {
        model.current = data
        setModel(model.current)
        let drone = modelToEnum(model.current)
        wayline.current.drone_enum = drone.drone_enum
        wayline.current.payload_enum = drone.payload_enum
        wayline.current.GSD = computeGSD(model.current, wayline.current.fly_height)
        getWayline && getWayline(wayline.current)
    }
    // 航线回显
    function RouteFeedback(wayLineInfo) {
        wayLineInfo.line_string_point.forEach((item, index) => {
            CesiumEntitys.current.positions.push(...item)
            CesiumEntitys.current.points.push(viewer.current.entities.add({
                name: `节点`,
                index: CesiumEntitys.current.points.length,
                position: new Cesium.Cartesian3.fromDegrees(...item),
                point: {
                    show: true,
                    pixelSize: 10,
                    color: Cesium.Color.fromCssColorString('#fff'), // 点的颜色
                    heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                },
            }))
        })
        CesiumEntitys.current.polyline_CesiumEntity = viewer.current.entities.add({
            polyline: {
                show: true,
                positions: new Cesium.CallbackProperty(() => {
                    return new Cesium.Cartesian3.fromDegreesArrayHeights([...CesiumEntitys.current.positions]);
                }, false),
                // 宽度
                width: 5,
                // 线的颜色
                material: Cesium.Color.fromCssColorString('#409EFF'),
                clampToGround: true,
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
            },
        })
        wayline.current.positions = wayLineInfo.positions
        area.current = viewer.current.entities.add({
            polyline: {
                positions: new Cesium.CallbackProperty(() => {
                    return new Cesium.Cartesian3.fromDegreesArray([...wayline.current.positions, wayline.current.positions[0], wayline.current.positions[1]])
                    // return new Cesium.Cartesian3.fromDegreesArray([...res.data, res.data[0], res.data[1]])
                }, false),
                // 宽度
                width: 3,
                // 线的颜色
                material: new Cesium.PolylineDashMaterialProperty({
                    color: Cesium.Color.fromCssColorString('#409EFF')
                }),
                clampToGround: true,
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
            },
            polygon: {
                hierarchy: new Cesium.CallbackProperty(() => {
                    return new Cesium.PolygonHierarchy(new Cesium.Cartesian3.fromDegreesArray(wayline.current.positions))
                    // return new Cesium.PolygonHierarchy(new Cesium.Cartesian3.fromDegreesArray(res.data))
                }, false),
                // 宽度
                width: 5,
                // 线的颜色
                material: Cesium.Color.fromCssColorString('#409EFF').withAlpha(0.3),
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
            },
        })
        wayline_position.current = [wayLineInfo.PList.map((item, index) => {
            return [item.Lng, item.Lat, item.Height]
        })].flat(Infinity)
        wayline_polyline.current = viewer.current.entities.add({
            polyline: {
                positions: new Cesium.CallbackProperty(() => {
                    return new Cesium.Cartesian3.fromDegreesArrayHeights(wayline_position.current);
                }, false),
                // 宽度
                width: 3,
                // 线的颜色
                material: Cesium.Color.fromCssColorString('#0aed8a'),
                clampToGround: new Cesium.CallbackProperty(() => {
                    return Cesium.Math.toDegrees(viewer.current.scene.camera.pitch) <= -80 ? true : false;
                }, false),
                show: new Cesium.CallbackProperty(() => {
                    return cursor.current === "move" ? false : true
                }, false),
            },
            position: new Cesium.CallbackProperty(() => {
                return Cesium.Cartesian3.fromDegrees(wayline_position.current[0], wayline_position.current[1], wayline_position.current[2]);
            }, false),
            billboard: {
                image: PointImg3('s'),
                color: Cesium.Color.WHITE,
                scale: 1,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,  //垂直位置
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,//水平位置
                heightReference: new Cesium.CallbackProperty(() => {
                    return Cesium.Math.toDegrees(viewer.current.scene.camera.pitch) <= -80 ? Cesium.HeightReference.CLAMP_TO_GROUND : Cesium.HeightReference.NONE;
                }, false),
                show: new Cesium.CallbackProperty(() => {
                    return cursor.current === "move" ? false : true
                }, false),
            },
        })
    }
    return {
        wayline,
        setWayline,
        addNotFlyZone,
        modelChange,
        RouteFeedback,
        waylineChange
    }
}
export default useBeltRouteHooks;