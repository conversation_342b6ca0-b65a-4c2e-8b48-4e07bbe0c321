import { Space, Tag, message, Modal, Switch, Badge, Image, Alert } from "antd";
import { downloadFile, getImgUrl, isEmpty } from "@/utils/utils";
import { timeFormat } from "@/utils/helper";
import { axiosApi } from "@/services/general";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import { useModel } from "umi";
const getTableTitle = (title) => {
  return (
    <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
  );
};
const TableCols = (currentTablePage) => {

  return [
    {
      title: getTableTitle("照片组"),
      key: "serial",
      align: "center",
      render: (text, record, index) => {
        // 当前页码从父组件传递过来
        return (currentTablePage - 1) * 7 + index + 1; 
      }

    },
    {
      title: getTableTitle("人"),
      dataIndex: "人",
      key: "人",
      align: "center",
    },
    {
      title: getTableTitle("行人"),
      dataIndex: "行人",
      key: "行人",
      align: "center",
    },
    {
      title: getTableTitle("摩托车"),
      dataIndex: "摩托车",
      key: "摩托车",
      align: "center",
    },
    {
      title: getTableTitle("汽车"),
      dataIndex: "汽车",
      key: "汽车",
      align: "center",
    },
    {
      title: getTableTitle("面包车"),
      dataIndex: "面包车",
      key: "面包车",
      align: "center",
    },
  ];
};

export default TableCols;
