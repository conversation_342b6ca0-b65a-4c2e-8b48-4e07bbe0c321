import React, { useEffect, useState, useMemo } from 'react';
import styles from '../index.less';
import { axiosApi, Get2 } from "@/services/general";
import { Card } from "antd";
import { useModel } from "umi";
import LastPageButton from "@/components/LastPageButton";
import DangerDetailPage from "@/pages/DJI/DangerPage/detail";



// 巡飞统计卡片组件
const FlightStatCard = ({ label, value, unit, className }) => (
  <div className={`${styles.flightStatCard} ${className || ''}`}>
    <div className={styles.flightStatValue}>
      {value}
    </div>
    {unit && <span className={styles.flightStatUnit}></span>}
    <div className={styles.flightStatLabel}>{label}</div>
  </div>
);

// 影像统计项组件
const ImageStatItem = ({ label, value, color }) => (
  <div className={styles.imageStatItem}>
    <div className={styles.imageStatColor} style={{ backgroundColor: color }}></div>
    <span className={styles.imageStatLabel}>{label}</span>
    <span className={styles.imageStatValue}>{value}</span>
  </div>
);

// 三层环形饼状图组件
const CHART_CONFIG = {
  outerRadius: 60,      // 最外层彩色环外径
  middleRadius: 50,     // 最外层彩色环内径
  separatorRadius: 50,  // 中间分隔环外径
  innerRadius: 45,      // 核心区域半径
  centerX: 70,
  centerY: 70,
  gapAngle: 2,          // 间隙角度
  startAngle: -90,      // 从顶部开始
  svgSize: 140
};

const PieChart = ({ data, showPercentage = true, emptyText = '暂无数据' }) => {
  // 确保数据始终是数组
  const validData = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return [];
    }
    return data;
  }, [data]);

  // 计算总数和验证
  const total = useMemo(() => {
    if (validData.length === 0) {
      return 0;
    }
    return validData.reduce((sum, item) => {
      const value = typeof item.value === 'number' ? item.value : 0;
      return sum + Math.max(0, value); // 确保值不为负数
    }, 0);
  }, [validData]);

  // 计算路径数据
  const pathsData = useMemo(() => {
    if (validData.length === 0 || total === 0) {
      return [];
    }

    // 过滤掉值为0的数据项，避免产生不必要的间隙
    const nonZeroData = validData.filter(item => {
      const value = Math.max(0, item.value || 0);
      return value > 0;
    });

    if (nonZeroData.length === 0) {
      return [];
    }

    const {
      outerRadius,
      middleRadius,
      centerX,
      centerY,
      gapAngle,
      startAngle
    } = CHART_CONFIG;

    let currentAngle = startAngle;
    const totalGapAngle = nonZeroData.length * gapAngle;
    const availableAngle = 360 - totalGapAngle;

    return nonZeroData.map((item, index) => {
      const value = Math.max(0, item.value || 0);
      const percentage = value / total;
      const angle = percentage * availableAngle;
      const itemStartAngle = currentAngle;
      const itemEndAngle = currentAngle + angle;

      currentAngle += angle + gapAngle;

      // 转换为弧度
      const startAngleRad = (itemStartAngle * Math.PI) / 180;
      const endAngleRad = (itemEndAngle * Math.PI) / 180;

      // 计算外圆弧点
      const x1Outer = centerX + outerRadius * Math.cos(startAngleRad);
      const y1Outer = centerY + outerRadius * Math.sin(startAngleRad);
      const x2Outer = centerX + outerRadius * Math.cos(endAngleRad);
      const y2Outer = centerY + outerRadius * Math.sin(endAngleRad);

      // 计算内圆弧点
      const x1Inner = centerX + middleRadius * Math.cos(startAngleRad);
      const y1Inner = centerY + middleRadius * Math.sin(startAngleRad);
      const x2Inner = centerX + middleRadius * Math.cos(endAngleRad);
      const y2Inner = centerY + middleRadius * Math.sin(endAngleRad);

      const largeArcFlag = angle > 180 ? 1 : 0;

      const pathData = [
        `M ${x1Inner} ${y1Inner}`,
        `L ${x1Outer} ${y1Outer}`,
        `A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${x2Outer} ${y2Outer}`,
        `L ${x2Inner} ${y2Inner}`,
        `A ${middleRadius} ${middleRadius} 0 ${largeArcFlag} 0 ${x1Inner} ${y1Inner}`,
        'Z'
      ].join(' ');

      return {
        pathData,
        color: item.color || '#ccc',
        name: item.name || `项目${index + 1}`,
        value,
        percentage: (percentage * 100).toFixed(1)
      };
    });
  }, [validData, total]);

  // 处理空数据或总数为0的情况
  if (validData.length === 0) {
    return (
      <div className={styles.pieChartContainer}>
        <div className={styles.emptyState}>{emptyText}</div>
      </div>
    );
  }

  if (total === 0) {
    return (
      <div className={styles.pieChartContainer}>
        <div className={styles.emptyState}>影像总计为0</div>
      </div>
    );
  }

  const {
    centerX,
    centerY,
    separatorRadius,
    innerRadius,
    svgSize
  } = CHART_CONFIG;

  return (
    <div className={styles.pieChartContainer}>
      <svg
        width={svgSize}
        height={svgSize}
        viewBox={`0 0 ${svgSize} ${svgSize}`}
        role="img"
        aria-label={`饼图显示${validData.length}个数据项，总计${total}`}
      >
        {/* 定义背景图片pattern */}
        <defs>
          <pattern id="imageStatsPattern" patternUnits="objectBoundingBox" width="1" height="1">
            <image
              href={require('../../assets/image/低空监控/影像统计.png')}
              x="0"
              y="0"
              width="90"
              height="90"
              preserveAspectRatio="xMidYMid slice"
            />
          </pattern>
        </defs>

        {/* 最外层彩色分段环 */}
        {pathsData.map((item, index) => (
          <path
            key={`${item.name}-${index}`}
            d={item.pathData}
            fill={item.color}
            stroke="none"
            strokeWidth="0"
            role="img"
            aria-label={`${item.name}: ${item.value}${showPercentage ? ` (${item.percentage}%)` : ''}`}
            style={{ cursor: 'pointer' }}
            onMouseEnter={(e) => {
              e.target.style.opacity = '0.8';
            }}
            onMouseLeave={(e) => {
              e.target.style.opacity = '1';
            }}
          >
            <title>{`${item.name}: ${item.value}${showPercentage ? ` (${item.percentage}%)` : ''}`}</title>
          </path>
        ))}

        {/* 中间层：深色分隔环 */}
        <circle
          cx={centerX}
          cy={centerY}
          r={separatorRadius}
          fill="#1a1a1a"
          stroke="none"
        />

        {/* 最内层：核心显示区域 */}
        <circle
          cx={centerX}
          cy={centerY}
          r={innerRadius}
          fill="url(#imageStatsPattern)"
          stroke="rgba(26, 188, 156, 0.3)"
          strokeWidth="1"
        />
      </svg>
      
      {/* 中心文字 */}
      <div className={styles.pieChartCenter}>
        <div className={styles.pieChartTotal} aria-live="polite">
          {total}
        </div>
        <div className={styles.pieChartLabel}>影像总计</div>
      </div>
    </div>
  );
};

const formatDateTime = (dateStr) => {
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) throw new Error('Invalid date');
    
    return new Intl.DateTimeFormat('sv-SE', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).format(date);
  } catch (error) {
    console.error('Date formatting error:', error);
    return null;
  }
};

const MissionItem = ({ name, createTM, status, item, onDetailClick }) => {
  // 根据状态级别确定样式类名
  const getStatusClass = (status) => {
    if (status?.includes('紧急') || status?.includes('严重')) return 'urgent';
    if (status?.includes('警告') || status?.includes('注意')) return 'warning';
    return 'normal';
  };

  return (
    <div className={`${styles.missionItem} ${styles[getStatusClass(status)]}`}>
      {/* 单行布局：事件名称、时间信息、详情按钮 */}
      <div className={styles.missionMainRow}>
        <span className={styles.missionName} title={name}>
          {name}
        </span>
        <span className={styles.missionTime} title={formatDateTime(createTM)}>
          {formatDateTime(createTM)}
        </span>
        <span
          className={styles.missionDetail}
          title="查看事件详情"
          onClick={() => onDetailClick(item)}
        >
          详情
        </span>
      </div>
    </div>
  );
};

const RightSidebar = ({ collapsed }) => {
  const sidebarWidth = 380; // Fixed expanded width
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [flightTaskCount, setFlightTaskCount] = useState({}); // 巡飞统计数据对象
  const [mediaStatistics, setMediaStatistics] = useState({}); // 影像统计数据对象
  const { setPage } = useModel('pageModel');

  const handleDetailClick = (item) => {
    setPage(
      <Card title={<LastPageButton title="事件详情" />}>
        <DangerDetailPage danger2={item}></DangerDetailPage>
      </Card>
    );
  };

  // 获取巡查记录表格数据
  const getRecords = async () => {
    try {
      setLoading(true);

      // 获取待处理事件列表
      const penddingEventLists = await axiosApi('/api/v1/Danger/GetAllListByTable', 'GET', {
          state: 0
        })

      // 确保penddingEventLists.data存在，且为数组类型
      if (!Array.isArray(penddingEventLists.data)) {
        return;
      }

      setDataSource(penddingEventLists.data);
      setLoading(false);
    } catch (error) {
      console.log('Error in getRecords:', error);
      setLoading(false);
    }
  };

  // 获取巡飞统计数据
  const getFlightTaskCount = async () => {
    try {
      const res = await axiosApi('/api/v1/FlightTask/GetFlightTaskCount', 'GET', {});
      return res?.data || {};
    } catch (error) {
      console.log('Error in getFlightTaskCount:', error);
      return {};
    }
  };

  // 获取影像统计数据
  const getMediaStatistics = async () => {
    try {
      const res = await axiosApi('/api/v1/Media/GetMediaStatistics', 'GET', {});
      return res?.data || {};
    } catch (error) {
      console.log('Error in getMediaStatistics:', error);
      return {};
    }
  };

  useEffect(() => {
    getRecords();
    getFlightTaskCount().then(data => setFlightTaskCount(data)); // 获取巡飞统计数据
    getMediaStatistics().then(data => setMediaStatistics(data)); // 获取影像统计数据
  }, []);

  useEffect(() => {
    document.documentElement.style.setProperty('--right-sidebar-width', `${sidebarWidth}px`);
    if (collapsed) {
      document.documentElement.style.setProperty('--right-sidebar-width', `0px`);
    }
  }, [collapsed]);

  return (
    // <div className={`${styles.rightContainer}`}>
    <div
      className={`${styles.sidebar} ${styles.rightSidebar} ${collapsed ? styles.isCollapsed : ''}`}
      style={{ width: `${sidebarWidth}px` }} // 移除overflow auto，使用CSS控制
    >
      {true && (
        <>
          <div className={styles.section}>
            <div className={styles.sectionTitle}>巡飞统计</div>
            <div className={`${styles.sectionContent} ${styles.flightStatsContainer}`}>
              <FlightStatCard
                label="当日任务"
                value={flightTaskCount.dayCount}
                unit="个"
                className={styles.patrolDaysStat}
              />
              <FlightStatCard
                label="当月任务"
                value={flightTaskCount.monthCount}
                unit="个"
                className={styles.patrolTimesStat}
              />
              <FlightStatCard
                label="当年任务"
                value={flightTaskCount.yearCount}
                unit="个"
                className={styles.patrolMileageStat}
              />
            </div>
          </div>

          <div className={`${styles.section} ${styles.imageStatsSection}`}>
            <div className={styles.sectionTitle}>影像统计</div>
            <div className={`${styles.sectionContent} ${styles.newImageStatsContainer}`}>
              <div className={styles.pieChartSection}>
                <PieChart
                  data={[
                    { name: '图片', value: mediaStatistics.imageCount, color: '#00D4AA' }, // 图片 - 青色
                    { name: '视频', value: mediaStatistics.videoCount, color: '#52C41A' }, // 视频 - 绿色
                    { name: '二维模型', value: mediaStatistics.model2DCount, color: '#FF7A45' },  // 二维模型 - 橙色
                    { name: '三维模型', value: mediaStatistics.model3DCount, color: '#F759AB' }    // 三维模型 - 粉色
                  ]}
                />
              </div>
              <div className={styles.imageStatsLegend}>
                <ImageStatItem label="图片" value={mediaStatistics.imageCount} color="#00D4AA" />
                <ImageStatItem label="视频" value={mediaStatistics.videoCount} color="#52C41A" />
                <ImageStatItem label="二维模型" value={mediaStatistics.model2DCount} color="#FF7A45" />
                <ImageStatItem label="三维模型" value={mediaStatistics.model3DCount} color="#F759AB" />
              </div>
            </div>
          </div>

          <div className={styles.section + ' ' + styles.missionSection}>
            <div className={styles.sectionTitle}>
              异常告警
              {(loading) && <span style={{fontSize: '12px', color: '#999', marginLeft: '8px'}}>加载中...</span>}
            </div>  
            <div className={`${styles.sectionContent} ${styles.missionList}`}>
              {dataSource.map(item => {
                return (
                  <MissionItem
                    key={item.Guid}
                    name={item.Title}
                    createTM={item.CreateTM}
                    status={item.Level}
                    item={item}
                    onDetailClick={handleDetailClick}
                    // status={
                    //   item.status === 0 ? "未执行" :
                    //     item.status === 1 ? "执行中" :
                    //       item.status === 2 ? "执行成功" :
                    //         item.status === -1 ? "执行失败" : "未知状态"
                    // }
                  />
                )
              })}

            </div>
          </div>
        </>
      )}
    </div>
    // </div>
  );
};

export default RightSidebar;