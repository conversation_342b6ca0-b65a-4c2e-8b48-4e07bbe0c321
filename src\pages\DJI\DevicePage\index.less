.XXX {
    position: absolute;
    width: 260px;
    height: 280px;
    top: 20px;
    left: 20px;
    // background:rgba(0,0,255,0.2);
}

.container {
    position: relative;
    width: 100%;
    height: 100%;
}

.sideTool {
    position: absolute;
    width: 300px;
    height: 240px;
    top: 20px;
    right: 8px;
    z-index: 1000;
}
// .sideTool {
//     position: absolute;
//     // width: 300px;
//     // height: 240px;
//     top: 8px;
//     right: 8px;
//     z-index: 1000;
//     // cursor: ew-resize;
//     cursor: nesw-resize;
//     -webkit-user-select: none;
//     -ms-user-select: none;
//     user-select: none;
// }

.sideToolHead {
    height: 30px;
    position: relative;
    z-index: 1000;
    width: 100%;
    background: #091A1B;
    background-image: url('../../SI/assets/image/btn_bg.png');
    background-size: 100% 100%;
    border: 1px solid #08E7CB;
    border-image: linear-gradient(45deg, #00d8ff, #006fe2) 1 1;
    display: flex;
    padding: 1px;
    // box-shadow: #000 0 0 0 1px inset, #487dc7 0 0 4px inset;
    margin-bottom:5px;

}

.sideToolHead>button {
    height: 100% !important;
    line-height: 1;
    flex: 1;
}

.sideToolHead>button>span {
    color: rgba(8, 231, 203, 1);
    font-size: 13px;
}

.sideToolHead>button+button {
    border-left: #2c5084 solid 1px
}

.sideToolHead>button:hover {
    background: linear-gradient(0deg, #1c458d, transparent);
}

// .sideTool>div:last-child {
//     background: rgb(10, 27, 70, .25);
//     border: 1px solid;
//     border-image: linear-gradient(45deg, #00d8ff, #006fe2) 1 1;
//     padding: 1px;
//     box-shadow: #000 0 0 0 1px inset, #487dc7 0 0 4px inset;
    
// }

.jg {
    background: 'white';
    height: 36;
    margin: 12;
}

.FlexboxContainer {
    display: flex;
    flex-direction: column;
    gap: 10px;
}


.IndexPageStyle {
    margin: 0;
    //background: url('./../../../assets/mlbg.png') center center no-repeat;
    //  background-color: red;
    background-size: cover;
    width: 100%;
    height: 100%;
    position: relative
}

.headBar+div {
    height: calc(100vh - 65px) !important;
}

.shadow {
    // display:none;
    box-shadow: #000 0 0 100px inset;
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    z-index: -1;
    left: 0;
    pointer-events: none;
    // background: url('./../../../assets/img/shadow.png') center center no-repeat;
    background: url('@/assets/img/shadow2.png') center center no-repeat;
    background-size: 100% 110%;
}

.shadow::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 38px;
    background: url('@/assets/img/bg-foot.png') top center no-repeat;
    background-size: auto 100%;
    left: 0;
    bottom: 0;
}

.camerasBar {
  display: flex;
  position: absolute;
  top:20px;
  left: 300px;
  z-index: 1010;
  gap: 1px;
  
}
.camerasBarItem {
  padding: 6px 20px;
  color: #fff;
  font-size: 85%;
  background-image: url('../../SI/assets/image/btn_yellow.png');
  background-size: 100% 100%;
  position: relative;
  cursor: pointer;
  z-index: 1001;
}