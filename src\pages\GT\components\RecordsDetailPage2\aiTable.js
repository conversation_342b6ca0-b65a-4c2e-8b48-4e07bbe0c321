import { Space, Tag, message, Modal, Switch, Badge, Image, Alert, InputNumber, Popover, } from "antd";
import { timeFormat } from "@/utils/helper";
import { downloadFile, getImgUrl, isEmpty } from "@/utils/utils";
import { axiosApi } from "@/services/general";
import MyButton from "@/pages/GT/components/MyButton/MyButton";
import { useModel } from "umi";
import { useState, useEffect } from 'react';
import { queryPage, } from '@/utils/MyRoute';

const getTableTitle = (title) => {
    return (
        <div style={{ fontWeight: "bold", textAlign: "center" }}> {title}</div>
    );
};
const aiTableCols = () => {

    const { setPage, } = useModel("pageModel");

    const statusMap = {
        0: { color: 'default', text: '未开始' },
        1: { color: 'processing', text: '进行中' },
        2: { color: 'success', text: '已完成' },
        3: { color: 'error', text: '已取消' },
        '-1': { color: 'error', text: '执行失败' }
    };

    return [
        {
            title: getTableTitle("模型名称"),
            dataIndex: "ModelName",
            key: "ModelName",
            align: "center",
        },
        {
            title: getTableTitle("识别项目"),

            key: "Aclsname",
            align: "center",
            render: (_, record) => (
                record.Aclsname ? (
                    <Space wrap>
                        {record.Aclsname.split(/,\s?/).map((item, index) => (
                            <Tag
                                color="#666666"
                                style={{ margin: 2 }}
                                key={index}
                            >
                                {item.trim()}
                            </Tag>
                        ))}
                    </Space>
                ) : null
            )
        },
        {
            title: getTableTitle("任务状态"),
            // dataIndex: "State",
            key: "State",
            align: "center",
            render: (_, record) => {
                const { color, text } = statusMap[record.State] || {};
                return text ? (
                    <Tag color={color}>{text}</Tag>
                ) : (
                    <span>-</span>
                );
            }
        },
        {
            title: getTableTitle("识别结果"),
            dataIndex: "Content",
            key: "Content",
            align: "center",
        },
        {
            title: getTableTitle("操作"),
            align: "center",
            render: (_, record) => (
                <Space size="middle">

                    <MyButton
                        style={{ padding: "2px 8px", color: '#17AF91', background: 'none' }}
                        onClick={() => {
                            localStorage.setItem('record', JSON.stringify(record));
                            setPage(queryPage('识别详情'))
                        }}
                    >
                        详情
                    </MyButton>

                </Space>
            ),
        }
    ];
};

export default aiTableCols;
