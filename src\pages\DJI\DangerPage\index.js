import React, { useState, useEffect } from "react";
import { Card, Button, Table } from "antd";
import { getBodyH, isEmpty } from "@/utils/utils";
import { Get2 } from "@/services/general";
import TableCols from "./table";
import DangerDetailPage from "./detail";
import { useModel } from "umi";
import LastPageButton from "@/components/LastPageButton";
import ComStyles from "@/pages/common.less";

const DangerListPage = (props) => {
  const [DangerList, setDangerList] = useState({});
  const { setOpen, setPage } = useModel("pageModel");
  const [pageSize, setPageSize] = useState(3); // 初始每页显示条数

  const handleShowSizeChange = (current, newSize) => {
    setPageSize(newSize);
  };
  
  useEffect(() => {
    const getDangerData = async () => {
      const pst = await Get2("/api/v1/Danger/GetAllList", {});
      setDangerList(pst);
    };
    getDangerData();
  }, []);

  const showMap = (values) => {
    setPage(
      <Card bordered={false} title={<LastPageButton title="事件详情" />}>
        <DangerDetailPage danger2={values}></DangerDetailPage>
      </Card>
    );
  };

  const refrush = async () => {
    setOpen(false);
    const pst = await Get2("/api/v1/Danger/GetAllList", {});
    setDangerList(pst);
  };

  const exr = (
    <Button
      className={ComStyles.addButton}
      type="primary"
      onClick={() => refrush()}
    >
      刷新
    </Button>
  );
  return (
    <div style={{ margin: 0, height: getBodyH(56) }}>
      <Card
        title={<LastPageButton title="事件列表"></LastPageButton>}
        bordered={false}
        extra={exr}
      >
        <div>
          {isEmpty(DangerList) ? (
            <div />
          ) : (
            <Table
              pagination={{
                pageSize: pageSize,
                showSizeChanger: true,
                hideOnSinglePage: true,
                pageSizeOptions: ["3", "5", "10", "20"],
                hideOnSinglePage: true,
                onShowSizeChange: handleShowSizeChange, 
              }}
              bordered
              dataSource={DangerList}
              columns={TableCols(refrush, showMap)}
              size="small"
              scroll={{ y: getBodyH(276), x: "auto" }}
            />
          )}
        </div>
      </Card>
    </div>
  );
};

export default DangerListPage;
