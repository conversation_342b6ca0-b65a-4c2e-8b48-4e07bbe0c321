import { Card, Table, Button, message } from "antd";
import TableCols from "./table";
import { axiosApi } from "@/services/general";
import { useState, useEffect } from "react";

const TempLandPage = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [currentTablePage, setCurrentTablePage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const exr = (
    <div>
      <Button type="primary" onClick={() => console.log("新增")}>
        新增
      </Button>{" "}
    </div>
  );

  // 获取临时用地列表
  const getTempLandList = async () => {
    try {
      setLoading(true);
      const res = await axiosApi("/api/v1/Lsyd/GetList", "GET", null);
      setDataSource(res.data);
      setLoading(false);
      console.log("@res.data", res.data);
    } catch (err) {
      message.error("获取数据失败");
    }
  };

  // 挂载完成 获取初始数据
  useEffect(() => {
    getTempLandList();
  }, []);

  // 当数据变化时重置页码
  useEffect(() => {
    setCurrentTablePage(1);
  }, [dataSource]);

  return (
    <div className="blackBackground">
      <Card
        title={"临时用地管理"}
        extra={exr}
        style={{
          height: "100%",
          display: "flex",
          flexDirection: "column",
          flex: 1,
          overflow: "hidden",
          padding: 0,
        }}
      >
        <div
          style={{
            flex: 1,
            overflow: "hidden",
            padding: "24px",
          }}
        >
          <Table
            style={{ height: "100%" }}
            pagination={{
              defaultPageSize: pageSize,
              current: currentTablePage,
              pageSizeOptions: [10, 20, 40, 60, 80],
              showSizeChanger: true,
              onShowSizeChange: (current, size) => {
                setPageSize(size);
                setCurrentTablePage(1); 
              },
              onChange: (page) => setCurrentTablePage(page),
              total: dataSource.length,
              locale: {
                items_per_page: "条/页",
                jump_to: "跳至",
                page: "页",
              },
              showTotal: (total) => (
                <span className="custom-pagination-text">
                  共 {total} 条 第 {currentTablePage} /{" "}
                  {Math.ceil(total / pageSize)} 页
                </span>
              ),
              className: "custom-pagination-container",
            }}
            rowKey={(record) => record.id}
            loading={loading}
            bordered
            dataSource={dataSource}
            columns={TableCols()}
            size="small"
            scroll={{
              scrollToFirstRowOnChange: true,
              y: `calc(100vh - 310px)`,
              // y:600,
            }}
          />
        </div>
      </Card>
    </div>
  );
};
export default TempLandPage;
