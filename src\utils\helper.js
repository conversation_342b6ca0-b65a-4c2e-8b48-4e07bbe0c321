/* eslint-disable guard-for-in */
import { history } from "umi";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { isEmpty } from "lodash";
import { MainColor } from "./colorHelper";
import { Get, Get2, Post, Post2, Post3 } from "@/services/general";

dayjs.extend(utc);

export function AtoB(A, B, a) {
  let b = 0;
  let i = 0;

  for (i = 0; i < A.length - 1; i += 1) {
    if ((a - A[i]) * (a - A[i + 1]) <= 0) {
      b = B[i] + (B[i + 1] - B[i]) * ((a - A[i]) / (A[i + 1] - A[i]));
      return b;
    }
  }

  if (a < A[0]) {
    i = 0;
    b = B[i] + (B[i + 1] - B[i]) * ((a - A[i]) / (A[i + 1] - A[i]));
  }

  if (a > A[A.length - 1]) {
    i = A.length - 2;
    b = B[i] + (B[i + 1] - B[i]) * ((a - A[i]) / (A[i + 1] - A[i]));
  }

  return b;
}

export function ChaZhi2(a1, a2, y1, y2, y3, y4) {
  return (
    (1 - a1) * (1 - a2) * y1 +
    a1 * (1 - a2) * y2 +
    (1 - a1) * a2 * y3 +
    a1 * a2 * y4
  );
}

export function ChaZhi1(a1, y1, y2) {
  return a1 * y2 + (1 - a1) * y1;
}

export function Round(v, i) {
  const x = Number(v);
  if (x === -1) return "-";
  return x.toFixed(i);
}

export function BtoA(A, B, b) {
  let i = 0;
  let a = 0;

  for (i = 0; i < A.length - 1; i += 1) {
    if ((b - B[i]) * (b - B[i + 1]) <= 0) {
      a = A[i] + (A[i + 1] - A[i]) * ((b - B[i]) / (B[i + 1] - B[i]));
      return a;
    }
  }

  if (b < B.first) {
    i = 0;
    a = A[i] + (A[i + 1] - A[i]) * ((b - B[i]) / (B[i + 1] - B[i]));
  }

  if (b > B.last) {
    i = A.length - 2;
    a = A[i] + (A[i + 1] - A[i]) * ((b - B[i]) / (B[i + 1] - B[i]));
  }

  return a;
}

export function arrIndex(arr, key, value) {
  for (let i = 0; i < arr.length; i += 1) {
    // 遍历每个数组的对象元素的某个key值和value进行对比，相等返回当前元素下标，不等的话，返回null
    if (arr[i][key] === value) {
      return i;
    }
  }
  return null;
}

export function getGuid() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    // eslint-disable-next-line no-var
    // eslint-disable-next-line eqeqeq
    // eslint-disable-next-line no-bitwise
    const r = (Math.random() * 16) | 0;
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

export function getStrValue(obj, x) {
  if (isEmpty(obj)) return "";
  if (obj[x] === -1) return "-";
  return obj[x];
}

export function getBaseColor() {
  return MainColor;
  return "rgba(0,0,139,0.7)";

  return "rgba(24,144,255,0.7)";
}

export async function getLocation(lat, lng) {
  return "";
  const url = `https://restapi.amap.com/v3/geocode/regeo?output=json&location=${lng},${lat}&key=0148ec4dda4b1e2f06290949838573d1`;
  const xx = await Post3("/api/v2/Debug/Proxy", url);
  console.log("getLocation", xx);
  if (xx.status != "1") {
    return "";
  }
  if (isEmpty(xx.regeocode.addressComponent.neighborhood)) return "";

  const xx2 = xx.regeocode.formatted_address;
  const xx4 = xx.regeocode.addressComponent.neighborhood.name;

  const xx3 = xx2.substring(xx2.indexOf("市") + 1, xx2.length);
  console.log("getLocation", xx4);
  return xx4;
}

export function DateFormat5(fmt, date) {
  let ret;
  const opt = {
    "Y+": date.getFullYear().toString(), // 年
    "m+": (date.getMonth() + 1).toString(), // 月
    "d+": date.getDate().toString(), // 日
    "H+": date.getHours().toString(), // 时
    "M+": date.getMinutes().toString(), // 分
    "S+": date.getSeconds().toString(), // 秒
    // 有其他格式化字符需求可以继续添加，必须转化成字符串
  };
  // eslint-disable-next-line no-restricted-syntax
  for (const k in opt) {
    ret = new RegExp(`(${k})`).exec(fmt);
    if (ret) {
      // eslint-disable-next-line no-param-reassign
      fmt = fmt.replace(
        ret[1],
        ret[1].length === 1 ? opt[k] : opt[k].padStart(ret[1].length, "0")
      );
    }
  }
  return fmt;
}

/// RFC3339
export function dateFormat(str) {
  const date = dayjs.utc(str).add(0, "hour");

  return date.format("YYYY-MM-DD");
}

export function DateFormat(str, format) {
  const date = dayjs.utc(str).add(0, "hour");

  return date.format(format);
}

export function timeFormat(str) {
  // const date = dayjs(str).add(0, 'hour')
  // return date.format("YYYY/MM/DD HH:mm")
  if (!str || typeof str !== "string") {
    return ""; 
  }
  if (!str.includes("T")) {
    return formatMillisecondsToDate(str);
  }
  const date = new Date(str);
  const formattedDate = date
    .toISOString()
    .slice(0, 19)
    .replace("T", " ")
    .replace(/-/g, "/");
  return formattedDate;
}

export function timeFormatPY(str, py) {
  const date = dayjs.utc(str).add(py, "hour");
  // const date = dayjs(str)
  return date.format("YYYY/MM/DD HH:mm");
}

export function timeFormat3(str) {
  const date = dayjs.utc(str).add(0, "hour");
  //const date = dayjs(str)
  return date.format("YYYYMMDDHHmm");
}

export function timeFormat2(str) {
  const date = dayjs.utc(str).add(8, "hour");
  return date.format("YYYY/MM/DD HH:mm");
}

export function timeFormat22(str) {
  const date = dayjs.utc(str).add(8, "hour");
  return date.format("YYYY/MM/DD HH:mm");
}

export function timeFormat5(str) {
  const date = dayjs.utc(str).add(0, "hour");
  return date.format("YYYY-MM-DD");
}

export function dateFormat2(str) {
  const date = new Date(str).toJSON();
  const newDate = new Date(+new Date(date) + 0 * 3600 * 1000)
    .toISOString()
    .replace(/T/g, " ")
    .replace(/\.[\d]{3}Z/, "");

  return newDate;
}

export function RequestFullScreen() {
  let element = document.documentElement;
  let requestMethod =
    element.requestFullScreen || //W3C
    element.webkitRequestFullScreen || //Chrome
    element.mozRequestFullScreen || //FireFox
    element.msRequestFullScreen; //IE11
  if (requestMethod) {
    requestMethod.call(element);
  } else if (typeof window.ActiveXObject !== "undefined") {
    //for Internet Explorer
    let wscript = new ActiveXObject("WScript.Shell");
    if (wscript !== null) {
      wscript.SendKeys("{F11}");
    }
  }
}

export function ChangeFullScreen() {
  const element = document.documentElement;
  if (!document.fullscreenElement) {
    //进入页面全屏
    RequestFullScreen();
  } else {
    if (document.exitFullscreen) {
      //退出全屏
      document.exitFullscreen();
    }
  }
}

export function formatMillisecondsToDate(milliseconds) {
  const date = new Date(milliseconds * 1);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");
  return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
}

export function isIndividual(device) {
  //判断是否为单体无人机设备
  if (!device) return false;
  return typeof device.SN === "string" && device.BindCode==='pilot';
}

export function getRouteLevel(path) {
  // 移除前后的斜杠,判断路由长度
  if (!path && !path.includes("/")) {
    return;
  }
  const trimmedPath = path.replace(/^\/|\/$/g, "");
  const parts = trimmedPath.split("/");
  return parts.length;
}
