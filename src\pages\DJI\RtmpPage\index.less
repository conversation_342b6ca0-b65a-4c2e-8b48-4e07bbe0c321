 /* 滚动条整体样式 */
 .wrapper::-webkit-scrollbar {
     width: 5px; //滚动条宽度
     height: 10px;
     background-color: #f5f5f5;
 }

 /* 滚动条thumb(滑块)样式 */
 .wrapper::-webkit-scrollbar-thumb {
     width: 5px;
     height: 10px;
     background-color: #9a9a9a;
 }

 /* 滚动条hover状态下thumb(滑块)样式 */
 .wrapper::-webkit-scrollbar-thumb:hover {
     background-color: #555;
 }

 /* 滚动条上下箭头样式 */
 .wrapper::-webkit-scrollbar-button {
     background-color: #ccc;
     display: none;
 }

 /* 滚动条左右箭头样式 */
 .wrapper::-webkit-scrollbar-button:start:decrement,
 .wrapper::-webkit-scrollbar-add-button {
     display: none;
 }

 .wrapper::-webkit-scrollbar-button:end:increment,
 .wrapper::-webkit-scrollbar-sub-button {
     display: none;
 }

 .veidopage {
     width: 300px;
     height: 100%;
     background: url('@/assets/img/veidodbg.png') no-repeat center bottom;
     background-blend-mode: soft-light;
     background-size: auto 80%;
     //  border-top: 1px solid #0095ff;
     //  border-right: 1px solid #0095ff;
     overflow: auto;
     border-style: solid;
     border-width: 1px 1px 0 0;
     border-image: linear-gradient(0deg, #0095ff, rgba(0, 255, 191, 0.2)) 1;
 }

 .box {
     display: flex;
     height: 100% !important;
     background: #00082c;

 }

 .vtitle {
     border-bottom: #003949 solid 1px;
     display: flex;
     justify-content: space-between;
     width: 100%;
     overflow: hidden;
     background: url('@/assets/img/tu.png'), linear-gradient(0deg, rgb(0 145 250 / 30%), transparent);
     height: 45px;
 }

 .sideitem {
     display: flex;
     align-items: center;
     gap: 10px;
     padding-right: 10px;
 }

 .sideitem .item {
     width: 28px;
     height: 28px;
 }

 .item {
     width: 56px;
     height: 56px;
     opacity: 0.4;
     cursor: pointer;
     transition: 0.2s all;
 }

 .sideitem .item:hover,
 .sideitem .item.cur {
     opacity: 1;
 }

 .sideitem .item>img {
     width: 100%;
     height: 100%;
 }

 .vtitletxt {
     font-size: 18px;
     color: #fff;
     font-weight: bold;
     padding: 0 16px;
     display: flex;
     align-items: center;
     position: relative;
     z-index: 0;

 }

 .vtitletxt::after,
 .vtitletxt::before {
     content: "";
     position: absolute;
     width: 130%;
     height: 100%;
     background: #0039494c;
     left: -25%;
     transform: skewX(-30deg);
 }

 .vtitletxt::before {
     background: linear-gradient(90deg, #0066ff, #07b6ff);
     left: -30%;
     z-index: -1;
 }

 .vtitletxt::after {
     background: #039cc64c;
     left: -13%;
     z-index: -2;
 }

 .vtitletxt>span {
     background-image: linear-gradient(0deg, rgb(87, 185, 255) 0%, #fff 50%);
     background-clip: text;
     -webkit-background-clip: text;
     -webkit-text-fill-color: transparent;
     filter: drop-shadow(rgba(0, 46, 99, 0.8) 0 0 2px);
 }

 .flybox {
     display: flex;
     align-items: center;
     gap: 10px;
     flex-wrap: wrap;
 }

 .flybox>span {
     cursor: pointer;
     font-size: 14px;
     padding: 4px 8px;
     color: rgba(140, 226, 254, 0.8);
     border: rgb(90 154 182 / 70%) solid 1px;
     background: rgb(2 123 255 / 30%);
     border-radius: 2px;
     opacity: 0.8;
     position: relative;
     z-index: 0;
 }

 .flybox>span::after {
     content: "";
     position: absolute;
     background: url('@/assets/img/line5.png');
     width: 100%;
     height: 100%;
     background-size: auto 10%;
     left: 0;
     top: 0;
     mix-blend-mode: soft-light;
     opacity: 0.6;
     z-index: -1;

 }

 .flybox .cur {
     opacity: 1;
     color: #fff;
     border: #05ffc0 1px solid;
     background: linear-gradient(90deg, #0569ff, #059a9d);
     text-shadow: #000 0 0;


 }

 .flybox>span.cur::after {
     opacity: 1;
 }

 .flycon {

     padding-bottom: 12px;
     border-bottom: #002256 solid 1px;
 }

 .flyconitem {
     padding: 8px
 }

 .flycontitle {
     font-size: 15px;
     font-weight: bold;
     color: #fff;
     margin: 4px 0 8px 0;
     background: linear-gradient(45deg, rgba(2, 128, 254, 0.2), transparent, transparent);
     padding: 8px 8px;
     border-style: solid;
     border-width: 0 0 1px 0;
     border-image: linear-gradient(90deg, rgba(0, 247, 255, 0.6), transparent) 1;
     display: flex;
     align-items: center;
 }

 .flycontitle2 {
     font-size: 14px;
     font-weight: bold;
     color: grey;
     margin-bottom: 16px;
 }

 .flyTilside {
     flex: 1;
     display: flex;
     align-items: center;
     justify-content: flex-end;
 }

 .flyTilside>span {
     font-size: 13px;
     font-weight: 500;
     padding: 0 0;
     display: flex;
     align-items: center;
 }

 .flyTilside>span.zyz {
     color: #ccff00;
 }

 .flyTilside>span.kx {
     color: rgba(0, 150, 255, 1);
 }

 .flyTilside>span.lx {
     color: rgb(156, 156, 156);
 }

 .dot {
     width: 13px;
     height: 13px;
     border-radius: 10em;

     display: inline-block;
     margin-right: 0.8vmin;
     position: relative;
     overflow: hidden;
     filter: saturate(180%);
     margin-right: 6px;
     margin-left: 6px;
 }

 .flyTilside>span.zyz .dot {
     background: #479e00;
     border: #0a3a02 solid 1px;
     box-shadow: #c4ffb7 0 0 0 2px inset, rgba(156, 239, 83, 0.3) 0 0 1px 1px, #ccff00 0 0 4px;
 }

 .flyTilside>span.kx .dot {
     border: #08003b solid 1px;
     background: rgba(0, 150, 255, 1);
     box-shadow: rgba(0, 200, 255, 1) 0 0 0 2px inset, rgba(0, 150, 255, 0.3) 0 0 1px 1px, #00c8ff 0 0 4px;
 }

 .flyTilside>span.lx .dot {
     border: #08003b solid 1px;
     background: rgb(117, 117, 117);
     box-shadow: rgb(141, 141, 141) 0 0 0 2px inset, rgba(56, 56, 56, 0.3) 0 0 1px 1px, #3d3d3d 0 0 6px;
 }

 .flyTilside>span .dot::after {
     width: 6px;
     height: 6px;
     content: "";
     position: absolute;
     background: #fff;
     border-radius: 10em;
     left: 50%;
     transform: translateX(-50%);
     margin-top: -1px;
     filter: blur(1px)
 }

 .veidowin {
     margin: 2px;
     cursor: pointer;
     width:calc(100% - 4px);
      
 }