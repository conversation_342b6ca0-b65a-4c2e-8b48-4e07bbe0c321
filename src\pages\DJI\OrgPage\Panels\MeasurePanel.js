import React, { useEffect, useState, useRef } from "react";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import * as turf from "@turf/turf";
import styles from "./MeasurePanel.less";
import { Dropdown, Input, message } from "antd";
import { getRtmpHttpUrl } from "@/utils/config";
import { getGuid } from "@/utils/helper";
import { useModel } from "umi";
import { HGet2 } from "@/utils/request";
import { getMapDataBySN } from "@/services/general";
import { getLocationMarket6 } from "@/pages/Maps/dt_market";
import { LocationIcon } from "@/components/icons/icons";
import { isEmpty } from "@/utils/utils";
import { FlyToPoint2 } from "@/pages/DJI/FlyToPage/helper";
import { GetDistance } from "@/pages/Maps/helper";
import { Gcj02ToWgs84 } from "@/pages/Maps/gps_helper";
import DraggableModal from "@/components/DraggableModal";
const { Search } = Input;

const MeasurePanel = ({
  map,
  left,
  right,
  device,
  updateCenter,
  showSearch,
  showOrthofire,
  showBZBtn,
}) => {
  if (!device) {
    device = JSON.parse(localStorage.getItem("device"));
  }
  const [markersLayerGroup, setMarkersLayerGroup] = useState(L.layerGroup()); //存贮marker
  const [currentMovingMarker, setCurrentMovingMarker] = useState(null); // 跟随鼠标移动的marker
  const [DiatanceGroup, setDistanceGroup] = useState([]); // 存储线段各段的距离
  const [NodeLayerGroup, setNodeLayerGroup] = useState(L.layerGroup()); // 存贮点击添加的红点
  const [DangerLayerGroup, setDangerLayerGroup] = useState(L.layerGroup()); // 存贮点击添加的红点

  const [lineData, setLineData] = useState([]); // 保存绘制线的坐标点数据
  const [tempLine, setTempLine] = useState(null); // 临时绘制的线
  const [drawing, setDrawing] = useState(false); // 是否处于绘制线状态
  const [drawnLayers, setDrawnLayers] = useState([]); // 已经绘制完成的线图层

  const [polygonData, setPolygonData] = useState([]); // 保存绘制面的坐标点数据
  const [tempPolygon, setTempPolygon] = useState(null); // 临时绘制的面
  const [drawingPolygon, setDrawingPolygon] = useState(false); // 是否处于绘制面状态
  const [drawnPolygons, setDrawnPolygons] = useState([]); // 已经绘制完成的面图层
  const [distanceMarkers, setDistanceMarkers] = useState([]); // 存储多个线段距离标记

  const { setModal, setOpen } = useModel("pageModel");
  const { fj } = useModel("droneModel");

  let [getLnglatText, setGetLnglatText] = useState("拾取坐标"); // 点击获取经纬度按钮文字
  let getLnglat = useRef(false); // 点击获取经纬度
  let latLngtip = useRef(null); // 经纬度提示框
  let [toggleOrthofireData, setToggleOrthofireData] = useState([]);
  let LocationIconMarkerArr =
    JSON.parse(window.localStorage.getItem("LocationIconMarker")) || []; // 存储定位列表
  let LocaSearchMarkerArr =
    JSON.parse(window.localStorage.getItem("LocaSearchMarker")) || []; //存储搜索列表

  const [messageApi, contextHolder] = message.useMessage();
  const openMessage = (type, content) => {
    //在模版字符串中有时直接用message将不起作用
    messageApi.open({
      key: content,
      type: type,
      content: content,
    });
  };

  //创建marker
  const createMarker = (text) => {
    const customIcon = L.divIcon({
      html: `<div style="width: 60px; height: 20px; background-color: rgba(253, 95, 96, 0.5); color: #fff; text-align: center; line-height: 20px;">${text}</div>`,
      iconSize: [60, 20],
      iconAnchor: [30, 30], // [30, 10] 表示水平偏移30像素，垂直偏移10像素
    });
    return customIcon;
  };

  //计算两点距离
  const calculateDistance = (point1LatLng, point2LatLng) => {
    const point1 = turf.point([point1LatLng.lng, point1LatLng.lat]);
    const point2 = turf.point([point2LatLng.lng, point2LatLng.lat]);
    const distance = turf
      .distance(point1, point2, { units: "kilometers" })
      .toFixed(2);
    return distance;
  };

  //点击位置添加marker
  const addMarker = (latlng) => {
    let test = "起点";
    if (lineData.length == 0) {
      test = "起点";
    } else {
      const distance = calculateDistance(lineData[lineData.length - 1], latlng);
      test = distance + " km";
      setDistanceGroup([...DiatanceGroup, distance]);
    }
    const customIcon = createMarker(test);
    const newMarker = L.marker(latlng, { icon: customIcon });
    markersLayerGroup.addLayer(newMarker);
    setMarkersLayerGroup(markersLayerGroup);
  };

  //跟随移动的marker
  const updateMarker = (latlng) => {
    if (drawing && lineData.length > 0) {
      const LastPoint = lineData[lineData.length - 1];
      const distance = calculateDistance(LastPoint, latlng);
      const text = `${distance} km`;
      const customIcon = createMarker(text);
      if (currentMovingMarker) {
        currentMovingMarker.setLatLng(latlng).setIcon(customIcon);
      } else {
        const newMovingMarker = L.marker(latlng, { icon: customIcon });
        markersLayerGroup.addLayer(newMovingMarker);
        setCurrentMovingMarker(newMovingMarker);
        setMarkersLayerGroup(markersLayerGroup);
      }
    }
  };

  //清除所有的Marker
  const clearMarkers = () => {
    markersLayerGroup.clearLayers();
    setMarkersLayerGroup(L.layerGroup());
    setCurrentMovingMarker(null);
    // 清除所有距离标记
    distanceMarkers.forEach((marker) => {
      map.removeLayer(marker);
    });
    setDistanceMarkers([]); // 清空状态
  };

  //清空所有红点
  const clearNodes = () => {
    NodeLayerGroup.clearLayers();
    setNodeLayerGroup(L.layerGroup());
  };

  //左键点击绘制
  const handleMapClick = (e) => {
    if (!drawing && !drawingPolygon) return;

    if (drawing) {
      const newLineData = [...lineData, e.latlng];
      setLineData(newLineData);
      addMarker(e.latlng); // 点击添加marker
      // 如果有两个以上的点，计算并显示距离
      if (newLineData.length > 1) {
        const lastPoint = newLineData[newLineData.length - 2];
        const distance = calculateDistance(lastPoint, e.latlng);
        const midPoint = [
          (lastPoint.lat + e.latlng.lat) / 2,
          (lastPoint.lng + e.latlng.lng) / 2,
        ];
        const newDistanceMarker = L.marker(midPoint, {
          icon: createMarker(distance + " km"),
        }).addTo(map);
        // 将新标记添加到数组中
        setDistanceMarkers((prevMarkers) => [
          ...prevMarkers,
          newDistanceMarker,
        ]);
      }
    }

    if (drawingPolygon) {
      const newPolygonData = [...polygonData, e.latlng];
      setPolygonData(newPolygonData);
    }

    //添加红点用于标记
    const node = L.circle(e.latlng, {
      color: "#fff", // 边框颜色
      fillOpacity: 0, // 填充透明度设置为0，形成镂空效果
      radius: 6, // 圆形标记大小
      weight: 2, // 边框宽度
    }).addTo(map);
    NodeLayerGroup.addLayer(node);
    setNodeLayerGroup(NodeLayerGroup);
  };

  ///移动
  const handleMouseMove = (e) => {
    if (!drawing && !drawingPolygon) return;
    updateMarker(e.latlng); // 更新移动的Marker

    if (drawing) {
      const updatedLine = [...lineData, e.latlng];
      if (tempLine) {
        tempLine.setLatLngs(updatedLine);
      } else {
        const newTempLine = L.polyline(updatedLine, {
          color: "red",
          weight: 3,
        }).addTo(map);
        setTempLine(newTempLine);
      }
    }

    if (drawingPolygon) {
      const updatedPolygon = [...polygonData, e.latlng];
      if (tempPolygon) {
        tempPolygon.setLatLngs(updatedPolygon);
        if (updatedPolygon.length > 2) {
          updatedPolygon.push(updatedPolygon[0]);
          const AreaReturn = AreaMeasure(updatedPolygon); //调用函数计算面积
          tempPolygon
            .bindTooltip(`面积: ${AreaReturn} 平方公里`, {
              permanent: true,
              direction: "center",
              className: "polygon-tooltip",
            })
            .openTooltip();
        }
      } else {
        const newTempPolygon = L.polygon(updatedPolygon, {
          color: "red",
          weight: 3,
        }).addTo(map);
        setTempPolygon(newTempPolygon);
      }
    }
  };

  //右键停止
  const handleRightClick = () => {
    if (drawing) {
      stopDrawing();
    }

    if (drawingPolygon) {
      stopDrawingPolygon();
    }
  };

  //面积计算
  const AreaMeasure = (polygon) => {
    if (!polygon || polygon.length < 3) {
      return 0; // 多边形至少需要三个点
    }
    const coordinates = polygon.map((latlng) => [latlng.lng, latlng.lat]); // 将经纬度转换为 Turf.js 所需的格式
    const area = turf.area({
      type: "Polygon",
      coordinates: [[...coordinates, coordinates[0]]], // 闭合多边形
    });
    const areaInSqKm = (area / 1000000).toFixed(2); // 转换为平方公里
    return areaInSqKm;
  };

  //开始绘制线
  const startDrawing = () => {
    setLineData([]);
    setDrawing(true);
  };

  //停止绘制线
  const stopDrawing = () => {
    if (lineData.length === 0) {
      setDrawing(false);
      return;
    }
    if (tempLine) {
      map.removeLayer(tempLine);
    }
    const finalLine = L.polyline(lineData, {
      color: "red",
      weight: 3,
    });
    finalLine.addTo(map);

    let totalDistance = 0;
    for (let i = 0; i < DiatanceGroup.length; i++) {
      const distance = Number.parseFloat(DiatanceGroup[i]);
      totalDistance = totalDistance + distance;
    }
    finalLine
      .bindTooltip("共:" + totalDistance.toFixed(2) + "千米", {
        permanent: true,
        opacity: 1.0,
        sticky: false,
        direction: "top",
      })
      .openTooltip();
    setDistanceGroup([]);

    setDrawnLayers((prevLayers) => [...prevLayers, finalLine]);
    setDrawing(false);
    setTempLine(null);
    if (currentMovingMarker) {
      map.removeLayer(currentMovingMarker);
      setCurrentMovingMarker(null);
    }
  };

  //开始绘制多边形
  const startDrawingPolygon = () => {
    setPolygonData([]);
    setDrawingPolygon(true);
  };

  // 停止绘制多边形
  const stopDrawingPolygon = () => {
    if (polygonData.length === 0) {
      setDrawingPolygon(false);
      return;
    }
    if (tempPolygon) {
      map.removeLayer(tempPolygon);
    }
    const finalPolygon = L.polygon(polygonData, {
      color: "red",
      weight: 3,
    });
    finalPolygon.addTo(map);

    const AreaReturn = AreaMeasure(polygonData);
    finalPolygon
      .bindTooltip("总面积:" + AreaReturn + "平方公里", {
        permanent: true,
        opacity: 0.8,
        sticky: false,
        direction: "top",
      })
      .openTooltip();

    setDrawnPolygons((prevPolygons) => [...prevPolygons, finalPolygon]);
    setDrawingPolygon(false);
    setTempPolygon(null);
    if (currentMovingMarker) {
      map.removeLayer(currentMovingMarker);
      setCurrentMovingMarker(null);
    }
  };

  //清除所有绘制
  const clearAllDrawings = () => {
    toggleGetLnglatFc();
    clearDrawnLayers();
    clearDrawnPolygons();
    clearMarkers();
    clearNodes();
  };

  const clearDrawnLayers = () => {
    drawnLayers.forEach((layer) => map.removeLayer(layer)); //清空绘制线图层
    setDrawnLayers([]);
  };

  const clearDrawnPolygons = () => {
    drawnPolygons.forEach((polygon) => map.removeLayer(polygon)); //清空绘制面图层
    setDrawnPolygons([]);
  };

  const toggleGetLnglatFc = () => {
    //取经纬度开关
    if (getLnglat.current) {
      getLnglat.current = false;
      setGetLnglatText("拾取坐标");
      if (!getLnglat.current) {
        map.removeLayer(latLngtip.current);
      }
    } else {
      getLnglat.current = true;
      setGetLnglatText("取消拾取");
    }
  };

  function onMapClickTip(e) {
    if (!getLnglat.current) return;
    if (getLnglat.current) {
      let latlng = e.latlng;
      // 反转经纬度
      let reversedLatLng = {
        lat: latlng.lng,
        lng: latlng.lat,
      };
      latLngtip.current = L.popup()
        .setLatLng(e.latlng)
        .setContent(
          `经度：${reversedLatLng.lat.toFixed(8)}°，纬度：${reversedLatLng.lng.toFixed(8)}°`
        )
        .openOn(map);
    }
  }

  function bindEvent(lat, lng) {
    const modeCode = fj.data?.mode_code;
    const isFly = (modeCode > 2 && modeCode < 9) || modeCode === 17;
    if (!isFly) {
      openMessage("warning", "请在飞行器飞行时或飞行器未返航状态下尝试");
      return;
    }
    const distance = GetDistance(
      parseFloat(lat),
      parseFloat(lng),
      device.Lat,
      device.Lng
    );
    if (distance > 10000) {
      openMessage("warning", "该点距离太远，建议采用航线飞行！");
      return;
    }
    FlyToPoint2(device.SN, lat, lng, 150);
  }

  function onOffIconMark(latLng, list) {
    let popupContent = `
      <div class="popup-content" style="display:flex;justify-content:center;align-items:center;cursor:pointer">
          <div class="fly-to-point" style="padding:5px;border:1px solid #ccc">飞到此处</div>
          <div class="clear-mark" style="padding:5px;border:1px solid #ccc;">移除标记</div>
      </div>`;

    // 创建并打开弹出框
    const popup = L.popup()
      .setLatLng(latLng)
      .setContent(popupContent)
      .openOn(map);

    // 使用 setTimeout 确保弹出框内容已添加到 DOM
    setTimeout(() => {
      // 获取弹出框的 DOM 元素
      const popupElement = document.querySelector(".leaflet-popup-content");

      // 绑定飞至该点事件
      const flyToPointElement = popupElement.querySelector(".fly-to-point");
      if (flyToPointElement) {
        flyToPointElement.onclick = function () {
          bindEvent(latLng[0], latLng[1]);
          map.closePopup(); // 关闭弹出框
        };
      }

      // 绑定删除标记事件
      const clearMarkElement = popupElement.querySelector(".clear-mark");
      if (clearMarkElement) {
        clearMarkElement.onclick = function () {
          // 点击删除重新设置图标并存储到本地
          LocationIconMarkerArr = LocationIconMarkerArr.filter(
            (item) => item !== latLng
          );
          localStorage.setItem(
            "LocationIconMarker",
            JSON.stringify(LocationIconMarkerArr)
          );
          SETiconMark(
            list,
            LocationIcon(),
            DangerLayerGroup,
            setDangerLayerGroup,
            latLng,
            L.latLng(latLng[0], latLng[1])
          );
          message.success("定位移除成功");
          map.closePopup(); // 关闭弹出框
        };
      }
    }, 0);
  }

  function SETiconMark(
    list,
    icon,
    Group,
    SetGroup,
    clearValue,
    removePoint,
    tip
  ) {
    // 设置图标函数
    if (removePoint) {
      // 如果指定了要删除的点，先从组中移除该点
      Group.eachLayer((layer) => {
        if (layer.getLatLng().equals(removePoint)) {
          Group.removeLayer(layer);
        }
      });
    }

    list?.forEach((item) => {
      const customIcon = L.divIcon({
        html: icon,
        iconAnchor: [16, 20],
      });

      const newMarker = L.marker(item, { icon: customIcon });
      newMarker.on("click", () => onOffIconMark(item, list));

      if (clearValue) {
        // 点击到图标上的经纬度值
        map.removeLayer(clearValue);
        SetGroup(Group);
      } else {
        Group.addLayer(newMarker);
        SetGroup(Group);
        if (tip) {
          // 新设置的值会提示
          message.success("定位设置成功");
        }
      }
    });
  }

  useEffect(() => {
    const getMapData = async () => {
      // 获取设备的地图数据，0：普通正射，1：tms为true的正射，2：geojson线条
      let res = await getMapDataBySN(device);
      let zsArr = [];
      let geojsonArr = [];
      setToggleOrthofireData(res);

      if (res && res.length > 0) {
        for (let value of res) {
          if (value.MapType == 0 || value.MapType == 1) {
            zsArr.push(value);
          }
          if (value.MapType == 2) {
            geojsonArr.push(value);
          }
        }
        //初始化地图数据
        if (zsArr.length > 0) {
          updateCenter(null, zsArr[0]);
        }
        if (geojsonArr.length > 0) {
          updateCenter(null, geojsonArr[0]);
        }
      }
    };

    getMapData();
  }, []);

  useEffect(() => {
    // 必不可少的本地存储发生变化时显示图标
    SETiconMark(
      LocationIconMarkerArr,
      LocationIcon(),
      DangerLayerGroup,
      setDangerLayerGroup
    );
    drawSearch(LocaSearchMarkerArr);
  }, [LocationIconMarkerArr, LocaSearchMarkerArr]);
  useEffect(() => {
    if (map) {
      markersLayerGroup.addTo(map);
      NodeLayerGroup.addTo(map);
      DangerLayerGroup.addTo(map);
      map.on("click", handleMapClick);
      map.on("mousemove", handleMouseMove);
      map.on("contextmenu", handleRightClick);
      map.on("dblclick", onMapClickTip);
    }
    return () => {
      if (map) {
        map.off("click", handleMapClick);
        map.off("mousemove", handleMouseMove);
        map.off("contextmenu", handleRightClick);
        map.off("dblclick", onMapClickTip);
      }
    };
  }, [
    map,
    lineData,
    drawing,
    tempLine,
    drawnLayers,
    polygonData,
    drawingPolygon,
    tempPolygon,
    drawnPolygons,
    markersLayerGroup,
    currentMovingMarker,
    NodeLayerGroup,
    getLnglat,
    latLngtip,
    DangerLayerGroup,
  ]);

  const getButton = (title, items) => {
    return (
      <Dropdown
        menu={{
          items: items,
        }}
        placement="bottomLeft"
        arrow
        //  theme="dark"
        style={{
          // zIndex: 1000000,
          width: 180,
          zIndex: 10005,
        }}
      >
        <div style={{ fontSize: 13.0, margin: 2.0 }}>{title}</div>
      </Dropdown>
    );
  };

  const getItem = (title, onClick) => {
    return {
      key: getGuid(),
      label: (
        <a style={{ zIndex: 10015 }} onClick={onClick}>
          {title}
        </a>
      ),
    };
  };

  const drawSearch = (sL) => {
    let nn = 0;
    markersLayerGroup.clearLayers();
    const list = [];
    if (isEmpty(sL)) return;
    sL.forEach((e) => {
      nn = nn + 1;
      if (nn <= 5) {
        const newMarker = getLocationMarket6(e, nn);
        newMarker.on("click", function () {
          let latLng = e.location;
          flyToPointFc(latLng);
        });
        list.push(newMarker);
      }
    });
    for (let i = list.length - 1; i >= 0; i--) {
      markersLayerGroup.addLayer(list[i]);
    }

    setMarkersLayerGroup(markersLayerGroup);
  };

  function flyToPointFc(latLng) {
    const point = latLng;

    // 创建弹出框内容
    const popupContent = `
      <div class="popup-content" style="display:flex;justify-content:center;align-items:center;cursor:pointer">
          <div class="fly-to-point" style="padding:5px;border:1px solid #ccc">飞到此处</div>
          <div class="detail" style="padding:5px;border:1px solid #ccc">搜索列表</div>
          <div class="clear-mark" style="padding:5px;border:1px solid #ccc;">移除标记</div>
      </div>
  `;

    // 创建并打开弹出框
    const popup = L.popup()
      .setLatLng(point)
      .setContent(popupContent)
      .openOn(map);

    // 使用 setTimeout 确保弹出框内容已添加到 DOM
    setTimeout(() => {
      // 获取弹出框的 DOM 元素
      const flyToPointElement = document.querySelector(".fly-to-point");
      if (flyToPointElement) {
        flyToPointElement.addEventListener("click", function () {
          bindEvent(point[0], point[1]);
          map.closePopup(); // 关闭弹出框
        });
      }
      const detailElement = document.querySelector(".detail");
      if (detailElement) {
        detailElement.addEventListener("click", function () {
          openModal();
          map.closePopup(); // 关闭弹出框
        });
      }
      // 绑定删除标记事件
      const clearMarkElement = document.querySelector(".clear-mark");
      if (clearMarkElement) {
        clearMarkElement.onclick = function () {
          removeLocation(point);
          message.success("定位移除成功");
          map.closePopup(); // 关闭弹出框
        };
      }
    }, 0);
  }

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isModalOpen2, setIsModalOpen2] = useState(false);

  const openModal = () => {
    //开启弹窗
    setIsModalOpen(true);
  };
  const closeModal = (modalType) => {
    //关闭弹窗时恢复地图交互
    map.dragging.enable(); // 启用地图拖动
    map.scrollWheelZoom.enable(); // 启用地图缩放
    if (modalType === "search") {
      setIsModalOpen(false);
    } else if (modalType === "orthofire") {
      setIsModalOpen2(false);
    }
  };
  const toggleOrthofire = async () => {
    setIsModalOpen2(!isModalOpen2);
  };

  function formatDistance(list) {
    // 格式化经纬度并以距离设备远近排序
    if (!list || list.length === 0) return [];

    const sortedCoordinates = [];
    // 将设备的坐标转换为 WGS-84
    const [deviceWgsLat, deviceWgsLng] = Gcj02ToWgs84(
      Number(device.Lat),
      Number(device.Lng)
    );

    list.forEach((coord) => {
      const point = coord.location.split(",");
      const [convertedLat, convertedLng] = Gcj02ToWgs84(
        Number(point[1]),
        Number(point[0])
      );

      // 更新 location 为经过转换的数组格式
      coord.location = [convertedLat, convertedLng];

      // 创建点对象
      const point1 = turf.point([convertedLat, convertedLng]);
      const point2 = turf.point([deviceWgsLat, deviceWgsLng]);
      const distance = turf.distance(point1, point2, { units: "meters" }); // 计算距离，单位为米

      // 将距离添加到新的对象中
      sortedCoordinates.push({ ...coord, distance });
    });

    // 对结果数组按距离排序
    sortedCoordinates.sort((a, b) => a.distance - b.distance);

    // 只保留前六个结果
    const topSixCoordinates = sortedCoordinates.slice(0, 5);

    return topSixCoordinates; // 返回前六个排序后的结果
  }

  const [searchData, setSearchData] = useState([]);
  // 从本地存储加载数据
  const loadData = () => {
    setSearchData(LocaSearchMarkerArr);
  };

  useEffect(() => {
    loadData();
  }, []);

  // 更新本地存储并更新状态
  const updateLocalStorage = (newData) => {
    localStorage.setItem("LocaSearchMarker", JSON.stringify(newData));
    setSearchData(newData);
  };

  // 删除相等的点
  const removeLocation = (point) => {
    const updatedArray = LocaSearchMarkerArr.filter(
      (item) => item.location !== point
    );
    updateLocalStorage(updatedArray);
    if (updatedArray.length <= 0) {
      closeModal();
    }
  };
  function searchDataDiv() {
    // 地点搜索列表弹窗内容
    return (
      <div>
        {searchData?.map((item, index) => {
          return (
            <div
              key={index}
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <div
                style={{
                  whiteSpace: "normal",
                  width: "100%",
                  background: "ccc",
                }}
              >
                <div
                  onClick={() => {
                    updateCenter(item.location, null, 16);
                  }}
                >
                  {index +
                    1 +
                    "." +
                    item.pname +
                    item.cityname +
                    item.adname +
                    item.address}
                  {"——" + item.name}
                </div>
                <div style={{ display: "flex", justifyContent: "flex-end" }}>
                  <ButtonWithHover
                    onClick={() => {
                      bindEvent(item.location[0], item.location[1]);
                    }}
                  >
                    ✈飞到此处
                  </ButtonWithHover>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  }
  const [Orthofire, setOrthofire] = useState("正射影像");

  function toggleOrthofireList() {
    // 正射列表弹窗内容
    if(!toggleOrthofireData) return;
    if (toggleOrthofireData?.length === 0)
      return <div style={{ textAlign: "center" }}>暂无数据</div>;
    //筛选正射影像或矢量地图
    const filteredData = toggleOrthofireData?.filter(
      (item) => {
        if (Orthofire === "正射影像") {
         return item.MapType === 0 || item.MapType === 1;
        }else if (Orthofire === "geojson") {
         return item.MapType === 2;
        }
      }
    );
    // 过滤后的数据为空，返回“暂无数据”
    if (filteredData?.length === 0) {
      return <div style={{ textAlign: "center" }}>暂无符合条件的数据</div>;
    }

    return (
      <div>
        {filteredData?.map((item, index) => {
          return (
            <div
              key={index}
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <ToggleOrthofireDiv index={index} item={item}>
                {item.MapName}
              </ToggleOrthofireDiv>
            </div>
          );
        })}
      </div>
    );
  }

  const ToggleOrthofireDiv = ({ children, item, index }) => {
    const [hoveredIndex, setHoveredIndex] = useState(false);
    const [selectedIndex, setSelectedIndex] = useState();
    let showOrthofire = false;
    return (
      <div
        onClick={() => {
          showOrthofire = !showOrthofire;
          updateCenter([item.Lat, item.Lng],item, 15);
          setSelectedIndex((prevIndex) => (prevIndex === index ? null : index));
        }}
        onMouseEnter={() => setHoveredIndex(true)}
        onMouseLeave={() => setHoveredIndex(false)}
        style={{
          padding: "3px 15px",
          margin: "4px 0",
          color: hoveredIndex || selectedIndex === index ? "#ffa500" : "#fff",
          transition: "color 0.3s",
          fontWeight: hoveredIndex ? "bold" : "normal",
          border: "1px solid #ccc",
          borderRadius: 5,
          overflow: "hidden",
          whiteSpace: "nowrap",
          textOverflow: "ellipsis",
        }}
      >
        {children}
      </div>
    );
  };

  const ButtonWithHover = ({ onClick, children }) => {
    const [hovered, setHovered] = useState(false);
    return (
      <div
        onClick={onClick}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
        style={{
          background: hovered ? "#1e8bbd" : "#2da3d4",
          padding: 4,
          margin: "5px 0",
          borderRadius: 5,
          fontSize: 11,
          cursor: "pointer",
          transition: "background 0.3s",
        }}
      >
        {children}
      </div>
    );
  };

  const onSearch = async (e) => {
    let areaNum = "510100";
    if (getRtmpHttpUrl().includes("***************")) {
      areaNum = "510683";
    }
    const data = await HGet2(`/api/v2/Map/GetPlace2?p1=${e}&p2=${areaNum}`);

    if (isEmpty(data?.pois)) return;
    message.success("搜索成功");
    let fortmatData = formatDistance(data.pois); //格式化经纬度并以距离设备远近排序
    localStorage.setItem("LocaSearchMarker", JSON.stringify(fortmatData)); //存储搜索结果到本地
    drawSearch(fortmatData); //绘制图标
    setOpen(false);
    openModal(); //开启搜索列表弹窗
    updateCenter(fortmatData[0]?.location); //跳转到第一个最近的搜索坐标
    setSearchData(fortmatData); //更新搜索列表状态
  };

  const searchPlace = () => {
    setModal(
      <Search
        placeholder="输入要搜索的关键字"
        onSearch={onSearch}
        enterButton
        key={getGuid()}
        style={{
          width: 440,
        }}
      />
    );
    setOpen(true);
  };

  const onSearchDanger = (e) => {
    // 输入坐标标设图标
    let latlng = e.split(/[，,]/).reverse();
    if (!e || !latlng[0] || !latlng[1]) {
      return message.warning("经纬度为空或者输入格式有误");
    }
    if (latlng[0] > 90 || latlng[0] < -90) {
      return message.warning("纬度应介于-90~90度");
    }
    if (latlng[1] > 180 || latlng[1] < -180) {
      return message.warning("经度应介于-180~180度");
    }

    SETstore(latlng);
    function SETstore(latlng) {
      const key = "LocationIconMarker";
      let list = JSON.parse(localStorage.getItem(key)) || [];
      // 重复检查
      const latlngString = latlng.toString(); // 将 latlng 转换为字符串进行比较
      if (list.some((item) => item.toString() === latlngString)) {
        message.warning("此位置已经设置过。");
        return; // 阻止添加重复项
      }
      list.push(latlng);
      updateCenter(latlng, null, 16); //跳转到设置的坐标
      try {
        localStorage.setItem(key, JSON.stringify(list));
        SETiconMark(
          [latlng],
          LocationIcon(),
          DangerLayerGroup,
          setDangerLayerGroup,
          null,
          null,
          true
        );
      } catch (error) {
        console.error("保存位置到本地存储时出错：", error);
      }
      setOpen(false);
    }
  };
  const searchDanger = () => {
    setModal(
      <Search
        placeholder="格式:109.55,31.05"
        onSearch={onSearchDanger}
        key={getGuid()}
        enterButton
        style={{
          width: 440,
        }}
      />
    );
    setOpen(true);
  };

  const changeFBL = () => {
    return [
      showSearch ? getItem("地点搜索", searchPlace) : null,
      getItem(getLnglatText, toggleGetLnglatFc),
      getItem("经纬度定位", searchDanger),
      getItem("标注线段", startDrawing),
      getItem("标注区域", startDrawingPolygon),
      getItem("清空标注", clearAllDrawings),
      showOrthofire && getItem("图层列表", toggleOrthofire),
    ];
  };

  return (
    <div>
      {contextHolder}
      <DraggableModal
        title="地点搜索列表"
        child={searchDataDiv()}
        isOpen={isModalOpen}
        onClose={() => closeModal("search")}
        map={map}
        style={{ width: 270}}
      ></DraggableModal>
      <DraggableModal
        title={
          <div>
            <div>地图图层列表</div>
            <div
              style={{
                display: "flex",
                justifyContent: "space-evenly",
                marginTop: 10,
                fontSize: 11,
                cursor: "pointer",
              }}
            >
              <span
                onClick={() => setOrthofire("正射影像")}
                style={{
                  backgroundColor: Orthofire === "geojson" ? "#274996" : "#1e8bbd",
                  padding: "3px 5px",
                  borderRadius: 5,
                }}
              >
                正射影像
              </span>
              <span
                onClick={() => setOrthofire("geojson")}
                style={{
                  backgroundColor: Orthofire === "正射影像" ? "#274996" : "#1e8bbd",
                  padding: "3px 5px",
                  borderRadius: 5,
                }}
              >
                矢量地图
              </span>
            </div>
          </div>
        }
        child={toggleOrthofireList()}
        isOpen={isModalOpen2}
        onClose={() => closeModal("orthofire")}
        map={map}
        style={{ width: 170 }}
      ></DraggableModal>
      {showBZBtn && (
        <div className={styles.camerasBar} style={{ right: right, left: left }}>
          <div
            className={styles.camerasBarItem}
            style={{ marginLeft: 2.0, }}
          >
            {getButton("地图标注", changeFBL())}
          </div>
        </div>
      )}
    </div>
  );
};

export default MeasurePanel;
