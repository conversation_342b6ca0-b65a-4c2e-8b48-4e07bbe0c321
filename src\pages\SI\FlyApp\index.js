import { Card, Row, Col } from 'antd';
import React, { useRef, useEffect, useState } from "react";
import { axiosApi } from "@/services/general";
import { getBodyH, isEmpty, getImgUrl, getImgSLTUrl } from "@/utils/utils";
import './index.less';

// 引入图片（实际项目中应替换为真实图片）
import croplandImg from '@/pages/SI/assets/image/croplandImg.webp';
import miningImg from '@/pages/SI/assets/image/miningImg.webp';
import satelliteImg from '@/pages/SI/assets/image/satelliteImg.webp';
import disasterImg from '@/pages/SI/assets/image/disasterImg.webp';
import mapResourceImg from '@/pages/SI/assets/image/mapResourceImg.webp';
import updatingImg from '@/pages/SI/assets/image/updatingImg.webp';

const FlyApp = () => {

  const [data, setData] = useState([]);
  const [configData, setConfigData] = useState();
  const [isCenter, setIsCenter] = useState(true);

  // 应用数据
  const applications = [
    {
      id: 1,
      url: 'ZHGD',
      title: '智慧耕地智能巡检系统',
      image: croplandImg,
    },
    {
      id: 2,
      url: 'LSYD',
      title: '临时用地智能巡检系统',
      image: miningImg,
    },
    {
      id: 3,
      url: 'WPZF',
      title: '卫片执法智能巡检系统',
      image: satelliteImg,
    },
    {
      id: 4,
      url: 'DZZH',
      title: '地质灾害智能监测系统',
      image: disasterImg,
    },
    {
      id: 5,
      url: 'YZT',
      title: '自然资源"一张图"管理系统',
      image: mapResourceImg,
    },
    {
      id: 6,
      url: 'ZZCH',
      title: '自主测绘更新系统',
      image: updatingImg,
    }
  ];


  useEffect(() => {
    const getData = async () => {

      let configRes = await axiosApi("/api/v2/APPConfig/Get", "GET", {
        sid: 'gt',
      });
      if (configRes) {
        console.log(configRes, "@configRes");
        setConfigData(configRes);
      }

      let res = await axiosApi("/api/v1/SystemInfo/Get", "GET", {
        sid: 'gt',
      });
      if (res && res.data && res.data.length > 0) {
        // 如果数据大于4个，则不居中显示
        if (res.data.length > 4) {
          setIsCenter(false)
        }
        console.log('@res', res);
        setData(res.data);
      }
    };
    getData();
  }, []);

  // 处理图片加载错误
  const handleImageError = (e) => {
    e.target.src = 'data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%22320%22%20height%3D%22180%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%20320%20180%22%20preserveAspectRatio%3D%22none%22%3E%0A%20%20%3Cdefs%3E%0A%20%20%20%20%3Cstyle%20type%3D%22text%2Fcss%22%3E%0A%20%20%20%20%20%20%23holder%20text%20%7B%0A%20%20%20%20%20%20%20%20fill%3A%20%23999%3B%0A%20%20%20%20%20%20%20%20font-family%3A%20sans-serif%3B%0A%20%20%20%20%20%20%20%20font-size%3A%2016px%3B%0A%20%20%20%20%20%20%20%20font-weight%3A%20400%3B%0A%20%20%20%20%20%20%7D%0A%20%20%20%20%3C%2Fstyle%3E%0A%20%20%3C%2Fdefs%3E%0A%20%20%3Cg%20id%3D%22holder%22%3E%0A%20%20%20%20%3Crect%20width%3D%22100%25%22%20height%3D%22100%25%22%20fill%3D%22%23373940%22%3E%3C%2Frect%3E%0A%20%20%20%20%3Cg%3E%0A%20%20%20%20%20%20%3Ctext%20text-anchor%3D%22middle%22%20x%3D%22160%22%20y%3D%2295%22%3E%E5%BA%94%E7%94%A8%E5%9B%BE%E7%89%87%3C%2Ftext%3E%0A%20%20%20%20%3C%2Fg%3E%0A%20%20%3C%2Fg%3E%0A%3C%2Fsvg%3E';
  };

  // 处理卡片点击
  const handleCardClick = (app) => {
    console.log('点击应用:', app.Title);
    // 进入应用
    // 打开新的页面 跳转到国土的页面
    window.open(`/#/gt/${app.TheURL}`);
    // 设置currentPage
    localStorage.setItem("currentPage", app.Title);
    localStorage.setItem("IsSIJump", true);
  };

  return (
    <div className="fly-app-container">
      <div className="content-wrapper">
        <Row gutter={[24, 24]} className="app-list">
          {data.map(app => (
            <Col key={app.ID} xs={24} sm={12} md={6}>
              <Card
                hoverable
                className="app-card"
                variant='borderless'
                onClick={() => handleCardClick(app)}
                cover={
                  <img
                    className='app-image'
                    src={getImgSLTUrl(app.ImgURL)}
                    alt={app.Title}
                  />
                }
              >
                <div className="app-title">
                  {app.Title}
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    </div>
  );
};

export default FlyApp; 