import React, { useEffect, useState } from "react";
import { useModel } from "umi";
import { queryPage } from '@/utils/MyRoute';
import MyMenu from "@/pages/GT/components/MyMenu";
import dayjs from 'dayjs';
import { timeFormat } from "@/utils/helper";
import {
    Button, Tabs, Card, Table, Form, Input, Select, Modal, DatePicker, List, Pagination, Tag,
    message, Row, Col, Descriptions, Slider, InputNumber, Image, Spin
} from 'antd';
import { axiosApi } from "@/services/general";
import './index.css'
import aiTableCols from './aiTable';
import flyTableCols from './flyTable';
import surveyTaskTableCols from './surveyTaskTable';


function RecordsDetailPage2({ record }) {
    // const { page, setPage, lastPage, currentPage } = useModel("pageModel");

    const [selectedId, setSelectedId] = useState(null);
    const [pageNum, setPageNum] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [startTime, setStartTime] = useState(() => {
        return dayjs().subtract(7, 'day').startOf('day').toDate();
    });
    const [endTime, setEndTime] = useState(() => dayjs().endOf('day').toDate());
    const [surveyTaskExecutionList, setSurveyTaskExecutionList] = useState([]);
    const [total, setTotal] = useState(0);
    const [surveyTaskExecutionFlightResult, setSurveyTaskExecutionFlightResult] = useState([]);
    const [surveyTaskExecutionAITask, setSurveyTaskExecutionAITask] = useState([]);

    const [activeTabKey, setActiveTabKey] = useState('1');

    // 获取巡检计划执行记录
    const getSurveyTaskExecutionList = async () => {
        const formatTime = (date) => {
            return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
        };

        const res = await axiosApi("/api/v1/Survey/GetSurveyTaskExecutionList", "GET", {
            SurveyTaskID: record.TaskID,
            StartTime: formatTime(startTime),
            EndTime: formatTime(endTime),
            pageNum: pageNum,
            pageSize: pageSize,
        });

        if (res.code === 1) {
            setSurveyTaskExecutionList(res.data.list ?? []);
            setTotal(res.data.pagination.total);
        }
    }

    // 分页处理
    const handlePagination = (page, pageSize) => {
        setPageNum(page);
        setPageSize(pageSize);
    }

    useEffect(() => {
        getSurveyTaskExecutionList();
    }, [pageNum, pageSize, startTime, endTime]);

    const getSurveyTaskExecutionFlightResult = async (id) => {
        const res = await axiosApi("/api/v1/Survey/GetSurveyTaskExecutionFlightResult", "GET", {
            executionID: id,
        });

        if (res.code === 1) {
            setSurveyTaskExecutionFlightResult(res.data ?? []);
        }
        if (res.code === 0) {
            message.error(res.msg);
            setSurveyTaskExecutionFlightResult([])
        }

    };

    const getSurveyTaskExecutionAITask = async (id) => {
        const res = await axiosApi("/api/v1/Survey/GetSurveyTaskExecutionAITask", "GET", {
            executionID: id,
        });

        if (res.code === 1) {
            setSurveyTaskExecutionAITask(res.data ?? [])
        }
        if (res.code === 0) {
            message.error(res.msg);
            setSurveyTaskExecutionAITask([])
        }
    };

    return (
        <div>
            <Card title={"巡检详情"}>
                <Row gutter={16}>
                    {/* 左侧列表区域 */}
                    <Col span={6}>
                        <div className="date-picker-group">
                            <DatePicker
                                style={{ flex: 1 }}
                                value={dayjs(startTime)}
                                onChange={(date) => {
                                    // 自动拼接00:00:00
                                    const newDate = date ? date.startOf('day') : dayjs().startOf('day');
                                    setStartTime(newDate.toDate());
                                }}
                                disabledDate={(current) => {
                                    return current && current > dayjs().endOf('day');
                                }}
                                allowClear={false}
                                placeholder="选择开始日期"
                                showNow={false}
                                format="YYYY-MM-DD"
                            />
                            <DatePicker
                                style={{ flex: 1 }}
                                value={dayjs(endTime)}
                                onChange={(date) => {
                                    // 自动拼接23:59:59
                                    const newDate = date ? date.endOf('day') : dayjs().endOf('day');
                                    setEndTime(newDate.toDate());
                                }}
                                disabledDate={(current) => {
                                    // 限制不能选择未来时间，且不能早于开始时间
                                    return current > dayjs().endOf('day') ||
                                        current < dayjs(startTime).startOf('day');
                                }}
                                allowClear={false}
                                placeholder="选择结束日期"
                                showNow={false}
                                format="YYYY-MM-DD"
                            />
                        </div>
                        {/* 列表容器 */}
                        <div className="record-list-container">
                            <Table
                                size="small"
                                rowKey="ID"
                                columns={surveyTaskTableCols()}
                                dataSource={surveyTaskExecutionList}
                                pagination={false}
                                onRow={(record) => ({
                                    onClick: () => {
                                        setSelectedId(record.ID);
                                        getSurveyTaskExecutionFlightResult(record.ID);
                                        getSurveyTaskExecutionAITask(record.ID);
                                    },
                                    style: {
                                        cursor: 'pointer'
                                    },
                                    className: `record-table-row ${selectedId === record.ID ? 'selected' : ''}`
                                })}
                                rowClassName={(record) =>
                                    `custom-row ${selectedId === record.ID ? 'selected' : ''}`
                                }
                            />
                        </div>

                        {/* 分页器 */}
                        <div className="record-pagination">
                            <Pagination
                                size='small'
                                simple='true'
                                current={pageNum}
                                pageSize={pageSize}
                                total={total}
                                onChange={handlePagination}
                                showSizeChanger
                                showQuickJumper
                            />
                        </div>
                    </Col>

                    {/* 右侧详情区域 */}
                    <Col span={18}>
                        <Tabs
                            activeKey={activeTabKey}
                            onChange={(activeKey) => {
                                setActiveTabKey(activeKey);
                            }}
                            items={[
                                {
                                    key: '1',
                                    label: '飞行任务',
                                    children: (
                                        <Table
                                            className="fly-table"
                                            size="small"
                                            rowKey="ID"
                                            pagination={false}
                                            dataSource={surveyTaskExecutionFlightResult}
                                            columns={flyTableCols()}
                                        />
                                    ),
                                },
                                {
                                    key: '2',
                                    label: 'AI识别结果',
                                    children: (
                                        <Table
                                            className="ai-table"
                                            size="small"
                                            rowKey="ID"
                                            pagination={false}
                                            dataSource={surveyTaskExecutionAITask}
                                            columns={aiTableCols()}
                                        />
                                    ),
                                },
                            ]}
                        />
                    </Col>
                </Row>
            </Card>
        </div>
    )
};

export default RecordsDetailPage2;
