import { Get2, axios<PERSON>pi } from "@/services/general";
import { getBodyH, getBodyW2, isEmpty } from "@/utils/utils";
import { Row, Col, Button, Modal, Input, message, Select, Tooltip } from "antd";
import { PlayCircleOutlined, DeleteOutlined } from "@ant-design/icons";
import { useEffect, useRef, useState } from "react";
import gongge1 from "@/assets/icons/gongge1.png";
import gongge4 from "@/assets/icons/gongge4.png";
import gongge6 from "@/assets/icons/gongge6.png";
import gongge9 from "@/assets/icons/gongge9.png";
import camera4 from "@/assets/drcImgs/camera4.png";
import MediaViewer from "./MediaViewer";
import { removeVideoSource } from "./Panels/hooks";
import styles from "./index.less";
import { FJStart, JCStart } from "../DRCPage/Panels/RtmpChange";
import { checkIf<PERSON>lyer } from "@/utils/utils";
import HanHuaPage from "@/pages/DJI/DevicePage/Panels/HanHuaQi";
import { useModel } from "umi";

const RtmpPage = () => {
  const [dL, setDL] = useState([]);
  const [ind, setInd] = useState(0);
  const rData = useRef([]);
  const [pb, setPB] = useState(4);
  const [index, setIndex] = useState({});
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [JcName, setJcName] = useState("");
  const [JcSn, setJcSn] = useState();
  const [selectwebRtcPlyer, setSelectwebRtcPlyer] = useState("单兵直播"); //选择LiveGBS或者其他的按钮，用于判断返回的WebRtcPlayer的地址
  const [videoUrl, setVideoUrl] = useState(""); // 手动添加的视频地址
  const h4 = getBodyH(72) / 2;
  const h6 = getBodyH(80) / 2;
  const h9 = getBodyH(80) / 3;
  const AddVideoSourceArr = JSON.parse(
    localStorage.getItem("AddVideoSource") || "[]"
  );
  const [messageApi, contextHolder] = message.useMessage();
  const { setModal, setOpen } = useModel("pageModel");

  const openMessage = (type, content) => {
    //在模版字符串中有时直接用message将不起作用
    messageApi.open({
      key: content,
      type: type,
      content: content,
    });
  };
  const getDList = async () => {
    try {
      const onlineDevices = await Get2("/api/v1/Device/GetAllList");
      const filteredOnline = onlineDevices.filter(
        (item) => item.IfOnLine === true
      );
      const AddVideoSourceArr = JSON.parse(
        localStorage.getItem("AddVideoSource") || "[]"
      );

      const fetchLiveInfo = async (source) => {
        if (source.videoType === "单兵直播") {
          try {
            const res = await axiosApi("/api/v1/Live/HMJStart", "GET", {
              sn: source.key,
            });
            return {
              ...source,
              isOnline: (res && res.isOnline) || null, // 设备的在线状态
              SN: (res && res.flv) || "", // 设备播放地址
            };
          } catch (error) {
            return {
              ...source,
              isOnline: null,
              SN: "",
            };
          }
        }
        return source; // 不是单兵直播
      };

      // 并行获取所有单兵直播的信息
      const liveSources = await Promise.all(
        AddVideoSourceArr.map(fetchLiveInfo)
      );
      // 合并在线设备和本地视频源，并检查重复项
      const combinedSources = [...filteredOnline];
      const onlineDeviceKeys = new Set(
        filteredOnline.map((device) => device.key)
      );
      const uniqueLiveSources = liveSources.filter(
        (source) => !onlineDeviceKeys.has(source.key)
      ); //过滤掉在线设备重复的
      combinedSources.push(...uniqueLiveSources);

      setDL(combinedSources);

    } catch (error) {
      console.error("获取设备列表时出错:", error);
    }
  };

  useEffect(() => {
    getDList();
  }, []);

  const GetItemX = (i, height) => {
    //点击提示选中了右边的哪个屏幕
    let border = "1px solid #004080";
    if (ind == i) {
      border = "1px solid red"; //  选中的为红色
    }
    if (pb == 0 && i == 0) border = "1px solid  #004080"; //默认选中为视频一宫格时，宫格边缘为蓝色

    const d2 = rData.current[i];
    console.log(d2);

    if (isEmpty(d2))
      return (
        <div
          className={styles.veidowin}
          style={{ height: height, border: border }}
          onClick={() => setInd(i)}
        />
      );
    let sn = d2.data.SN;
    let sn2 = d2.data.SN2;
    let nm = d2.data.DName;
    let type = d2.data?.videoType; //视频类型

    //如果飞机在线
    if (d2.ifFJ && !type) {
      sn = d2.data.SN2;
      nm = nm + "-无人机镜头";
    }
    if (type) {
      nm = `${nm} - ${type}`;
    }
    const onHanHuaQi = () => {
      setModal(<HanHuaPage device={{ SN: sn2 }} setOpen={setOpen} />);
      setOpen(true);
    };
    return (
      <div
        style={{ margin: 2.0, cursor: "pointer", height, overflow: "hidden", position: 'relative' }}
        onClick={() => setInd(i)}
      >
        {d2 ? (
          <>
            <div
              style={{
                position: "absolute",
                top: 8,
                right: 8,
                zIndex: 10,
                fontWeight: "bold",
                color: "#00aeff",
                fontSize: 16.0,
                fontFamily: "kaiTi",
              }}
            >
              {nm || "无数据"}
            </div>
            <MediaViewer key={sn} SN={sn} type={type}></MediaViewer>
            {sn2 && <div style={{ width: '5%', maxWidth: 42, aspectRatio: 1, position: 'absolute', bottom: '30%', right: '1%' }}>
              <Tooltip placement="left" title="喊话器">
                <img
                  style={{ width: '100%', height: '100%' }}
                  draggable={false}
                  src={camera4}
                  onClick={() => {
                    if (checkIfFlyer()) {
                      onHanHuaQi();
                    }
                  }}
                ></img>
              </Tooltip>
            </div>}
          </>
        ) : (
          <div className={styles.veidowin} style={{ height, border }} />
        )}
      </div>
    );
  };

  const getDiv1 = (
    <div
      style={{
        marginTop: 8.0,
        height: getBodyH(90),
        width: getBodyW2(20) - 300,
      }}
    >
      {GetItemX(0, getBodyH(90))}
    </div>
  );

  const getDiv4 = (
    <Row style={{ height: "100%", width: "100%" }}>
      {[0, 1, 2, 3].map((index) => (
        <Col key={index} span={12} style={{ padding: 2.0 }}>
          {GetItemX(index, h4)}
        </Col>
      ))}
    </Row>
  );

  const getDiv6 = (
    <div>
      {[0, 1].map((row) => (
        <Row key={row} style={{ height: "100%", width: "100%" }}>
          {[0, 1, 2].map((col) => (
            <Col key={col} span={8} style={{ padding: 2.0 }}>
              {GetItemX(row * 3 + col, h6)}
            </Col>
          ))}
        </Row>
      ))}
    </div>
  );

  const getDiv9 = (
    <div>
      {[0, 1, 2].map((row) => (
        <Row key={row} style={{ height: "100%", width: "100%" }}>
          {[0, 1, 2].map((col) => (
            <Col key={col} span={8} style={{ padding: 2.0 }}>
              {GetItemX(row * 3 + col, h9)}
            </Col>
          ))}
        </Row>
      ))}
    </div>
  );

  const w1 = getBodyW2(0);
  const h1 = getBodyH(57);

  const showModal = () => {
    setIsModalOpen(true);
  };
  const handleOk = () => {
    addNewItem(JcName, JcSn, selectwebRtcPlyer);
  };
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const addNewItem = async (DName, SN, videoType) => {
    let isOnline = null;
    let key = SN;
    if (DName === "" || SN === "") {
      message.warning("请输入名称和频道号/视频地址");
      return;
    }
    for (const item of AddVideoSourceArr) {
      if (item.DName === DName || item.key === SN) {
        return message.warning("名称或频道号/视频地址重复，请重新输入");
      }
    }
    if (videoType === "单兵直播") {
      try {
        let res = await axiosApi("/api/v1/Live/HMJStart", "GET", { sn: SN });
        if (res && res.flv) {
          isOnline = res.isOnline; // 设备的在线状态
          SN = res.flv; // 设备的播放地址
        } else {
          SN = "";
        }
      } catch (error) {
        openMessage("error", `获取${DName}单兵直播信息失败:`, error)
      }
    }
    const newItem = {
      DName: DName,
      SN: SN + "",
      key: key,
      videoType: videoType,
      isOnline: isOnline,
    };

    if (AddVideoSourceArr == null) {
      localStorage.setItem("AddVideoSource", JSON.stringify([newItem]));
    } else {
      const itemsArray = AddVideoSourceArr;
      itemsArray.push(newItem);
      localStorage.setItem("AddVideoSource", JSON.stringify(itemsArray));
    }
    // 清空输入框,必须的
    setJcName("");
    setJcSn("");

    setDL([...dL, newItem]);
    setIsModalOpen(false); // 关闭弹窗
  };

  const LeftTopDiv = (
    <div className={styles.vtitle}>
      <span className={styles.vtitletxt}>
        <span>视频墙</span>
      </span>
      <Button
        style={{
          height: 20,
          width: 10,
          marginTop: 12,
          marginLeft: 10,
          backgroundColor: "#44c0e4",
        }}
        onClick={showModal}
      >
        {" "}
        +{" "}
      </Button>
      <div className={styles.sideitem}>
        <div className={[styles.item, styles.cur].join(" ")}>
          <img src={gongge1} onClick={() => setPB(0)}></img>
        </div>
        <div className={styles.item}>
          <img src={gongge4} onClick={() => setPB(1)}></img>
        </div>
        <div className={styles.item}>
          <img src={gongge6} onClick={() => setPB(2)}></img>
        </div>
        <div className={styles.item}>
          <img src={gongge9} onClick={() => setPB(3)}></img>
        </div>
      </div>
    </div>
  );

  const ZBonClick = (d1, p1) => {
    if (p1) {
      FJStart(d1);
    } else {
      JCStart(d1);
    }
    setIndex({ data: d1, ifFJ: p1 });
    rData.current[ind] = { data: d1, ifFJ: p1 };
  };

  //页面左边的视频播放按钮
  const getDButton = (d1) => {
    let en3 = () => {
      if (d1.isOnline === false) {
        return message.warning("设备离线");
      } else if (d1.isOnline === null) {
        return message.warning("设备未注册");
      }
      ZBonClick(d1, false);
    };

    if (!d1.hasOwnProperty("OrgCode")) {
      // 检查 d1 是否包含 OrgCode 属性，如果不包含则是手动添加的直播源
      return (
        <div className={styles.flybox}>
          <span className={styles.cur} onClick={en3}>
            <PlayCircleOutlined /> {d1.videoType}
          </span>
          <span
            onClick={() => {
              removeVideoSource(d1.key);
              getDList();
            }}
          >
            移除
            <DeleteOutlined />
          </span>
        </div>
      );
    }

    let en2 = () => null;
    let col2 = "grey";
    let en1 = () => null;
    let col1 = "grey";
    if (!isEmpty(d1.OsdData)) {
      //机场在线
      col1 = "#00aeff";
      en1 = () => {
        ZBonClick(d1, false);
      };
      //device_online_status:机场停机坪上的飞行器开机状态，0：关机，1：开机
      if (d1.OsdData.sub_device?.device_online_status != "0") {
        en2 = () => {
          ZBonClick(d1, true);
        };
        col2 = "#00aeff";
      } else {
        //飞行器关机
        en2 = () => {
          message.info("飞行器处于关机中");
        };
      }
    } else {
      //机场离线
      en1 = () => {
        message.info("机场离线");
      };
    }

    return (
      <div className={styles.flybox}>
        <span className={styles.cur} onClick={en1}>
          机场镜头
        </span>
        <span onClick={en2}>无人机镜头</span>
      </div>
    );
  };

  const getDStatus = (d1) => {
    const ModeCodeJson = {
      0: "空闲中",
      1: "现场调试",
      2: "远程调试",
      3: "固件升级中",
      4: "作业中",
      true: "在线",
      false: "离线",
      null: "设备未注册",
    };
    if (d1.videoType)
      return (
        <span className={d1.isOnline ? styles.kx : styles.lx}>
          <i className={styles.dot}></i>
          {ModeCodeJson[d1.isOnline]}
        </span>
      );
    if (isEmpty(d1.OsdData))
      return (
        <span className={styles.lx}>
          <i className={styles.dot}></i>离线
        </span>
      );
    if (d1.OsdData.mode_code == "0")
      return (
        <span className={styles.kx}>
          <i className={styles.dot}></i>
          {ModeCodeJson[d1.OsdData.mode_code]}
        </span>
      );
    if (d1.OsdData.mode_code == "4")
      return (
        <span className={styles.zyz}>
          <i className={styles.dot}></i>
          {ModeCodeJson[d1.OsdData.mode_code]}
        </span>
      );
    return (
      <span className={styles.lx}>
        <i className={styles.dot}></i>
        {ModeCodeJson[d1.OsdData.mode_code]}
      </span>
    );
  };

  const getDItem = (d1, index) => {
    return (
      <div className={styles.flycon} key={index}>
        <div className={styles.flyconitem}>
          <div className={styles.flycontitle}>
            <span>{d1.DName}</span>
            <div className={styles.flyTilside}>{getDStatus(d1)}</div>
          </div>
          <div className={styles.flycontitle2}>{d1.OrgName}</div>
          {getDButton(d1)}
        </div>
      </div>
    );
  };

  const getDListPanel = (data) => {
    return data.map((d1, index) => getDItem(d1, index));
  };

  //切换N宫格
  const getPBBody = (i) => {
    switch (i) {
      case 0:
        return getDiv1;
      case 1:
        return getDiv4;
      case 2:
        return getDiv6;
      case 3:
        return getDiv9;
      default:
        return getDiv9;
    }
  };
  //选择LiveGBS或者其他的按钮，用于判断返回的WebRtcPlayer的地址
  const LiveGBS_Or_OtherChange = (value) => {
    setSelectwebRtcPlyer(value);
  };

  return (
    <div className={styles.box} style={{ height: h1 }}>
      <div className={[styles.wrapper, styles.veidopage].join(" ")}>
        <Modal
          title="添加直播源"
          open={isModalOpen}
          onOk={handleOk}
          onCancel={handleCancel}
          width={350}
          style={{
            borderRadius: "8px", // 示例：添加圆角
            border: "none", // 示例：移除边框
            boxShadow: "0 4px 8px rgba(0, 0, 0, 0.15)", // 示例：添加阴影
          }}
        >
          <div style={{ padding: "16px" }}>
            {" "}
            <p style={{ marginBottom: "8px", fontWeight: "bold" }}>名称</p>
            <Input
              style={{ width: "60%", marginBottom: "16px" }}
              value={JcName}
              onChange={(e) => setJcName(e.target.value)}
            />
            <Select
              defaultValue="单兵设备"
              style={{
                width: "40%",
              }}
              onChange={LiveGBS_Or_OtherChange}
              options={[
                {
                  value: "单兵设备",
                  label: "单兵设备",
                },
                {
                  value: "无人机接入",
                  label: "无人机接入",
                },
                {
                  value: "mu38视频",
                  label: "mu38视频",
                },
              ]}
            />
            <p style={{ marginBottom: "8px", fontWeight: "bold" }}>
              频道号/视频地址
            </p>
            <Input
              style={{ width: "100%" }}
              value={JcSn}
              onChange={(e) => setJcSn(e.target.value)}
            />
          </div>
        </Modal>
        {LeftTopDiv}
        {getDListPanel(dL)}
      </div>

      <div style={{ height: h1, width: w1 - 300, background: "black" }}>
        {getPBBody(pb)}
      </div>
    </div>
  );
};

export default RtmpPage;
