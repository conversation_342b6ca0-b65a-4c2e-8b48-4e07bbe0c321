import { useRef, useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Card,
  message,
  Col,
  Row,
  Select,
  Radio,
  Table,
  Input,
  Checkbox,
  Splitter,
  Tag,
} from "antd";
import { getBodyH, getDeviceName, isEmpty } from "@/utils/utils";
import LastPageButton from "@/components/LastPageButton";
import { GetMqttClient } from "@/utils/websocket";
import dayjs from "dayjs";
import { getGuid } from "@/utils/helper"; //生成guid
import { HGet2, HPost2 } from "@/utils/request";
import { axiosApi } from "@/services/general";
import * as turf from "@turf/turf"; //计算几何
import "./index.less";
import {
  MapContainer,
  Tooltip,
  TileLayer,
  useMapEvents,
  Circle,
  Polygon,
  Marker,
} from "react-leaflet";
import UploddFile from "./UploddFile";


const SetNfz = ({doNotShowLastButton}) => {
  const mapRef = useRef();
  const mqttC = useRef({}); //mqtt客户端
  const deviceSN = "8UUXN3D00A02NS"; //荆州低空经济
  const [DeviceList, setdeviceList] = useState([]); //从接口获取的所有的设备列表
  const [selectDe, setSelectDe] = useState(); //当前选择的设备
  const [mapkey, setMapkey] = useState(""); //用于刷新地图组件
  const [typeOfPolyfence, setTypeOfPolyfence] = useState("nfz"); //当前选择的飞行区类型: nfz-禁飞区 dfence-围栏
  const [tupeOfPolygon, setTupeOfPolygon] = useState("Polygon"); //当前选择的多边形类型: Polygon-多边形 Point-圆形
  const [nfztitle, setNfztitle] = useState(""); // 自定义飞行区的名称
  const [nfzcontent, setNfzcontent] = useState(""); // 自定义飞行区的说明
  const [GetNfzData, setGetNfzData] = useState([]); //获取到的禁飞区数据

  const [startdraw, setStartdraw] = useState(false); //是否开始绘制

  const [circlecenter, setCirclecenter] = useState([]); //临时绘制圆的中心点
  const [circleradius, setCircleradius] = useState(); //临时绘制圆的半径
  const [polygonVertices, setPolygonVertices] = useState([]); //绘制的多边形的顶点
  const [temporary, setTemporary] = useState([]); // 存储当前鼠标移动的临时位置

  const [tabledataSource, setTabledataSource] = useState([]); // 表格数据
  const [selectedRowKeys, setSelectedRowKeys] = useState([]); // 表格中选择的行
  const [get_select_flyarea, setGet_select_flyarea] = useState([]); // 下拉框选择时将从后端获取到的飞行区数据放到这里，通过MapContainer加载到地图上

  const [splitterSize, setSplitterSize] = useState(["100%", "0%"]);
  const [CheckboxKeys, setCheckboxKeys] = useState([]); // 用于刷新Checkbox组件
  const { TextArea } = Input;

  const GetNfzDataMethod = async () => {
    const result_GetNfzData = await HGet2("/api/v1/FlyArea/GetAllList");
    setGetNfzData(result_GetNfzData);
  };
  const GetalreadyNfzData = async (SN) => {
    //获取已经同步的区域机场的禁飞区数据
    let res = await axiosApi(`/api/v1/FlyArea/GetCurrent`, "GET", {
      SN: SN,
    });
    if (res.code === 1) {
      return res.data.features;
    }
  };

  const CmdMqttConn = (sn) => {
    if (!isEmpty(mqttC.current)) {
      mqttC.current.end();
    }

    mqttC.current = GetMqttClient();
    mqttC.current.on("message", (topic, message) => {
      updateVal(topic, message);
    });
    mqttC.current.subscribe("thing/product/" + deviceSN + "/events");
  };

  //挂载时获取下拉列表数据
  const getDeviceList = async () => {
    const result = await HGet2("/api/open/Device/GetAllList");
    const uniqueResult = Array.from(
      new Map(result.map((item) => [item.SN, item])).values()
    );

    const devicedata = uniqueResult.map((item) => ({
      ...item,
      label: item.DName,
      value: item.SN,
    }));
    setdeviceList(devicedata); // 更新设备下拉列表
  };

  useEffect(() => {
    CmdMqttConn(deviceSN);
    getDeviceList();
    GetNfzDataMethod();
  }, []);

  useEffect(() => {
    if (DeviceList && DeviceList.length > 0) {
      //首次加载默认选择第一个设备
      SelectChange(DeviceList[0].value);
    }
  }, [DeviceList]);

  useEffect(() => {
    if (selectDe == null) return;
    else {
      setSelectedRowKeys([]); //清空选中行
      setTabledataSource([]); // 清空表格数据
      setGet_select_flyarea([]); //清空地图数据
      setMapkey(dayjs().valueOf()); // 更新地图key
      GetFlyArea(selectDe.SN); //添加表格数据
    }
  }, [GetNfzData]);
  const SelectChange = (e) => {
    let data = DeviceList.find((item) => item.value === e);
    setSelectDe(data); // 更新选中的设备
    setMapkey(dayjs().valueOf()); // 更新地图key

    //根据Data.SN 将飞行区数据同步到地图上。
    setTabledataSource([]); // 清空表格数据
    setGet_select_flyarea([]); //清空地图数据
    GetFlyArea(data?.SN); //添加表格数据
  };

  //数据添加到表格里面
  const GetFlyArea = async (SN) => {
    let alreadyArr = await GetalreadyNfzData(SN); //获取已经同步的飞行区数据

    // 添加空值检查和数组类型验证
    if (!GetNfzData || !Array.isArray(GetNfzData) || !alreadyArr && !Array.isArray(alreadyArr)) {
      message.warning("获取数据异常");
      return;
    }
    const formatColor = {
      nfz: "red",
      dfence: "blue",
    };
    const formatText = {
      nfz: "禁飞区",
      dfence: "电子围栏",
    };


    for (let i = 0; i < GetNfzData.length; i++) {
      if (GetNfzData[i].OrgCode === SN) {
        //添加到table中
        let data = { ...GetNfzData[i] }; //深拷贝，防止修改原始数据
        let MContent = data.MContent; //选中的项目名称
        let title = `${formatText[data.MType]}:${MContent}`;
        let color = formatColor[data.MType] || "gray"; // 默认颜色
        data.MData = JSON.parse(data.MData);
        data.key = data.ID;
        data.MType_Zh_CN = formatText[data.MType];

        setTabledataSource((prevData) => [...prevData, data]); //添加到表格
        for (let j = 0; j < alreadyArr.length; j++) {
          //如果已经同步的飞行区在当前机场中，则默认勾选
          if (data.MData.id === alreadyArr[j].id) {
            setSelectedRowKeys((prevSelected) => [...prevSelected, data.ID]); //默认勾选
          }
        }

        //根据data.MData 绘制飞行区到地图上
        if (data.MData !== null) {
          if (data.MData.geometry.type === "Polygon") {
            let Geo_Polygon = ExchangeCoordinates(
              data.MData.geometry.coordinates[0]
            ); //坐标交换用于加载到地图上
            setGet_select_flyarea((prevData) => [
              ...prevData,
              <Polygon
                key={data.ID}
                positions={[...Geo_Polygon]} // 临时连接当前鼠标位置
                color={color}
                weight={2}
                // opacity={0.3}
                dashArray="5,5" // 虚线
              >
                <Tooltip>
                  <span style={{ color: color }}>{title}</span>
                </Tooltip>
              </Polygon>,
            ]); //添加多边形到地图上
          } else if (data.MData.geometry.type === "Point") {
            let Geo_Point = [
              data.MData.geometry.coordinates[1],
              data.MData.geometry.coordinates[0],
            ];
            let Geo_Point_radius = data.MData.properties.radius;
            setGet_select_flyarea((prevData) => [
              ...prevData,
              <Circle
                key={data.ID}
                center={Geo_Point}
                radius={Geo_Point_radius}
                color={color}
              >
                <Tooltip>
                  <span style={{ color: color }}>{title}</span>
                </Tooltip>
              </Circle>,
            ]);
          }
        }
      }
    }
  };

  const Add_get_select_flyarea = () => {
    if (get_select_flyarea.length == 0) {
      return;
    } else {
      return get_select_flyarea.filter((item) =>
        selectedRowKeys.includes(Number(item.key))
      ); // 仅渲染选中的飞行区
    }
  };

  // 下拉列表组件
  const DeviceSelect = (
    <>
      <div style={{ whiteSpace: "nowrap" }}>机场选择：</div>
      <Select
        value={selectDe?.label}
        onChange={SelectChange}
        options={DeviceList}
        style={{
          width: 200,
        }}
      />
    </>
  );

  const updateVal = (t1, m1) => {
    const xx = JSON.parse(m1);
    const bid = xx.bid;
    const tid = xx.tid;
    //禁飞区
    //注意回复信息要和请求信息的bid和tid一致，否则不会生效
    /* if (xx.method == "flight_areas_get") {
      message.info("正在获取禁飞区文件");
      const Nfzdata = {
        output: {
          files: [
            {
              checksum:
                "a93438ac2a16e1471deaf113f89bccecdf36dc1969a45d2fc3f488ea40a758a7",
              name: "geofence_e9d9cd54a692d2f642178e2cf7d7bcf3.json",
              size: 569,
              url: "https://300bdf2b-a150-406e-be63-d28bd29b409f.oss-cn-chengdu.aliyuncs.com/Nfz_test/geofence_e9d9cd54a692d2f642178e2cf7d7bcf3.json",
            },
          ],
        },
        result: 0,
      };
      DoCMD_reply(deviceSN, "flight_areas_get", Nfzdata, bid, tid);
      message.info("正在给机场发送禁飞区文件");
    } */

    if (xx.method == "flight_areas_sync_progress") {
      const status = xx.data.status;
      const reasonCode = xx.data.reason; // 假设 xx.data.reason 是返回码

      switch (status) {
        case "fail":
          message.info("禁飞区同步失败");
          // 根据返回码提供更详细的错误信息
          switch (reasonCode) {
            case 1:
              message.info("解析云端返回的文件信息失败");
              break;
            case 2:
              message.info("获取飞行器端文件信息失败");
              break;
            case 3:
              message.info("从云端下载文件失败");
              break;
            case 4:
              message.info("链路翻转失败");
              break;
            case 5:
              message.info("传输文件失败");
              break;
            case 6:
              message.info("disable失败");
              break;
            case 7:
              message.info("自定义飞行区删除失败");
              break;
            case 8:
              message.info("飞行器端加载作业区域数据失败");
              break;
            case 9:
              message.info("enable失败");
              break;
            case 10:
              message.info("机场增强图传无法关闭，作业区域数据同步失败");
              break;
            case 11:
              message.info("飞行器开机失败，无法同步作业区域数据");
              break;
            case 12:
              message.info("checksum校验失败");
              break;
            case 13:
              message.info("同步异常超时");
              break;
            default:
              message.info("未知错误，返回码: " + reasonCode);
          }
          break;

        case "synchronizing":
          message.info("正在同步禁飞区");
          break;

        case "synchronized":
          message.info("禁飞区同步完成");
          break;

        case "wait_sync":
          message.info("禁飞区等待同步");
          break;

        default:
          message.info("未知同步状态");
          break;
      }
    }
  };

  //表格配置信息
  const columns = [
    {
      title: "显示/隐藏",
      dataIndex: "select",
      render: (_, record) => (
        <Checkbox
          checked={selectedRowKeys.includes(record.key)}
          onChange={() => {
            handleCheckboxChange(record.key);
            setCheckboxKeys([...CheckboxKeys, record.key]);
          }}
        />
      ),
    },
    {
      title: <div style={{ textAlign: "center" }}>名称</div>,
      dataIndex: "MTitle",
      render: (text) => (
        <span style={{ letterSpacing: "1px" }}>{text}</span> // 调整文字间距
      ),
    },
    {
      title: <div style={{ textAlign: "center" }}>类型</div>,
      dataIndex: "MType_Zh_CN",
    },
    {
      title: <div style={{ textAlign: "center" }}>说明</div>,
      dataIndex: "MContent",
      render: (text) => (
        <span style={{ letterSpacing: "1px" }}>{text}</span> // 调整文字间距
      ),
    },
    {
      title: <div style={{ textAlign: "center" }}>操作</div>,
      dataIndex: "action",
      render: (_, record) => (
        <Tag onClick={() => handleButtonClick(record)}>
          <a>删除</a>
        </Tag>
      ),
    },
  ];

  //删除对应的行
  const handleButtonClick = async (record) => {
    let DeleteData = GetNfzData.filter((item) => item.ID === record.ID)[0];
    let res = await HPost2(`/api/v1/FlyArea/Delete`, DeleteData);
    if (res.err === null) {
      message.success("删除成功");
      GetNfzDataMethod(); //重新获取飞行区数据
    } else {
      message.error("删除失败");
    }
  };

  //切换显示和隐藏
  const handleCheckboxChange = (key) => {
    setSelectedRowKeys((prevSelected) => {
      if (prevSelected.includes(key)) {
        return prevSelected.filter((item) => item !== key); // 取消勾选
      } else {
        return [...prevSelected, key]; // 勾选
      }
    });
  };

  const ToBuFlyAreaToJC = async () => {
    if (!selectDe) {
      message.error("请先选择一个机场");
      return;
    }
    const getSelectedData = (type) => {
      // 筛选出选中的电子围栏或者禁飞区数据
      // 将 selectedRowKeys转换为 Set，提升查找性能
      const selectedRowKeysSet = new Set(selectedRowKeys);

      return GetNfzData.filter((fence) => {
        // 如果被选中，且类型匹配
        return selectedRowKeysSet.has(fence.ID) && fence.MType == type;
      });
    };
    // 1. 获取选中的电子围栏数据
    const fenceFeatures = getSelectedData("dfence")
      .map((item) => {
        try {
          const mData = { ...item, ...JSON.parse(item.MData) };
          return mData;
        } catch (error) {
          console.error("解析 MData 失败:", item, error);
          return null;
        }
      })
      .filter((item) => item !== null);

    // 2. 将设备的位置转换为 turf.js 的 point 对象
    const devicePoint = turf.point([selectDe.Lng, selectDe.Lat]);
    let isInsideAllFences = true; // 初始化为 true
    let MContent = ""; //选中的项目名称

    for (const feature of fenceFeatures) {
      try {
        MContent = feature.MContent;
        let isInside = false; // 默认不在当前围栏内
        if (feature.geometry.type === "Polygon") {
          const polygon = turf.polygon(feature.geometry.coordinates);
          isInside = turf.booleanPointInPolygon(devicePoint, polygon);
        } else if (feature.geometry.type === "Point") {
          const center = feature.geometry.coordinates;
          const radius = feature.properties.radius;
          const distance = turf.distance(devicePoint, turf.point(center), {
            units: "meters",
          });
          isInside = distance <= radius;
        }

        if (!isInside) {
          isInsideAllFences = false; // 只要有一个不在，就设置为 false
          break;
        }
      } catch (error) {
        console.error("电子围栏空间分析失败:", feature, error);
        message.error("电子围栏空间分析失败，请检查数据格式");
        return;
      }
    }

    if (!isInsideAllFences) {
      message.warning(`设备没有处于名为【${MContent}】电子围栏内，无法同步`);
      return;
    }

    // 4. 获取选中的禁飞区数据
    const nfzFeatures = getSelectedData("nfz")
      .map((item) => {
        try {
          const mData = { ...item, ...JSON.parse(item.MData) };
          return mData;
        } catch (error) {
          console.error("解析 MData 失败:", item, error);
          return null;
        }
      })
      .filter((item) => item !== null); // 移除解析失败的元素

    // 5. 检查设备是否位于*任何*禁飞区内
    let isInNfz = false;
    for (const feature of nfzFeatures) {
      try {
        MContent = feature.MContent;
        if (feature.geometry.type === "Polygon") {
          // 创建一个 turf.js 的 polygon 对象
          const polygon = turf.polygon(feature.geometry.coordinates);
          // 使用 turf.booleanPointInPolygon 检查设备是否在禁飞区内
          if (turf.booleanPointInPolygon(devicePoint, polygon)) {
            isInNfz = true;
            break;
          }
        } else if (feature.geometry.type === "Point") {
          // 创建一个 turf.js 的 circle 对象
          const center = feature.geometry.coordinates;
          const radius = feature.properties.radius;
          // 使用 turf.distance 检查设备是否在禁飞区内
          const distance = turf.distance(devicePoint, turf.point(center), {
            units: "meters",
          });
          if (distance <= radius) {
            isInNfz = true;
            break;
          }
        }
      } catch (error) {
        console.error("空间分析或数据处理失败:", feature, error);
        message.error("空间分析或数据处理失败，请检查数据格式");
        return;
      }
    }

    if (isInNfz) {
      message.warning(`设备处于名为【${MContent}】的禁飞区内，无法同步`);
      return;
    }
    // 6. 如果设备不在任何禁飞区内 *且* 被*所有*电子围栏包含，则执行同步操作
    let res = await axiosApi(
      "/api/v1/FlyArea/PushProp?ids=" +
        selectedRowKeys.join(",") +
        "&SN=" +
        selectDe.SN
    );
    if (res && res.code === 1) {
      message.success("同步飞行器区域成功");
    }
  };

  const [isClearArea, setIsClearArea] = useState(false); //是否清空飞行区
  useEffect(() => {
    if (selectDe && selectDe.SN && isClearArea === true) {
      ToBuFlyAreaToJC();
    }
  }, [isClearArea]);

  const clearArea = () => {
    // 清空 selectedRowKeys 状态
    setSelectedRowKeys([]);
    setIsClearArea(true);
    message.success("已清空区域");
  };

  //开始绘制飞行区
  const startDraw = () => {
    setStartdraw(true);
  };

  const SubMitNfz = async () => {
    let SubMitJsonData;
    if (nfztitle == "" || nfzcontent == "") {
      message.info("请填写飞行区名称和说明");
      return;
    }
    if (isEmpty(selectDe)) {
      message.info("请选择机场");
      return;
    }
    if (circlecenter.length == 0 && polygonVertices.length == 0) {
      message.info("请绘制飞行区");
      return;
    } else if (circlecenter.length !== 0 && polygonVertices.length == 0) {
      //生成圆的json数据
      SubMitJsonData = CreateNfz(
        selectDe.SN,
        typeOfPolyfence,
        nfztitle,
        nfzcontent,
        tupeOfPolygon,
        circlecenter,
        circleradius
      );
    } else if (circlecenter.length == 0 && polygonVertices.length !== 0) {
      let GeoPolygon = polygonVertices;
      GeoPolygon.push(temporary); // 临时点位增加到多边形顶点数组
      // 保证多边形顶点数组的首尾坐标相同
      GeoPolygon.push(polygonVertices[0]);
      //生成多边形的json数据
      SubMitJsonData = CreateNfz(
        selectDe.SN,
        typeOfPolyfence,
        nfztitle,
        nfzcontent,
        tupeOfPolygon,
        polygonVertices,
        0
      );
    }
    let res = await HPost2(`/api/v1/FlyArea/Add`, SubMitJsonData);
    if (res.err === null) {
      message.success("提交成功");
      setCirclecenter([]);
      setCircleradius(0);
      setPolygonVertices([]);
      setTemporary([]);
      setStartdraw(false);
      setNfztitle("");
      setNfzcontent("");
      GetNfzDataMethod(); //重新获取飞行区数据
    } else {
      message.error("提交失败");
    }
  };

  //用于生成禁飞区json文件
  const CreateNfz = (
    SN,
    FlyType,
    FlyTitle,
    FlyContent,
    GeoType,
    GeoData,
    GeoRadius
  ) => {
    const PolygonData = {
      OrgCode: SN,
      MType: FlyType,
      MTitle: FlyTitle,
      MContent: FlyContent,
      MData: {
        id: getGuid(),
        type: "Feature",
        geofence_type: FlyType,
        geometry: {
          type: GeoType,
          coordinates:
            GeoType === "Point"
              ? ExchangeCoordinates(GeoData)
              : [ExchangeCoordinates(GeoData)], // 注意这里的双重方括号
        },
        properties: {
          radius: GeoRadius * 1,
          enable: true,
        },
      },
    };
    if (GeoType == "Point") {
      PolygonData.MData.properties.subType = "Circle";
    }

    PolygonData.MData = JSON.stringify(PolygonData.MData);
    return PolygonData;
  };

  //交换传入的坐标数组，使其为[经度,纬度]
  const ExchangeCoordinates = (coordinates) => {
    let coordinatesReverse = [];
    if (coordinates.length == 2) {
      coordinatesReverse = [coordinates[1], coordinates[0]]; // 直接赋值，不套额外的 []
    } else {
      for (let i = 0; i < coordinates.length; i++) {
        coordinatesReverse[i] = [coordinates[i][1], coordinates[i][0]];
      }
    }
    return coordinatesReverse;
  };

  // 地图监听
  const MouseEventHandler = () => {
    // 使用 useMapEvents 来监听地图上的事件
    const mapEvents = useMapEvents({
      //右键点击事件
      contextmenu(e) {
        if (startdraw) {
          setStartdraw(false); // 右键结束绘制
          /*           if (circlecenter.length !== 0 && polygonVertices.length == 0) {
            //生成圆的json数据
          } else if (polygonVertices.length !== 0 && circlecenter.length == 0) {
            //生成多边形的json数据
          } */
        }
      },
      //左键单击事件
      click(e) {
        if (startdraw && e.originalEvent.button === 0) {
          if (tupeOfPolygon == "Polygon" && circlecenter.length == 0) {
            //绘制多边形
            if (polygonVertices.length == 0) {
              setPolygonVertices([[e.latlng.lat, e.latlng.lng]]); // 第一个顶点
            } else {
              setPolygonVertices([
                ...polygonVertices,
                [e.latlng.lat, e.latlng.lng],
              ]); // 增加顶点
            }
          } else if (tupeOfPolygon == "Point" && polygonVertices.length == 0) {
            //绘制圆形
            if (circlecenter.length == 0) {
              const { lat, lng } = e.latlng;
              setCirclecenter([lat, lng]);
              setCircleradius(0); // 初始化半径为0
            }
          }
        }
      },
      //鼠标移动事件
      mousemove(e) {
        if (startdraw) {
          if (tupeOfPolygon == "Polygon") {
            //绘制多边形
            setTemporary([e.latlng.lat, e.latlng.lng]);
          } else if (tupeOfPolygon == "Point") {
            //绘制圆形
            if (circlecenter.length == 2) {
              const [lat, lng] = circlecenter;
              const radius = turf.distance(
                turf.point([lng, lat]),
                turf.point([e.latlng.lng, e.latlng.lat]),
                { units: "meters" }
              );
              setCircleradius(radius.toFixed(2));
            }
          }
        }
      },
      dblclick(e) {
        if (!startdraw) {
          setPolygonVertices([]); // 右键清除多边形
          setTemporary([]); // 右键清除临时顶点
          setCirclecenter([]); // 右键清除圆
          setCircleradius(0); // 右键清除半径
        }
      },
    });

    return null; // 这个组件不需要渲染任何 UI 元素，只用来监听事件
  };

  // 显示机场图标
  const getHomeIcon = () => {
    if (isEmpty(selectDe)) return;
    return (
      <Marker position={[selectDe.Lat, selectDe.Lng]} icon={deviceIcon2}>
        <Tooltip direction="bottom" permanent offset={[0, 28]}>
          {selectDe.DName}
        </Tooltip>
      </Marker>
    );
  };

  // 机场图标位置
  const deviceIcon2 = L.icon({
    iconUrl: require("@/assets/icons/device.png"), //图标地址
    iconAnchor: [21, 10],
    iconSize: [42, 42], // 图标宽高
  });

  //地图标注Url
  const dt_bz2 =
    "http://t0.tianditu.gov.cn/cia_w/wmts?" +
    "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
    "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}" +
    "&tk=d26935ae77bbc1fb3a6866a5b6ff573f";

  return (
    <div
      className="nfzPageForSI"
      style={{
        margin: 0,
        height: "calc(100vh - 57px)",
        // background: "#F5F5FF",
      }}
    >
      <Card
        className="Card"
        title={ !doNotShowLastButton ? (
          <Row align="middle" justify="space-between">
            <Col>
              <LastPageButton title="电子围栏" />
            </Col>{" "}
          </Row>
        ): (
          <div>电子围栏</div>
        )
        }
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            gap: "20px",
          }}
        >
          <div className="left_div">
            {selectDe && (
              <MapContainer
                key={mapkey}
                ref={mapRef}
                layersOptions={null}
                attributionControl={null}
                zoomControl={null}
                preferCanvas={true}
                center={[selectDe.Lat, selectDe.Lng]}
                zoom={16}
                style={{
                  width: "100%",
                  height: "100%",
                  borderRadius: 5.0,
                }}
              >
                <TileLayer
                  attribution={null}
                  url={
                    "https://server.arcgisonline.com/arcgis/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
                  }
                />
                {getHomeIcon()}
                <TileLayer attribution={null} url={dt_bz2} />
                {circlecenter && circleradius && (
                  <Circle
                    center={circlecenter}
                    radius={circleradius}
                    color={"orange"}
                  />
                )}
                {temporary && (
                  <Polygon
                    positions={[...polygonVertices, temporary]} // 临时连接当前鼠标位置
                    color={"orange"}
                    weight={2}
                    // opacity={0.3}
                    dashArray="5,5"
                  />
                )}
                {Add_get_select_flyarea()}
                <MouseEventHandler />
              </MapContainer>
            )}
          </div>
          <div className="right_div">
            <Splitter
              onResize={setSplitterSize}
              layout="vertical"
              className="Tabs_Class"
            >
              <Splitter.Panel size={splitterSize[0]} resizable={false}>
                <div className="splitterOne_Topdiv">
                  {DeviceSelect}
                  <Button
                    style={{
                      marginLeft: "auto",
                      color: "#fff",
                      // backgroundColor: "#0a3cb9cc",
                    }}
                    onClick={clearArea}
                  >
                    清空区域
                  </Button>
                  <Button
                    style={{
                      marginLeft: "20px",
                      color: "#fff",
                      // backgroundColor: "#0a3cb9cc",
                    }}
                    onClick={ToBuFlyAreaToJC}
                  >
                    同步勾选区域至机场
                  </Button>
                  <Button
                    className="QieHaun_Button"
                    onClick={() => setSplitterSize(["0%", "100%"])}
                  >
                    绘制
                  </Button>
                  <UploddFile
                    key={getGuid()}
                    device={selectDe}
                    refrush={GetNfzDataMethod}
                  />
                </div>
                <Table
                  className="table"
                  dataSource={tabledataSource}
                  columns={columns}
                  pagination={false} //取消分页符
                  scroll={{ y: "74vh" }}
                />
              </Splitter.Panel>
              <Splitter.Panel size={splitterSize[1]} resizable={false}>
                <div className="splitterTwo_Topdiv">
                  <Button
                    className="QieHaun_Button"
                    onClick={() => {
                      setSplitterSize(["100%", "0%"]);
                      GetNfzDataMethod(); //重新获取飞行区数据
                    }}
                  >
                    返回
                  </Button>
                </div>

                <div>
                  <label className="Label_Class">名称：</label>
                  <Input
                    value={nfztitle}
                    onChange={(e) => setNfztitle(e.target.value)}
                    placeholder="标题不能为空"
                    className="input_Ttile_Class"
                  ></Input>
                  <div style={{ marginTop: "10px", display: "flex" }}>
                    <label className="Label_Class">说明：</label>
                    <TextArea
                      value={nfzcontent}
                      onChange={(e) => setNfzcontent(e.target.value)}
                      placeholder="说明不能为空"
                      rows={6}
                      className="input_content_Class"
                    ></TextArea>
                  </div>

                  <div style={{ marginTop: "10px" }}>
                    <label className="Label_Class">类型：</label>
                    <Radio.Group
                      onChange={(e) => setTypeOfPolyfence(e.target.value)}
                      value={typeOfPolyfence}
                    >
                      <Radio value={"nfz"} className="Radio_Class">
                        禁飞区
                      </Radio>
                      <Radio value={"dfence"} className="Radio_Class">
                        围栏
                      </Radio>
                    </Radio.Group>
                    <br></br>
                    <label className="Label_Class">形状：</label>
                    <Radio.Group
                      onChange={(e) => {
                        setTupeOfPolygon(e.target.value);
                        setCirclecenter([]);
                        setCircleradius(0);
                        setPolygonVertices([]);
                        setTemporary([]);
                        setStartdraw(false);
                      }}
                      value={tupeOfPolygon}
                    >
                      <Radio value={"Polygon"} className="Radio_Class">
                        多边形
                      </Radio>
                      <Radio value={"Point"} className="Radio_Class">
                        圆形
                      </Radio>
                    </Radio.Group>
                  </div>

                  <Button
                    type="primary"
                    onClick={startDraw}
                    style={{ marginTop: "20px" }}
                  >
                    开始/继续绘制
                  </Button>
                  <Button
                    type="dashed"
                    onClick={SubMitNfz}
                    style={{
                      marginTop: "20px",
                      marginLeft: "10px",
                      color: "#1890ff",
                    }}
                  >
                    提交
                  </Button>
                </div>
              </Splitter.Panel>
            </Splitter>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default SetNfz;
