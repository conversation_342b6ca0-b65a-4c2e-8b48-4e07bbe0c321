import { getBodyH, isEmpty } from '@/utils/utils';
import FLVPlayer from '@/components/FLVPlayer';
import WebRtcPlayer from '@/components/WebRtcPlayer/indexxr';
import WebRtcPlayer2 from '@/components/WebRtcPlayer/indexjc';

import Agora from '@/components/AgoraPlayer';
import MouseCtrPanel  from '@/pages/DJI/FlyToPage/MouseCtrPanel';
import { getGuid } from '@/utils/helper';

//const url1 = 'http://47.108.62.6:8080/live/';
//const url1 = 'webrtc://47.108.62.6/live/';

//const url1 = 'http://47.108.62.6:11012/live/';
//const url1 = 'http://play-agora.schykj.net.cn/live/';

//const url2 = '/api/v1/Live/AgoraToken?sn=';

const Player2 = (width, height, url) => {
    // eslint-disable-next-line no-param-reassign
    if (isEmpty(url)) url = url1;
    return <FLVPlayer url={url} ctl={false} width={width} height={height} />;
  };
  
  const Player1 = (width, height,sn3) => {
    return <Agora  h1={height} w1={width}  sn3={sn3}/>

  };

  // const Player3 = (width, height,url,sn) => {
  //   // eslint-disable-next-line no-param-reassign
  //   if(isEmpty(url)) return <MouseCtrPanel child={<Videos  h1={height} w1={width} url={url3}/>}> </MouseCtrPanel>;
  //   return  <MouseCtrPanel sn={sn} child={<Videos  h1={height} w1={width} url={url}/>}></MouseCtrPanel>;
  // };


const GetPlayer=( width, height,i,sn,isDrc)=>{
    const device=JSON.parse( localStorage.getItem('device'))
    if(isEmpty(device)) return <div></div>
    const url6 = '/api/v1/RtmpSource/Token?sn='+device.SN;
    const url7 = '/api/v1/RtmpSource/Token?sn='+device.SN2;; 

    if(i===1) return  <Agora style={{zIndex: 0,}}  h1={height} w1={width}  sn3={device.SN}/>
    if(i===2) return  <MouseCtrPanel child={<Agora key={device.SN2} h1={height} w1={width}  sn3={device.SN2}/>} sn={sn}/>
    if(i===3) return  <WebRtcPlayer2  sn3={device.SN} ctl={false} width={width} height={height} />;
    if(i===4) return  <WebRtcPlayer  sn3={device.SN2} ctl={false} width={width} height={height} />
    if(i===5) return  <FLVPlayer  sn3={device.SN2+"ai"} ctl={false} width={width} height={height} />
    if(i===6) return  <Agora  h1={height} w1={width}  sn3={device.SN2}/>
    if(i===7) return  <FLVPlayer  sn3={device.SN} ctl={false} width={width} height={height} />
    if(i===8) return  <MouseCtrPanel child={<WebRtcPlayer  sn3={device.SN2} ctl={false} width={width} height={height} />} sn={device.SN}/>
    if(i===9) return  <WebRtcPlayer  sn3={device.SN2+"ai2"} ctl={false} width={width} height={height} />
    if(i===10) return <WebRtcPlayer  sn3={device.SN2} ctl={false} width={width} height={height} />
    if(i===11) return <Agora  h1={height} w1={width}  sn3={device.SN}/>
    if(i===12) return <MouseCtrPanel child={<WebRtcPlayer sn3={sn} ctl={false} width={width} height={height} isDrc={isDrc} />} sn={device.SN}/>
    //if(i===5) return Player1(width,height,url5,sn)

}

// const GetPlayer2=( width, height,i,sn)=>{
//  return <MouseCtrPanel child={GetPlayer2(width,height,i,sn)} sn={sn}/>
// }


export default GetPlayer;