import { useState } from "react";
import { useEffect } from "react";
import LoadPanel from "@/components/IfShowPanel/load_panel";
import { isEmpty, getBodyH } from "@/utils/utils";
import LastPageButton from "@/components/LastPageButton";
import { message, Card, Button, Modal, Table, Space, Tag, Select, Input } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import EditTable from "@/pages/Maps/MapPanel/MapOperatePage/edit_table";
import UpdateTable from "@/pages/Maps/MapPanel/MapOperatePage/update_table";
import { Get2, Post2, Get3,axiosApi } from "@/services/general";
import { dateFormat2, getGuid } from "@/utils/helper";
import dayjs from "dayjs";
const { confirm } = Modal;
const { Search } = Input;
const ModelingTasks = () => {
    const [ifLoad, setIfLoad] = useState(true);
    const [mapList, setMapList] = useState([
        // {
        //     "ID": 15,
        //     "OrgCode": "bdzl",
        //     "WayLineIDS": "d7ce1e6c-8313-4c25-83eb-814b6e7b5318",
        //     "WaylineName": "智慧园区巡检",
        //     "InputKey": "1741599120711374000",
        //     "OutputKey": "1741599120711374000_OUT",
        //     "BizId": "1741599120711374000",
        //     "Remarks": "DSM_TIFF",
        //     "TaskId": "1899030499972657154",
        //     "Status": "PENDING",
        //     "ErrorType": "",
        //     "CreateTM": "2025-03-10T17:32:05.951109Z",
        //     "StartTM": "0001-01-01T00:00:00Z",
        //     "EndTM": "0001-01-01T00:00:00Z"
        // }
    ]);

    const onSearch = (value, _e, info) => {
        getMList(value);
    };

    const getMList = async (searchValue) => {
        try {
            // let pst = await Get2("/api/v1/CreateModele/GetAllModeleList");
            let pst = await axiosApi(`/api/v1/CreateModele/GetAllModeleList`, "GET", { })
            if (pst && pst.length > 0) {
                setMapList(pst);
            }
            setIfLoad(false);
            // setMapList(arr);
        } catch (error) {
            setIfLoad(false);
        }
    };
    const canClick = async (record) => {
        const { TaskId } = record;
        // Query 参数
        // 发送请求
        let params = {
            url: "/api/v1/CreateModele/QueryModeleProcess",
            TaskId: TaskId,
            // 其他查询参数
        };
        // let pst = await Get3(params);
        let pst = await axiosApi(`/api/v1/CreateModele/QueryModeleProcess`, "GET", { taskId: TaskId })
        // 弹出模态框
        if (pst && pst.Progress) {
            confirm({
                title: "任务进度查询",
                okCancel: false,  // 隐藏取消按钮
                content: (
                    <div>
                        <p>任务ID：{record.TaskId}</p>
                        <p>任务名称：{record.WaylineName}</p>
                        <p>任务状态：{record.Status}</p>
                        <p>创建时间：{dateFormat2(record.CreateTM)}</p>
                        <p>
                            当前进度：
                            <span style={{ color: "red" }}> {new Intl.NumberFormat('zh-CN', {
                                style: 'percent',
                                minimumFractionDigits: 0,
                                maximumFractionDigits: (pst.Progress % 1 === 0) ? 0 : 1
                            }).format(pst.Progress / 100)}</span>
                        </p>
                    </div>
                ),
                onOk() { },
            });
        } else {
            message.error("当前查询失败");
        }
    }
    const cancelClick = async (record) => {
        const { TaskId } = record;
        // Query 参数
        // 发送请求
        let params = {
            url: "/api/v1/CreateModele/CancleModelingByTaskId",
            TaskId: TaskId,
            // 其他查询参数
        };
        // let pst = await Get3(params);
        let pst = await axiosApi(`/api/v1/CreateModele/CancleModelingByTaskId`, "GET", { taskId: TaskId })
        if (pst && pst.Success) {
            message.error("取消成功");
        } else {
            message.error("取消失败");
        }
        getMList();
    }
    useEffect(() => {
        getMList();
    }, []);

    const TableCols = () => {
        return [
            {
                title: "航线名称",
                dataIndex: "WaylineName",
                key: "WaylineName",
                align: "center",
            },
            {
                title: "建模类型",
                align: "center",
                dataIndex: "Remarks",
                key: "Remarks",
            },
            {
                title: "任务ID",
                align: "center",
                key: "TaskId",
                dataIndex: "TaskId",
            },
            {
                title: "任务状态",
                align: "center",
                key: "Status",
                dataIndex: "Status",
                render: (text) => <div>
                    {text == "PENDING" ? '等待' : text === "RUNNING" ? '运行中' : text === "COMPLETED" ? '完成'
                        : text === "FAILED" ? '失败' : text === "CANCELED" ? '取消' : ''}
                </div>
            },
            {
                title: "任务进度",
                align: "center",
                key: "Taskstage",
                dataIndex: "Taskstage",
                
            },
            {
                title: "创建时间",
                align: "center",
                key: "CreateTM",
                dataIndex: "CreateTM",
                render: (text) => {
                    return dateFormat2(text);
                }
            },
            {
                title: "进度",
                align: "center",
                key: "Progress",
                dataIndex: "Progress",
                render: (text) => {
                    if (isNaN(text)) return '-';
                    // 将0-100转换为0-1的小数，并格式化为百分比
                    const formatted = new Intl.NumberFormat('zh-CN', {
                        style: 'percent',
                        minimumFractionDigits: 0,
                        maximumFractionDigits: (text % 1 === 0) ? 0 : 2 // 整数显示0位小数，非整数显示2位
                    }).format(text / 100);

                    return <div>{formatted}</div>;
                },
            },
            {
                title: "操作",
                align: "center",
                render: (record) => (
                    <div>
                        {(record.Status === "PENDING" || record.Status === "RUNNING") ? (
                            <div>
                                <Space size="middle">
                                    <Tag>
                                        <span onClick={() => { canClick(record) }}>进度查询</span>
                                    </Tag>
                                    <Tag>
                                        <span style={{ color: "red" }} onClick={() => {
                                            cancelClick(record)
                                        }}>取消</span>
                                    </Tag>
                                </Space>
                            </div>
                        ) : '--'}
                    </div>

                ),
            },
        ];
    };


    let extra = (
        <div
            style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                width: "100%",
                gap: "10px",
            }}
        >
            <Search
                enterButton
                placeholder="输入关键词搜索..."
                onSearch={onSearch}
                key={getGuid()}
            />
            <span style={{ marginLeft: 6.0 }}><Button type="primary" onClick={() => getMList()}>刷新</Button> </span>

        </div >
    );
    return (
        <Card title={<LastPageButton title="建模任务" />} extra={extra}>
            <div style={{ cursor: "pointer" }}>
                <div>
                    {/* {isEmpty(mapList) ? (
                        <div />
                    ) : ( */}
                    <Table
                        pagination={{
                            defaultPageSize: 10,
                            defaultCurrent: 1,
                            showQuickJumper: true,
                            pageSizeOptions: [10, 20, 30, 40, 50],
                            showSizeChanger: true,
                            locale: {
                                items_per_page: "条/页",
                                jump_tp: "跳至",
                                page: "页",
                            },
                        }}
                        key={getGuid()}
                        dataSource={mapList}
                        bordered
                        columns={TableCols()}
                        size="small"
                        scroll={{ y: getBodyH(276) }}
                    />
                    {/* )} */}
                </div>
            </div>
        </Card>
    );
};

export default ModelingTasks;
