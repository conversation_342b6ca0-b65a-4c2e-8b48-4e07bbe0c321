/* 虚拟鼠标组件样式 */
.virtualCursor {
  position: absolute;
  z-index: 9999;
  pointer-events: none;
  user-select: none;
  transition: transform 0.05s ease-out;
  
  /* 确保在所有地图元素之上 */
  will-change: transform;
  
  .cursorIcon {
    display: inline-block;
    font-size: 16px;
    line-height: 1;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    transition: all 0.2s ease;
  }
  
  /* 拖拽状态 */
  &.dragging {
    .cursorIcon {
      font-size: 18px;
      filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.4));
      transform: scale(1.1);
    }
  }
  
  /* 缩放状态 */
  &.zooming {
    .cursorIcon {
      animation: zoomPulse 0.5s ease-in-out infinite alternate;
    }
  }
  
  /* 状态指示器 */
  .statusIndicator {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    animation: fadeInOut 0.3s ease-in-out forwards;
    
    /* 小箭头 */
    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 4px solid transparent;
      border-top-color: rgba(0, 0, 0, 0.8);
    }
  }
  
  .dragIndicator {
    color: #007bff;
  }
  
  .zoomIndicator {
    color: #28a745;
  }
}

/* 动画定义 */
@keyframes zoomPulse {
  from { 
    transform: scale(1); 
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  }
  to { 
    transform: scale(1.3); 
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.5));
  }
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(-5px);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  100% {
    opacity: 0.8;
    transform: translateX(-50%) translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .virtualCursor {
    .cursorIcon {
      font-size: 14px;
    }
    
    &.dragging .cursorIcon {
      font-size: 16px;
    }
    
    .statusIndicator {
      font-size: 10px;
      padding: 1px 6px;
    }
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .virtualCursor {
    .cursorIcon {
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.8));
    }
    
    .statusIndicator {
      background: rgba(0, 0, 0, 0.95);
      border: 1px solid white;
    }
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .virtualCursor {
    transition: none;
    
    .cursorIcon {
      transition: none;
      animation: none !important;
    }
    
    &.zooming .cursorIcon {
      animation: none;
      transform: scale(1.2);
    }
    
    .statusIndicator {
      animation: none;
      opacity: 0.8;
    }
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .virtualCursor {
    .statusIndicator {
      background: rgba(255, 255, 255, 0.9);
      color: black;
      
      &::after {
        border-top-color: rgba(255, 255, 255, 0.9);
      }
    }
  }
}
