import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Checkbox,
  InputNumber,
  Radio,
  Upload,
  message,
  Spin,
  Splitter,
  Tooltip,
  Descriptions,
  Switch,
  List,
} from 'antd';
import TableCols from './table';
import { axiosApi } from "@/services/general";
import { Get2, Post2 } from '@/services/general';
import { useState, useEffect } from 'react';
import { useModel } from "umi";
import FormItem from 'antd/es/form/FormItem';
import { getDeviceName, WayLineTypeToString} from "@/utils/utils";
import { QuestionCircleOutlined, InboxOutlined } from '@ant-design/icons';
import { queryPage } from "@/utils/MyRoute";
import zhCN from 'antd/lib/locale/zh_CN';
import AImodelSelector from '@/pages/GT/components/RecordsPage/AImodelSelector.js';
import { DynamicDataTable } from '@/pages/SI/components/Common';

const RecordsPage = ({ type, title }) => {
  const { TextArea } = Input;
  const [loading, setLoading] = useState(false);
  const [loading2, setLoading2] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [WayLineList, setWayLineList] = useState([]);
  const [WayLineListOptions, setWayLineListOptions] = useState([]);
  const [selectedWayLines, setSelectedWayLines] = useState([]);
  const [tempSelectedWayLines, setTempSelectedWayLines] = useState([]);
  const { setPage, lastPage, currentPage } = useModel("pageModel");
  const [currentTablePage, setCurrentTablePage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isEdit, setIsEdit] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  // const [modalVisible2, setModalVisible2] = useState(false);
  const [modalVisible3, setModalVisible3] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [form] = Form.useForm();

  const [isZS, setIsZS] = useState(0);
  const [ZsFBL, setZsFBL] = useState(0);
  const [IsSW, setIsSW] = useState(0);
  const [SwQlh, setSwQlh] = useState(false);
  const [SwWlys, setSwWlys] = useState(0);
  const [SwJhys, setSwJhys] = useState(0);
  const [pendingFiles, setPendingFiles] = useState([]); // 等待上传的文件
  const [fileAddress, setFileAddress] = useState(); // 上传成功的文件地址
  const token = localStorage.getItem("token");

  const [taskTypeList, setTaskTypeList] = useState([]); // 任务类型列表
  const [modelList, setModelList] = useState([]); // 模型列表
  const [selectedModels, setSelectedModels] = useState([]); // 选择的模型
  const [cls, setCls] = useState({}) // 识别内容 (数字)
  const [user, setUser] = useState(); // 用户信息
  const [editData, setEditData] = useState();  // 点击编辑按钮返回的数据
  const IsSIJump = localStorage.getItem("IsSIJump");

  let TextureCompressionList = [
    { value: 0, label: "无纹理压缩" },
    { value: 1, label: "WEBP压缩" },
    { value: 2, label: "KTX压缩" },
    { value: 3, label: "CRN压缩" },
    { value: 4, label: "KTX2.0压缩" },
  ];
  let GeometricCompressionList = [
    { value: 0, label: "无几何压缩" },
    { value: 1, label: "DRACO压缩" },
  ];

  // 页面挂载完成，获取初始数据
  useEffect(() => {
    getRecords();
    getAllList();
    getModelData();
    getTaskType();
    setUser(JSON.parse(localStorage.getItem("user")));
  }, []);

  // 当数据变化时重置页码
  useEffect(() => {
    setCurrentTablePage(1);
  }, [dataSource]);

  const handlePageChange = (page) => {
    setPage(queryPage(page));
  };
  // 获取巡查记录表格数据
  const getRecords = async () => {
    try {
      setLoading(true);
      const res = await axiosApi('/api/v1/surveytask/GetAllList', 'GET', null);
      if (type) {
        const filteredData = res.data.filter(item => item.TaskType === type);
        setDataSource(filteredData);
      } else {
        setDataSource(res.data);
      }
      setLoading(false);
    } catch (error) {
      console.log(error);
    }
  };

  // 获取所有航线
  const getAllList = async () => {
    try {
      setLoading2(true);
      const res = await axiosApi('/api/v1/WayLine/GetAllList', 'GET', null);
      setWayLineList(res)
      setWayLineListOptions(res.map(item => ({
        label: item.WayLineName,
        value: item.WanLineId,
        wayLineType: item.WayLineType,
        airport: getDeviceName(item.SN)
      }))
      )
      setLoading2(false);
    }
    catch (error) {
      console.log(error);
    }
  };

  // 获取AI模型列表
  const getModelData = async () => {
    const pst = await Get2('/api/v1/AI/GetModelList', {});
    setModelList(pst);
  };

  // 获取任务类型
  const getTaskType = async () => {
    try {
      const taskTypeList = await axiosApi(`/api/v1/SystemInfo/Get`, 'GET', {
        sid: 'gt',
      });
      setTaskTypeList(taskTypeList.data);
    } catch (error) {
      console.log(error);
    }
  };

  const exr = <div><Button type="primary" onClick={() => { openCreatTask() }}>新增</Button> </div>

  // 打开新增任务页面
  const openCreatTask = () => {
    setModalVisible(true);
    form.resetFields();
    // 当有type时 自动选择对应任务类型
    if (type) {
      const matchedType = taskTypeList.find(item => item.TheURL === type);
      if (matchedType) {
        form.setFieldsValue({
          TaskType: matchedType.TheURL
        });
      }
    }
    reset();
  };

  // 关闭窗口
  const cloceCreatTask = () => {
    setModalVisible(false);
    setIsEdit(false);
    form.resetFields();
    reset();
  };

  const reset = () => {
    setSelectedWayLines([]);
    setIsZS(0);
    setZsFBL(0);
    setIsSW(0);
    setSwQlh(false);
    setSwWlys(0);
    setSwJhys(0);
    setPendingFiles([]);
    setSelectedModels([]);
    setCls({});
    setFileAddress();
  };

  const uploadFiles = async () => {
    // 提取原始文件对象
    const rawFiles = pendingFiles.map(file => file.originFileObj || file);
    // 如果没有选择文件 则不上传
    if (rawFiles.length === 0) {
      return '';
    }
    const formData = new FormData();
    rawFiles.forEach(file => {
      formData.append('file', file);
    });
    formData.append('chunkNumber', '');
    formData.append('totalChunks', '');
    formData.append('identifier', '');
    try {
      const res = await axiosApi(
        "/api/v1/upload/file",
        "POST",
        formData,
        {
          headers: {
            'auth': token,
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': '*',
            "Content-Type": "multipart/form-data",
          },
        }
      );
      // console.log('上传文件成功', res);
      return res.data.url;

    } catch (error) {
      console.log('上传文件失败', error);
      message.error('上传文件失败');
      return '';
    }
  }

  const handleSubmit = async () => {
    // 上传文件 获取返回的地址
    try {
      const address = await uploadFiles();
      setFileAddress(address);
      const data = {
        BaseInfo: {
          OrgCode: user.OrgCode,
          TaskDesc: form.getFieldValue('TaskDesc'),
          TaskType: form.getFieldValue('TaskType'),
          TaskContent: form.getFieldValue('TaskContent'),
          Unit: form.getFieldValue('Unit'),
          Cyclicplan: form.getFieldValue('Cyclicplan'),
          TableName: type,
        },
        WaylineIDs: form.getFieldValue('WayLineID'),
        SurveyTaskWaylineDetail: {
          IsZs: isZS,
          ZsFBL: ZsFBL,
          IsSw: IsSW,
          SwQlh: SwQlh,
          SwWlys: SwWlys,
          SwJhys: SwJhys,
          StyleFile: fileAddress,
        },
        SurveyTaskAiTmpl: selectedModels.map(model => {
          const labels = model.AList?.split(',') || [];
          const selectedLabels = (cls[model.Guid] || [])
            .map(index => labels[index]?.trim())
            .filter(Boolean);
          return {
            TaskName: '',
            TaskType: '',
            ModelID: model.Guid,
            ModelName: model.AName,
            ModelPath: model.ObjectName,
            Acls: (cls[model.Guid] || []).join(','),
            AclsName: selectedLabels.join(', ')
          }
        }),
      };
      // console.log('@@data', data);

      const res = await axiosApi('/api/v1/Survey/CreateSmartSurveyTask', 'POST', data);

      if (res.code === 0) {
        message.error(res.msg)
      }
      if (res.code === 1) {
        message.success('新增任务成功')
        getRecords(); // 更新数据
      }
      console.log('新增任务', res);
      cloceCreatTask();
    } catch (error) {
      console.log('新增任务失败', error);
      message.error('新增任务失败');
    }
  };

  const openEditModal = (data) => {
    console.log('@@@打开编辑弹窗', data);
    setEditData(data);
    setIsEdit(true);
    setModalVisible(true);
    // 先清空弹窗
    form.resetFields();
    reset();
    // 将查询到的数据填入弹窗
    form.setFieldsValue({
      TaskDesc: data.baseInfo.TaskDesc,
      TaskContent: data.baseInfo.TaskContent,
      Unit: data.baseInfo.Unit,
      Cyclicplan: data.baseInfo.Cyclicplan,
      TaskType: data.baseInfo.TaskType,
      WayLineID: data.wayLineIDs,
    });
    // 同步更新已选航线列表
    const selected = WayLineList.filter(item =>
      data.wayLineIDs.includes(item.WanLineId)
    );
    setSelectedWayLines(selected);

    setIsSW(data.surveyTaskWayLineDetail.IsSw);
    setIsZS(data.surveyTaskWayLineDetail.IsZs);
    setSwQlh(data.surveyTaskWayLineDetail.SwQlh);
    setSwWlys(data.surveyTaskWayLineDetail.SwWlys);
    setSwJhys(data.surveyTaskWayLineDetail.SwJhys);
    setFileAddress(data.surveyTaskWayLineDetail.StyleFile);

    const tempSelectedModels = data.surveyTaskAiTmpl
      .map(tmpl => {

        return modelList.find(model =>
          model.ModelID === tmpl.ModelID ||
          model.Guid === tmpl.ModelID
        )
      })
      .filter(Boolean)

    setSelectedModels(tempSelectedModels)

    const initialCls = data.surveyTaskAiTmpl.reduce((acc, tmpl) => {
      const model = modelList.find(m => m.Guid === tmpl.ModelID)
      if (model && tmpl.Acls) {
        acc[model.Guid] = tmpl.Acls.split(',')
          .filter(str => str !== '') // 过滤空字符串
          .map(Number)
      }
      return acc
    }, {})
    setCls(initialCls)
  };

  const handleEdit = async () => {
    try {
      // 如果又新上传了样式文件，则上传并更新地址 否则采用原先的地址信息
      if (pendingFiles.length > 0) {
        const address = await uploadFiles();
        setFileAddress(address);
      }
      const data = {
        baseInfo: {
          ID: editData.baseInfo.ID,
          orgCode: user.OrgCode,
          TaskID: editData.baseInfo.TaskID,
          TaskDesc: form.getFieldValue('TaskDesc'),
          TaskContent: form.getFieldValue('TaskContent'),
          TaskType: form.getFieldValue('TaskType'),
          Unit: form.getFieldValue('Unit'),
          Cyclicplan: form.getFieldValue('Cyclicplan'),
          TaskKeyValue: editData.baseInfo.TaskKeyValue,
          TableName: type,
          CreateTM: editData.baseInfo.CreateTM,
          State: editData.baseInfo.State,
          is_deleted: editData.baseInfo.is_deleted,
        },
        wayLineIDs: form.getFieldValue('WayLineID'),
        surveyTaskWayLineDetail: {
          ID: editData.surveyTaskWayLineDetail.ID,
          SurveyTaskID: editData.surveyTaskWayLineDetail.SurveyTaskID,
          IsZs: isZS,
          ZsFBL: ZsFBL,
          IsSw: IsSW,
          SwQlh: SwQlh,
          SwWlys: SwWlys,
          SwJhys: SwJhys,
          StyleFile: fileAddress,
          CreateTime: editData.surveyTaskWayLineDetail.CreateTime,
        },
        surveyTaskAiTmpl: selectedModels.map(model => {
          const labels = model.AList?.split(',') || [];
          const selectedLabels = (cls[model.Guid] || [])
            .map(index => labels[index]?.trim())
            .filter(Boolean);
          return {
            ID: null,
            TaskName: '',
            TaskType: '',
            ModelID: model.Guid,
            ModelName: model.AName,
            ModelPath: model.ObjectName,
            Acls: (cls[model.Guid] || []).join(','),
            AclsName: selectedLabels.join(', '),
            CreateTime: editData.surveyTaskWayLineDetail.CreateTime,
            SurveyTaskID: editData.surveyTaskWayLineDetail.CreateTime,
          }
        }),
      }

      const res = await axiosApi('/api/v1/Survey/UpdateSurveyTaskContent', 'POST', data);
      if (res.code === 0) {
        message.error(res.msg)
      }
      if (res.code === 1) {
        message.success('编辑任务成功')
        getRecords(); // 更新数据
        cloceCreatTask(); // 关闭弹窗
      }
    } catch (error) {
      console.log('编辑任务失败', error);
      message.error('编辑任务失败');
    }
  };

  // 当打开选择航线弹窗时同步已选数据
  useEffect(() => {
    if (modalVisible3) {
      setTempSelectedWayLines(selectedWayLines);
    }
  }, [modalVisible3]);

  return (
    <div className='blackBackground'>
      <Card
        title={title ? title : (IsSIJump === 'true') ? '巡飞计划' : currentPage}
        extra={exr}
        style={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          flex: 1,
          overflow: 'hidden',
          padding: 0,
        }}
      >
        <div style={{
          flex: 1,
          overflow: 'hidden',
          padding: '24px'
        }}>
          <DynamicDataTable
            pagination={{
              defaultPageSize: pageSize,
              current: currentTablePage,
              // pageSizeOptions: [10, 20, 40, 60, 80],
              showSizeChanger: true,
              // tableLayout: 'auto',
              // width: '100%',
              onShowSizeChange: (current, size) => {
                setPageSize(size);
                setCurrentTablePage(1);
              },
              onChange: (page) => setCurrentTablePage(page),
              total: dataSource.length,
              locale: {
                items_per_page: "条/页",
                jump_to: "跳至",
                page: "页",
              },
              showTotal: (total) => (
                <span className="custom-pagination-text">
                  共 {total} 条
                </span>
              ),
              className: "custom-pagination-container",
              showQuickJumper: true,
            }}
            rowKey={(record) => record.ID}
            loading={loading}
            dataSource={dataSource}
            columns={TableCols(handlePageChange, getRecords, openEditModal, taskTypeList)}
            scroll={{
              scrollToFirstRowOnChange: true,
              y: `calc(100vh - 310px)`,
              // y:600,  
            }}
          />
        </div>
        {/* 新增任务弹窗 / 编辑弹窗 */}
        <Modal
          title={isEdit ? '编辑任务' : '新增任务'}
          open={modalVisible}
          onCancel={() => cloceCreatTask()}
          footer={null}
          width="60vw"
          styles={{
            body: {
              maxHeight: '70vh',
              overflowY: 'auto',
              padding: 24
            }
          }}
        >
          <Form layout="horizontal" form={form} style={{ maxWidth: 1200, margin: '0 auto' }}>

            <div style={{ display: 'flex', gap: 16, marginBottom: 16 }}>
              <Form.Item
                name="TaskDesc"
                label="任务名称"
                rules={[{ required: true }]}
                style={{ flex: 1 }}
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 18 }}
              >
                <Input placeholder="请输入任务名称" />
              </Form.Item>

              <Form.Item
                name="TaskType"
                label="应用场景"
                rules={[{ required: true }]}
                style={{ flex: 1 }}
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 18 }}
              >
                <Select
                  placeholder="请选择应用场景"
                  options={taskTypeList.map(item => ({
                    value: item.TheURL,
                    label: item.Title
                  }))}
                />
              </Form.Item>
            </div>

            <div style={{ display: 'flex', gap: 16, marginBottom: 16 }}>
              <Form.Item
                name="Unit"
                label="所属单位"
                rules={[{ required: true, message: '请输入所属单位' }]}
                style={{
                  flex: 1,
                  maxWidth: 'calc(50% - 8px)'
                }}
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 18 }}
              >
                <Input
                  placeholder="请输入所属单位"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </div>

            <Form.Item
              name="TaskContent"
              label="任务内容"
              labelCol={{ span: 3 }}
              wrapperCol={{ span: 21 }}
              style={{ marginBottom: 16 }}
            >
              <TextArea autoSize={{ minRows: 3 }} />
            </Form.Item>

            {/* 循环计划快捷按钮 */}
            <div style={{ marginBottom: 12, marginLeft: '12.5%' }}>
              {[
                { label: '每2秒', value: '0/2 * * * * ?' },
                { label: '每2分钟', value: '0 0/2 * * * ?' },
                { label: '每月1日2点', value: '0 0 2 1 * ?' },
                { label: '工作日10:15', value: '0 15 10 ? * MON-FRI' },
                { label: '每天3个时间点', value: '0 0 10,14,16 * * ?' },
                { label: '工作时间每半小时', value: '0 0/30 9-17 * * ?' },
                { label: '每周三中午', value: '0 0 12 ? * WED' },
              ].map((item) => (
                <Button
                  key={item.value}
                  size="small"
                  type="link"
                  style={{ padding: '0 4px', height: 22 }}
                  onClick={() => form.setFieldsValue({ Cyclicplan: item.value })}
                >
                  {item.label}
                </Button>
              ))}
            </div>

            <Form.Item
              name="Cyclicplan"
              labelCol={{ span: 3 }}
              wrapperCol={{ span: 21 }}
              label={
                <span style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  循环计划
                  <Tooltip
                    title={
                      <div style={{ whiteSpace: 'pre-line', lineHeight: 1.5 }}>
                        {`循环计划规则输入说明:\n
                0/2 * * * * ?   表示每2秒 执行任务\n
                0 0/2 * * * ?    表示每2分钟 执行任务\n
                0 0 2 1 * ?   表示在每月的1日的凌晨2点调整任务\n
                0 15 10 ? * MON-FRI   表示周一到周五每天上午10:15执行作业\n
                0 0 10,14,16 * * ?   每天上午10点，下午2点，4点\n
                0 0/30 9-17 * * ?   朝九晚五工作时间内每半小时\n
                0 0 12 ? * WED    表示每个星期三中午12点`}
                      </div>
                    }
                  >
                    <QuestionCircleOutlined style={{ color: 'blue' }} />
                  </Tooltip>
                </span>
              }
              rules={[{ required: true }]}
            >
              <TextArea autoSize={{ minRows: 3 }} />
            </Form.Item>


            <Form.Item
              name="WayLineID"
              label="关联航线"
              rules={[{ required: true, message: '请选择关联航线' }]}
              labelCol={{ span: 3 }}
              wrapperCol={{ span: 21 }}>
              <Button
                type="primary"
                onClick={() => setModalVisible3(true)}
              >
                {selectedWayLines.length > 0 ?
                  `已选择${selectedWayLines.length}条航线` :
                  "选择航线"}
              </Button>

              <Modal
                title="选择关联航线"
                open={modalVisible3}
                width="50vw"
                footer={null}
                onCancel={() => setModalVisible3(false)}
              >
                <div style={{
                  position: 'relative',
                  height: '60vh',
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  {/* 搜索框 */}
                  <div style={{ marginBottom: 16 }}>
                    <Input.Search
                      placeholder="搜索航线名称/类型/机场"
                      onChange={(e) => setSearchText(e.target.value)}
                      allowClear
                    />
                  </div>
                  {/* 滚动区域 */}
                  <div style={{
                    // flex: 1,
                    // height: '50vh',
                    overflowY: 'auto',
                    paddingRight: 8
                  }}>
                    <List
                      dataSource={WayLineList.filter(item => {
                        const input = searchText.toLowerCase();
                        return (
                          item.WayLineName.toLowerCase().includes(input) ||
                          item.WayLineType.toLowerCase().includes(input) ||
                          getDeviceName(item.SN).toLowerCase().includes(input)
                        );
                      })}
                      renderItem={item => (
                        <List.Item
                          key={item.WanLineId}
                          style={{
                            padding: 12,
                            cursor: 'pointer',
                            backgroundColor: tempSelectedWayLines.some(i => i.WanLineId === item.WanLineId)
                              ? 'rgba(255,255,255,0.08)'
                              : 'inherit',
                            borderBottom: '1px solid #f0f0f0'
                          }}
                          onClick={() => {
                            setTempSelectedWayLines(prev => {
                              const exists = prev.some(i => i.WanLineId === item.WanLineId);
                              return exists
                                ? prev.filter(i => i.WanLineId !== item.WanLineId)
                                : [...prev, item];
                            });
                          }}
                        >
                          <div>
                            <div style={{ fontWeight: 500 }}>{item.WayLineName}</div>
                            <div style={{ color: '#999999', marginTop: 8 }}>
                              <span>类型：{WayLineTypeToString(item.WayLineType)}</span>
                              <span style={{ marginLeft: 24 }}>机场：{getDeviceName(item.SN)}</span>
                            </div>
                          </div>
                        </List.Item>
                      )}
                    />
                  </div>

                  <div style={{
                    position: 'relative',
                    right: 16,
                    bottom: 0,
                    // background: '#fff',
                    padding: '16px 0 0 0',
                    borderTop: '1px solid #f0f0f0',
                    textAlign: 'right'
                  }}>
                    <Button
                      style={{ marginRight: 16 }}
                      onClick={() => setModalVisible3(false)}
                    >
                      取消
                    </Button>
                    <Button
                      type="primary"
                      onClick={() => {
                        setSelectedWayLines(tempSelectedWayLines);
                        form.setFieldsValue({
                          WayLineID: tempSelectedWayLines.map(item => item.WanLineId)
                        });
                        setModalVisible3(false);
                      }}
                    >
                      确认
                    </Button>
                  </div>
                </div>
              </Modal>

            </Form.Item>

            <Table
              size="small"
              rowKey="WanLineId"
              pagination={false}
              dataSource={selectedWayLines}
              columns={[
                {
                  align: "center",
                  title: '航线名称',
                  dataIndex: 'WayLineName',
                },
                {
                  align: "center",
                  title: '航线类型',
                  // dataIndex: 'WayLineType',
                  render: (record) => <div>{WayLineTypeToString(record.WayLineType)}</div>,
                },
                {
                  align: "center",
                  title: '操作',
                  render: (_, record) => (
                    <Button
                      type="link"
                      danger
                      onClick={() => {
                        const newSelected = selectedWayLines.filter(
                          item => item.WanLineId !== record.WanLineId
                        );
                        setSelectedWayLines(newSelected);
                        form.setFieldsValue({
                          WayLineID: newSelected.map(item => item.WanLineId)
                        });
                      }}
                    >
                      删除
                    </Button>
                  )
                }
              ]}
            />


            <Descriptions column={2} colon={false} bordered style={{ marginTop: 16 }} labelStyle={{
              width: '20%',
              paddingRight: 16
            }}
              contentStyle={{
                width: '80%'
              }}>
              <Descriptions.Item label="生成正射DOM" span={24}>
                <Radio.Group value={isZS} onChange={(e) => setIsZS(e.target.value)}>
                  <Radio key={1} value={1}>
                    是
                  </Radio>
                  <Radio key={0} value={0}>
                    否
                  </Radio>
                </Radio.Group>
              </Descriptions.Item>
              {isZS == 1 && (
                <>
                  <Descriptions.Item label="正射分辨率" bordered span={24}>
                    <InputNumber
                      value={ZsFBL}
                      onChange={(value) => {
                        setZsFBL(value);
                      }}
                      allowClear
                      controls={false}
                    />
                  </Descriptions.Item>
                </>
              )}
              <Descriptions.Item label="生成三维B3DM" span={24}>
                <Radio.Group value={IsSW} onChange={(e) => setIsSW(e.target.value)}>
                  <Radio key={1} value={1}>
                    是
                  </Radio>
                  <Radio key={0} value={0}>
                    否
                  </Radio>
                </Radio.Group>
              </Descriptions.Item>

              {IsSW == 1 && (
                <>
                  <Descriptions.Item label="三维是否轻量化" span={24}>
                    <Switch
                      value={SwQlh}
                      onChange={(e) => {
                        setSwQlh(e);
                      }}
                      checkedChildren="开启"
                      unCheckedChildren="关闭"
                    />
                  </Descriptions.Item>
                  <Descriptions.Item label="纹理压缩" span={24}>
                    <Select
                      value={SwWlys}
                      allowClear
                      onClear={() => setSwWlys(null)}
                      placeholder={"选择纹理压缩类型"}
                      defaultValue={TextureCompressionList[SwWlys]?.label}
                      onSelect={(value) => {
                        setSwWlys(Number(value));
                      }}
                    >
                      {TextureCompressionList.map((item) => (
                        <Select.Option
                          key={item.value}
                          value={item.value}
                        >
                          {item.label}
                        </Select.Option>
                      ))}
                    </Select>
                  </Descriptions.Item>
                  <Descriptions.Item label="几何压缩" span={24}>
                    <Select
                      value={SwJhys}
                      allowClear
                      onClear={() => setSwJhys(null)}
                      placeholder={"选择几何压缩类型"}
                      defaultValue={GeometricCompressionList[SwJhys]?.label}
                      onSelect={(value) => {
                        setSwJhys(Number(value));
                      }}
                    >
                      {GeometricCompressionList.map((item) => (
                        <Select.Option
                          key={item.value}
                          value={item.value}
                        >
                          {item.label}
                        </Select.Option>
                      ))}
                    </Select>
                  </Descriptions.Item>
                </>
              )}
            </Descriptions>


            <AImodelSelector
              selectedModels={selectedModels}
              cls={cls}
              onChange={({ selectedModels, cls }) => {
                if (selectedModels) setSelectedModels(selectedModels);
                if (cls) setCls(cls);
              }}
            />

            {/* 操作按钮 */}
            <div style={{
              marginTop: 32,
              paddingTop: 24,
              textAlign: "right"
            }}>
              <Button
                type="primary"
                onClick={isEdit ? handleEdit : handleSubmit}
                style={{ width: 120, height: 38 }}
              >
                {isEdit ? "保存" : "提交"}
              </Button>
              <Button
                style={{ marginLeft: 16, width: 120, height: 38 }}
                onClick={cloceCreatTask}
              >
                取消
              </Button>
            </div>
          </Form>
        </Modal>
      </Card>
    </div>

  );
};

export default RecordsPage;