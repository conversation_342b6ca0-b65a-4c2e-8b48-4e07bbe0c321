.header_back {
    width: 100%;
    background: #091A1B;
}

#MyHead {
    width: 100%;
    height: 47px;
    background: url("@/pages/SI/assets/image/header/顶部.png");
    background-size: 100% 140%;
    background-repeat: no-repeat;
    display: grid;
    grid-template-columns: 33.33% 33.33% 33.33%;
    position: relative;
    z-index: 9999;
    cursor: pointer;
    color: #fff;
}

.header_center {
    padding-bottom: 3%;
}

.header_center_title {
    font-weight: bold;
    font-size: 1.4rem;
    background-image: linear-gradient(to bottom, #ffffff 20%, #08d6bd);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

}

.header_left {
    margin-left: 30%;
}

.header_right {
    margin-right: 30%;
}

.header_left,
.header_right {
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-family: 'kaiti';
    font-weight: bolder;
}

.header_left_item {
    transform: rotate(5deg);
}

.header_right_item {
    transform: rotate(-5deg);
}

.header_left_item,
.header_right_item {
    width: 130px;
    height: 55px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
}

.header_left_item:nth-child(1) {
    margin-bottom: 10px;
}

.header_right_item:nth-child(2) {
    margin-bottom: 10px;
}



.header_left_item>span,
.header_right_item>span {
    background-image: linear-gradient(to bottom, #ffffff 20%, #08d6bd);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}


.header_center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.header_other {
    position: absolute;
    top: 20%;
    right: 2%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
}

.header_other_setting {
    width: 35px;
    height: 25px;
    background: url("@/pages/SI/assets/image/header/管理.png") center center no-repeat;
    background-size: 100% 100%;

}

.header_other_user {
    width: 35px;
    height: 25px;
    background: url("@/pages/SI/assets/image/header/我的.png") center center no-repeat;
    background-size: 100% 100%;
    position: relative;
}

.header_other_user:hover .header_other_userBox {
    padding: 5px;
    height: 50px;
    border: 1px solid #1fbaa6;
    transition: all 0.3s;
}

.header_other_userBox:hover .header_other_userBox {
    padding: 5px;
    height: 50px;
    border: 1px solid #1fbaa6;
    transition: all 0.3s;
}

.header_other_userBox {
    cursor: pointer;
    height: 0;
    top: 25px;
    left: 0;
    font-size: 12px;
    position: absolute;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    background: linear-gradient(45deg, rgba(#08E7CB, 0.5) 20%, rgba(#0d2b2b, 0.5));
}
.btn_left_back{
    background: url("@/pages/SI/assets/image/header/left_btn.png");
}
.btn_right_back{
    background: url("@/pages/SI/assets/image/header/right_btn.png");
}
.btn_left_back_active{
    background: url("@/pages/SI/assets/image/header/left_btn_active.png");
}
.btn_right_back_active{
    background: url("@/pages/SI/assets/image/header/right_btn_active.png");
}

