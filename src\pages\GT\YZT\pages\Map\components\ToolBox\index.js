import React, { useState, useCallback } from 'react'
import { Dropdown, Button, message } from 'antd'
import { ToolOutlined, DownOutlined } from '@ant-design/icons'
import styles from './index.module.less'

/**
 * 工具箱组件
 * 提供卷帘、多屏对比、查询等功能的下拉菜单
 */
const ToolBox = ({
  onCurtainToggle,
  isCurtainMode = false,
  onMultiScreenCompare,
  onTemporalToggle,
  isTemporalMode = false,
  onQuery,
  className = ''
}) => {
  const [dropdownVisible, setDropdownVisible] = useState(false)

  // 处理卷帘功能
  const handleCurtainClick = useCallback(() => {
    if (onCurtainToggle) {
      onCurtainToggle()
    }
    setDropdownVisible(false)
  }, [onCurtainToggle])

  // 处理多屏对比功能
  const handleMultiScreenClick = useCallback(() => {
    if (onMultiScreenCompare) {
      onMultiScreenCompare()
    }
    setDropdownVisible(false)
  }, [onMultiScreenCompare])

  // 处理多时相对比功能
  const handleTemporalClick = useCallback(() => {
    if (onTemporalToggle) {
      onTemporalToggle()
    }
    setDropdownVisible(false)
  }, [onTemporalToggle])

  // 处理查询功能（暂未实现）
  const handleQueryClick = useCallback(() => {
    message.info('查询功能正在开发中...')
    if (onQuery) {
      onQuery()
    }
    setDropdownVisible(false)
  }, [onQuery])

  // 下拉菜单项配置
  const menuItems = [
    {
      key: 'curtain',
      label: (
        <div className={`${styles.toolboxMenuItem} ${isCurtainMode ? styles.active : ''}`}>
          <span className={styles.menuItemText}>卷帘</span>
          {isCurtainMode && <span className={styles.menuItemStatus}>已启用</span>}
        </div>
      ),
      onClick: handleCurtainClick
    },
    {
      key: 'multi-screen',
      label: (
        <div className={styles.toolboxMenuItem}>
          <span className={styles.menuItemText}>多屏对比</span>
          <span className={styles.menuItemStatus}>-开发中</span>
        </div>
      ),
      onClick: handleMultiScreenClick
    },
    {
      key: 'temporal',
      label: (
        <div className={`${styles.toolboxMenuItem} ${isTemporalMode ? styles.active : ''}`}>
          <span className={styles.menuItemText}>多时相对比</span>
          {isTemporalMode && <span className={styles.menuItemStatus}>已启用</span>}
        </div>
      ),
      onClick: handleTemporalClick
    },
    // {
    //   key: 'query',
    //   label: (
    //     <div className={`${styles.toolboxMenuItem} ${styles.disabled}`}>
    //       <span className={styles.menuItemText}>查询</span>
    //       <span className={styles.menuItemStatus}>-开发中</span>
    //     </div>
    //   ),
    //   onClick: handleQueryClick
    // }
  ]

  return (
    <div className={`${styles.toolboxContainer} ${className}`}>
      <Dropdown
        menu={{ items: menuItems }}
        trigger={['click']}
        placement="topLeft"
        open={dropdownVisible}
        onOpenChange={setDropdownVisible}
        overlayClassName="toolboxDropdown"
      >
        <Button
          type="default"
          className={`${styles.toolboxButton} ${isCurtainMode || isTemporalMode ? styles.active : ''}`}
          icon={<ToolOutlined />}
        >
          工具箱
          <DownOutlined />
        </Button>
      </Dropdown>
    </div>
  )
}

export default ToolBox
