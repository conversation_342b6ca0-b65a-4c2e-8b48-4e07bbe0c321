import { getBodyH, getBodyW2, isEmpty } from '@/utils/utils';
import { useEffect, useState,useRef } from 'react';
import { Button} from 'antd';
import styles from './index.less';
import DJBaseMap from '@/pages/Maps/DJBaseMap';
import JCXX from './JXSSPanel';
import IfShowPanel from '@/components/IfShowPanel';
import HmsPanel from '@/pages/DJI/HmsPage/HmsPanel';
import GetPlayer from './PlayerPanel';
import CameraPanel2 from './Panels/CameraPanel2';
import PageButton from './pageButton';
import RtmpButton from './RtmpButton';
import FJInfoPanel from './FJInfoPanel';
import FlyButtonPanel from './../FlyToPage/FlyButtonPanel';
import LevelPanel from './Panels/LevelPanel';
import Map3D from '@/pages/Cesium';
import {FJStart3,JCStart} from '../DRCPage/Panels/RtmpChange';
import CameraPanel from './Panels/CameraPanel';
import { useModel } from 'umi';
import CameraCtrPanel from '@/pages/DJI/FlyToPage/CameraCtrPanel';

const DevicePage = ({ device }) => {
  const [dimensions, setDimensions] = useState({ width: 300, height: 240 }); // 初始宽度和高度
  const sideToolRef = useRef(null); // 获取 div 元素的引用
  const [maxHeight, setMaxHeight] = useState(window.innerHeight * 0.8); // 最大高度，初始为浏览器高度的 80%
  const [url, setUrl] = useState(1);
  const [p1, setP1] = useState(<DJBaseMap device={device} w1={'100%'}  h1={getBodyH(56)} />);
  const [p2, setP2] = useState(GetPlayer('100%', '100%', 3));
  const [ifX, setIfX] = useState(true);
  const [ifY, setIfY] = useState(true);
  const [ifY2, setIfY2] = useState(true);
  const [ifY3, setIfY3] = useState(true);
  const { PDataInit,pData}=useModel('pageModel');
  const {MqttConnect}=useModel('mqttModel');
  const [ifShuaXin,setIfShuaXin]=useState(false);

  if (isEmpty(device)) {
    device = JSON.parse(localStorage.getItem('device'))
  }

  const onClickYY = () => {
    console.log(ifY);
    setIfY(!ifY);
  };

  useEffect(() => {
    MqttConnect(device);
    setP1(<DJBaseMap device={device} sn={device.SN} w1={dimensions.width} h1={getBodyH(56)} />);
    setP2(GetPlayer(dimensions.width, dimensions.height, 3, device.SN));
    JCStart(device);
    PDataInit(device.SN);
    return () => {
    };
  }, []);

  const getPanel= (tt,ww1,hh1)=>{
    device = JSON.parse(localStorage.getItem('device'))
    if (tt === "机场镜头") {
      return GetPlayer(ww1, hh1, 3, device.SN)
    }
    if (tt === "飞机镜头") {
      startLive(tt);
      if(pData.current.ifAI){
        return GetPlayer(ww1,hh1, 12, device.SN2+"ai")
      }
      return GetPlayer(ww1, hh1, 12,device.SN2)
    }

    if (tt === "飞行地图") {
      if (hh1=='240px'){
        return <DJBaseMap device={device} sn={device.SN} w1={ww1} h1={hh1}  isDrc={true}/>
      }
     return <DJBaseMap showBZBtn={true} device={device} sn={device.SN} w1={ww1} h1={hh1} isDrc={true}/>
    }

    if (tt === "三维场景") {
       return <Map3D width={ww1} h1={hh1}/>
    }
  }

  useEffect(()=>{
      const div1=getPanel(pData.current.pNM1,'100%',getBodyH(59))
      setP1(div1);
      const div2=getPanel(pData.current.pNM2,dimensions.width, dimensions.height)
      setP2(div2);
  },[pData.current.pNM1,pData.current.pNM2,pData.current.ifAI,ifShuaXin,dimensions])

  const startLive=(tt)=>{

   // if(tt=="飞机镜头"){
      FJStart3(device,device.SN2);
    //}
    return;
  }

  const onClickZHM =async (e) => {
    const tt = e.target.innerText;
    startLive(tt);
    pData.current.pNM1=tt;
  }

  const onClickCHM =async (e) => {
    const tt = e.target.innerText;
    startLive(tt);
    pData.current.pNM2=tt;
  }


  const btnPanel = <div className={styles.sideToolHead}>
    <RtmpButton sn={device.SN} />
    {PageButton('主画面', onClickZHM)}
    {PageButton('画中画', onClickCHM)}
    <Button type="text" onClick={() => onClickYY()}>
      {ifY ? '隐藏' : '显示'}
    </Button>
  </div>

  const xxx = <div style={{ height: 24.0, background: 'red' }}></div>
  const jcNoDiv=<div onClick={()=>setIfY2(true)}  style={{zIndex:1010, background:'rgba(44, 131, 183)',userSelect:'none',cursor:'pointer',color:'white',padding:8.0,writingMode:'vertical-rl', borderRadius:'0px 5px 5px 0px', position:'absolute',top:8,left:0}}>机场信息</div>
  const fjNoDiv=<div onClick={()=>setIfY3(true)}  style={{zIndex:1010, background:'rgba(44, 131, 183)',userSelect:'none',cursor:'pointer',color:'white',padding:8.0,writingMode:'vertical-rl', borderRadius:'0px 5px 5px 0px', position:'absolute',top:280,left:0 ,height:80}}>飞行信息</div>

  const isDragging = useRef(false); // 用于判断是否正在拖动

  const startDrag = (e) => {
    const startX = e.clientX;
    const startY = e.clientY;
    const startWidth = sideToolRef.current.offsetWidth;
    const startHeight = sideToolRef.current.offsetHeight;

    isDragging.current = true; // 设置拖动状态为 true

    const onMouseMove = (moveEvent) => {
      if (!isDragging.current) return;

      const newWidth = startWidth - (moveEvent.clientX - startX); // 从左侧调整宽度
      const newHeight = startHeight + (moveEvent.clientY - startY); // 从下侧调整高度

      // 使用 requestAnimationFrame 优化性能
      requestAnimationFrame(() => {
        setDimensions({
          width: newWidth > 300 ? newWidth : 300, // 设置最小宽度为 100px
          height: newHeight > 240 ? Math.min(newHeight, maxHeight) : 240 // 设置最小高度为 100px，最大高度为 maxHeight
        });
      });
    };

    const onMouseUp = () => {
      isDragging.current = false; // 设置拖动状态为 false
      document.removeEventListener("mousemove", onMouseMove);
      document.removeEventListener("mouseup", onMouseUp);
    };

    document.addEventListener("mousemove", onMouseMove);
    document.addEventListener("mouseup", onMouseUp);
  };

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      // 更新最大高度
      const newMaxHeight = window.innerHeight * 0.8;
      setDimensions((prev) => ({
        ...prev,
        height: Math.min(prev.height, newMaxHeight) // 确保当前高度不超过新最大高度
      }));
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  return (
    <div className={styles.IndexPageStyle}>
      <div
        className="container"
      >
        {p1}
        <div ref={sideToolRef} className={styles.sideTool} style={{
          width: `${dimensions.width}`,
          height: `${dimensions.height}`,
          transition: 'width 0.2s, height 0.2s',
          cursor: 'nesw-resize',
          overflow:'hidden'
        }} onMouseDown={startDrag}>
          {btnPanel}
          {ifY ? p2 : null}
        </div>
        <LevelPanel />
        {IfShowPanel(260, 260, 8, 8, null, null, <JCXX setIfY={setIfY2} device={device} />, ifY2,'none', jcNoDiv)}
        {IfShowPanel(290, 260, 280, 8, null, null, <FJInfoPanel sn={device.SN} setIfY={setIfY3} />, ifY3,'none', fjNoDiv)}
        {IfShowPanel(100, 800, 20, 360, 8, null, HmsPanel(), true)}
        {/* {IfShowPanel(260, 300, 290, null, 8, null, JoyStickPanel(), ifY)} */}
        {/* {IfShowPanel(28, getBodyW2(200), getBodyH(86), 80, null, 0, FJCtrPanel(device.SN), ifY)} */}
        {IfShowPanel(48, getBodyW2(200), getBodyH(136), 100, null, 0, <FlyButtonPanel device={device}></FlyButtonPanel> , true)}
        <CameraCtrPanel sn={device.SN} fj={null} />
        <CameraPanel/>
        <CameraPanel2 onFrush={()=>setIfShuaXin(!ifShuaXin)}></CameraPanel2>
        {/* {IfShowPanel(160,360,getBodyH(300),600,null,null,<div style={{background:getBaseColor()}}>{cmdPanel}</div>,ifCMDPanel)}
         */}
        {/* {IfShowPanel(28, 200, getBodyH(100), null, 8, 0, <div style={{ height: 36, width: 200 }}><img style={{ height: 36 }} height={36} width={150} src={BDZL}></img></div>, true)} */}
      </div>
      <div className={styles.shadow}></div>
    </div>
  );
};
//


export default DevicePage;
