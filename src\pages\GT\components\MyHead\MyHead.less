#MyHead {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 56px;
  padding: 21px 20px 20px 20px;
  cursor: pointer;
  background: linear-gradient(to right, #06393f 200px, #1e2531 50%);
  position: relative;
}

.headIconBox {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20px;
}

.headIcon {
  width: 2vw;
  height: 2vw;
  background: url("@/assets/images/kqgl/earth-rotate.gif");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  border-radius: 50%;
  box-shadow: 0 0 35px rgb(141, 140, 140);

}

.headIcon2 {
  width: 4vw;
  height: 1vw;
  background: url("@/assets/images/BDZL.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  // box-shadow: 0 0 15px rgb(199, 104, 8);
}

.headTitle {
  font-size: 22px;
  font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
  font-weight: bolder;
  color: #e3f5fb;
  white-space: nowrap;
  flex: 1;
  text-align: left;
  // max-width: 200px;
}

.headBtn {
  color: #e3f5fb;
  background: none;
  border: none;
  cursor: pointer;
  background-image: url("@/assets/icons/home.png");
  background-size: contain;
  width: 20px;
  height: 20px;
}

.headContent {
  // 绝对定位 保证自己居中
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  color: #e3f5fb;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  z-index: 1;
  margin: 0;
  flex: none;
}

.headContentItem {
  margin: 0 30px;
  line-height: 40px;
  padding: 0 20px;
  white-space: nowrap;
}

.active {
  color: #e3f5fb;
  background: url("@/pages/GT/assets/image/智慧耕保光标_u20.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.headSetting {
  display: flex;
  justify-content: space-between;
  align-items: center;
  white-space: nowrap;
  font-size: 13px;
  color: #fff;
}

.headSettingItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.headSettingItem:nth-child(2) {
  margin: 0 20px;
}

.headSettingItem_text {
  margin-left: 10px;
}

.menuTitle {
  position: relative;
  padding-right: 20px;
}

.arrowIcon {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
}

.arrow {
  margin-left: 8px;
  font-size: 12px;
  transition: transform 0.3s;
}

.menuItemWrapper:hover .arrow {
  transform: rotate(180deg);
}

.menuItemWrapper {
  position: relative;
  height: 100%;
}

.subMenu {
  position: absolute;
  top: calc(100% - 5px);
  /* 向上偏移5px产生重叠 防止鼠标刚离开就消失 */
  left: 50%;
  transform: translateX(-50%);
  background: #1e2531;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  min-width: 160px;
  z-index: 100;
  border-radius: 4px;
  padding-top: 10px;
}

.subMenuItem {
  color: #e3f5fb;
  padding: 12px 16px;
  cursor: pointer;
  transition: background 0.3s;
  white-space: nowrap;
}

.subMenuItem:hover {
  background: rgba(255, 255, 255, 0.1);
}

.subMenuItem:not(:last-child) {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}