import { GTRouter } from "@/utils/Routes/GTRouter";
import { GFRouter } from "@/utils/Routes/GFRouter";
import { SIRouter } from "@/utils/Routes/SIRouter";
import WayLineListPage from "../pages/DJI/WayLine/index";
import WayLine3DPage from "../pages/DJI/wayline3D/index";
import CesiumTest from "../pages/DJI/cesiumTest/index";
import TrajectoryDisplay from "../pages/DJI/TrajectoryDisplay/index";
import BeltRoute from "../pages/DJI/BeltRoute/index";
import PlanarRoute from "../pages/DJI/PlanarRoute/index";
import InspectionRoute from "../pages/DJI/InspectionRoute/index";
import OrgPage from "../pages/DJI/OrgPage";
import JiaoTongHome from "../pages/DJI/OrgPage/JiaoTongHome";
import DevicePage from "../pages/DJI/DevicePage/index";
import MediaDataPage from "../pages/DJI/MediaDataPage/index";
import MediaDataPage2 from "../pages/DJI/MediaDataPage/index2";
import TaskListPage from "../pages/DJI/TaskListPage/index2";
import CronListPage from "../pages/DJI/CronPage";
import FireIncidentTask from "@/pages/DJI/FireIncidentTask"
import UserListPage from "../pages/DJI/EmployeeMentPage";
import DangerListPage from "@/pages/DJI/DangerPage";
import DManagePage from "@/pages/DJI/DManagePage";
import { getGuid } from "./helper";
import Map3D from "@/pages/Cesium/index";
import ModelListPage from "@/pages/DJI/AIPage/ModelListPage";
import ModelListPage2 from "@/pages/DJI/AIPage/ModelListPage2";
import ModelListPage3 from "@/pages/DJI/AIPage/ModelListPage3";
import AIVideoJobPage from "@/pages/DJI/AIPage/AIVideoJobPage";
import AIMediaJobPage from "@/pages/DJI/AIPage/AIMediaJobPage";
import TaskPointPage from "@/pages/DJI/TaskPointMediaPage";
import JuanLianMap from "@/pages/Maps/MapPanel/JuanLian";
import SanlianJuanLian from "@/pages/Maps/MapPanel/SanlianJuanLian";
import ZhengSheMap from "@/pages/Maps/MapPanel/ZhengSheYingXiang";
import Model3DMap from "@/pages/Maps/MapPanel/Model3DMap";
import VideoDataListPage from "@/pages/DJI/VideoDataPage/index";
import VideoMergePage from "@/pages/DJI/VideoMergePage";
import MapJobPage from "@/pages/DJI/MapJobPage";
import SystemLogPage from "@/pages/DJI/SystemLogPage";
import FlightFilePage from "@/pages/DJI/FlightFilePage2";
import PictrueComparePage from "@/pages/DJI/PictureComparePage";
import MapOperatePage from "@/pages/Maps/MapPanel/MapOperatePage/index";
import ReportGenerationTasks from "@/pages/DJI/AIPage/ReportGenerationTasks/index";
import RtmpPage from "@/pages/DJI/RtmpPage";
import InformPage from "@/pages/DJI/NoticePage/InformPage";
import TaskCountPage from "@/pages/DJI/TaskCountPage";
import UnLockPage from "@/pages/DJI/UnLockPage";
import DeviceLogPage from "@/pages/DJI/DManagePage/DeviceLogPage";
import NoticeAddPage from "@/pages/DJI/NoticePage/form_add";
import OrgManagePage from "@/pages/DJI/OrgManagePage";
import NfzPage from "@/pages/DJI/NfzPage";
import ModelingTasks from "@/pages/DJI/ModelingTasks/index";


const DJRoutes = [
  ...GTRouter,
  ...GFRouter,
  ...SIRouter,
  {
    title: "态势感知",
    key: "/dj/tsgz",
    children: <JiaoTongHome key={getGuid()} />,
  },
  {
    title: "航拍照片",
    key: "/dj/scgl/zzch/MediaDataPage",
    children: <MediaDataPage key={getGuid()} />,
  },
  {
    title: "任务归档",
    key: "/dj/sjgl",
    children: <FlightFilePage key={getGuid()} />,
  },
  {
    title: "航拍视频",
    key: "/dj/scgl/zzch/MediaDataPage2",
    children: <MediaDataPage2 key={getGuid()} />,
  },
  {
    title: "图片对比",
    key: "/dj/sjgl",
    children: <PictrueComparePage key={getGuid()} />,
  },
  {
    title: "航线管理",
    key: "/dj/wayline/list",
    children: <WayLineListPage key={getGuid()} />,
  },
  {
    title: "航点航线",
    key: "/dj/wayline3D/point",
    children: <WayLine3DPage key={getGuid()} />,
  },
  {
    title: "地图测试",
    key: "/dj/CesiumTest",
    children: <CesiumTest key={getGuid()} />,
  },
  {
    title: "轨迹回显",
    key: "/dj/TrajectoryDisplay",
    children: <TrajectoryDisplay key={getGuid()} />,
  },
  {
    title: "带状航线",
    key: "/dj/BeltRoute",
    children: <BeltRoute key={getGuid()} />,
  },
  {
    title: "面状航线",
    key: "/dj/PlanarRoute",
    children: <PlanarRoute key={getGuid()} />,
  },
  {
    title: "垂直航线",
    key: "/dj/InspectionRoute",
    children: <InspectionRoute key={getGuid()} />,
  },
  {
    title: "航行记录",
    key: "/dj/task/list",
    children: <TaskListPage key={getGuid()} />,
  },
  {
    title: "航点照片",
    key: "/dj/task/point",
    children: <TaskPointPage key={getGuid()} />,
  },

  {
    title: "定时任务",
    key: "/dj/cron/list",
    children: <CronListPage key={getGuid()} />,
  },
  {
    title: "三方任务",
    key: "/dj/cron/fireIncidentTask",
    children: <FireIncidentTask key={getGuid()} />,
  },
  {
    title: "正射对比",
    key: "/dj/map/juanlian",
    children: <JuanLianMap key={getGuid()} />,
  },
  {
    title: "正射影像",
    key: "/dj/map/zhengshe",
    children: <ZhengSheMap key={getGuid()} />,
  },
  {
    title: "三维模型",
    key: "/dj/map/zhengshe",
    children: <Model3DMap key={getGuid()} />,
  },
  {
    title: "三维对比",
    key: "/dj/map/zhengshe",
    children: <SanlianJuanLian key={getGuid()} />,
  },
  {
    title: "用户管理",
    key: "/dj/user/list",
    children: <UserListPage key={getGuid()} />,
  },

  {
    title: "事件处理",
    key: "/dj/cron/list",
    children: <DangerListPage key={getGuid()} />,
  },

  {
    title: "视频九宫格",
    key: "/dj/cron/list",
    children: <RtmpPage key={getGuid()} />,
  },

  {
    title: "设备列表",
    key: "/dj/cron/list",
    children: <DManagePage key={getGuid()} />,
  },

  {
    title: "设备管理",
    key: "/dj/cron/list",
    children: <DManagePage key={getGuid()} />,
  },

  {
    title: "AI模型",
    key: "/dj/cron/list",
    children: <ModelListPage key={getGuid()} />,
  },

  {
    title: "AI模型2",
    key: "/dj/cron/list",
    children: <ModelListPage2 key={getGuid()} />,
  },

  {
    title: "AI模型3",
    key: "/dj/cron/list",
    children: <ModelListPage3 key={getGuid()} />,
  },

  {
    title: "视频识别任务",
    key: "/dj/cron/list",
    children: <AIVideoJobPage key={getGuid()} />,
  },

  {
    title: "图像识别任务",
    key: "/dj/cron/list",
    children: <AIMediaJobPage key={getGuid()} />,
  },
  {
    title: "报告生成任务",
    key: "/dj/cron/list",
    children: <ReportGenerationTasks key={getGuid()} />,
  },
  {
    title: "消息列表",
    key: "/dj/cron/list",
    children: <InformPage key={getGuid()} />,
  },
  {
    title: "视频整理",
    key: "/dj/cron/list",
    children: <VideoDataListPage key={getGuid()} />,
  },

  {
    title: "视频拼接",
    key: "/dj/cron/list",
    children: <VideoMergePage key={getGuid()} />,
  },
  {
    title: "系统日志",
    key: "/dj/cron/list",
    children: <SystemLogPage key={getGuid()} />,
  },
  {
    title: "地图建模",
    key: "/dj/mapjob/list",
    children: <MapJobPage key={getGuid()} />,
  },
  {
    title: "地图管理",
    key: "/dj/mapjob/list",
    children: <MapOperatePage key={getGuid()} />,
  },
  {
    title: "公告发布",
    key: "/dj/user/list",
    children: <NoticeAddPage key={getGuid()} />,
  },
  {
    title: "解禁文件",
    key: "/dj/mapjob/list",
    children: <UnLockPage key={getGuid()} />,
  },
  {
    title: "飞行统计",
    key: "/dj/task/list",
    children: <TaskCountPage key={getGuid()} />,
  },
  {
    title: "设备日志",
    key: "/dj/sjgl",
    children: <DeviceLogPage key={getGuid()} />,
  },
  {
    title: "任务记录",
    key: "/dj/sjgl",
    children: <FlightFilePage key={getGuid()} />,
  },
  {
    title: "组织管理",
    key: "/dj/sjgl",
    children: <OrgManagePage key={getGuid()} />,
  },
  {
    title: "电子围栏",
    key: "/dj/sjgl",
    children: <NfzPage key={getGuid()} />,
  },
  {
    title: "建模任务",
    key: "/dj/jmrw",
    children: <ModelingTasks key={getGuid()} />,
  },
];

export default DJRoutes;
