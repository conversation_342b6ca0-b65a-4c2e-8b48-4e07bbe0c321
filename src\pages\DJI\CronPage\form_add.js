import {
  Input,
  InputNumber,
  Form,
  DatePicker,
  Select,
  Button,
  message,
} from "antd";
import dayjs from "dayjs";
import { useState } from "react";
import { isEmpty } from "@/utils/utils";
import locale from "antd/es/date-picker/locale/zh_CN";
import "dayjs/locale/zh-cn";
import { HPost2 } from "@/utils/request";

const CronAddForm = (props) => {
  const { sn, wayList, refrush, cronList } = props;
  const [dt, setDt] = useState(3);
  const [tm, setTM] = useState({});
  const [way, setWay] = useState({});
  const [name, setName] = useState("");

  const getWaySelect = (wayList) => {
    const list = [];
    wayList.forEach((e) => {
      list.push(
        <Select.Option key={e.WanLineId} data={e} value={e.<PERSON>LineId}>
          {e.<PERSON><PERSON><PERSON>Name}
        </Select.Option>
      );
    });
    return list;
  };

  const onSave = async (e) => {
    if (!name) {
      return message.warning("请填写任务名称");
    }
    for (let value of cronList) {
      if (value.CName === name) {
        return message.warning("任务名称重复,请换个名称!");
      }
    }
    if (isEmpty(way)) {
      message.warning("请选择航线！");
      return;
    }

    if (isEmpty(tm)) {
      message.warning("请选择执行时间！");
      return;
    }

    const user = JSON.parse(localStorage.getItem("user"));
    console.log("CronAddForm", user);
    const data = {
      CName: name,
      SN: way.SN,
      FLightId: way.WanLineId,
      FlightName: way.WayLineName,
      JobTM: tm,
      DT: dt * 60 * 1000 * 60,
      UserName: user.Name,
    };
    console.log("CronAddForm", data, way);

    const xx = await HPost2("/api/v1/CronJob/Add", data);
    if (isEmpty(xx.err)) {
      message.info("创建成功！");
    }

    refrush();
  };

  const onChange = (values) => {
    const xx = wayList.find((item) => {
      return item.WanLineId === values;
    });
    console.log("onChange values of form: ", xx);
    setWay(xx);
  };

  const onTime = (values) => {
    let xx = dayjs(values);
    const now = dayjs();
    setTM(xx.valueOf());
  };

  const onDT = (values) => {
    setDt(values);
  };

  const onName = (e) => {
    setName(e.target.value);
  };

  return (
    <Form
      labelCol={{
        span: 4,
      }}
      wrapperCol={{
        span: 14,
      }}
      layout="horizontal"
      style={{
        maxWidth: 600,
      }}
    >
      <Form.Item label="任务名称">
        <Input onChange={onName}></Input>
      </Form.Item>

      <Form.Item label="选择航线">
        <Select onSelect={onChange}>{getWaySelect(wayList)}</Select>
      </Form.Item>

      <Form.Item label="执行时间">
        <DatePicker locale={locale} onChange={onTime} picker={"time"} />
      </Form.Item>

      <Form.Item label="重复频次">
        <InputNumber onChange={onDT} placeholder={dt} /> 小时
      </Form.Item>

      <Form.Item label={" "} colon={false}>
        <Button type="primary" onClick={onSave}>
          保存
        </Button>
      </Form.Item>
    </Form>
  );
};

export default CronAddForm;
