/**
 * 目前未使用，直接在原文件更改的，后续可直接删除
 */

import { getBodyH, getBodyW, getBodyW2 } from '@/utils/utils';
import imgPG from '@/assets/images/bg.png';
import styles from './index.module.less';
import { useState, useEffect, useRef } from 'react';
import IfShowPanel from '@/components/IfShowPanel';
import DangerCountPie from './Panels/DangerCountPie2.js';
import DJBaseMap from './Panels/OrgMap2';
import LeafletMap from '@/pages/GT/components/Map/2DMap';
import CesiumMap from '@/pages/GT/components/Map/3DMap';
import AMap from './Panels/AMapContainer';
import useDeviceStore from '@/stores/deviceStore';
import DataPanel from './Panels/DataPanel';
import JianKongPanel from './Panels/JianKongPanel';
import ZhiBanPanel from './Panels/ZhiBanPanel';
import FlightPanel from './Panels/FlightTaskPanel';
import DockStatePanel from './Panels/DockPanel';
import MainDataPanel from './Panels/MainDataPanel';
import RealTimeTasks from './Panels/RealTimeTasks';
import { getGuid } from '@/utils/helper';
import NoticePanel from './Panels/NoticePanel';
import { useModel } from 'umi';

const OrgPage = ({ className }) => {
  const { fetchBaseMapData } = useModel('mapModel');
  const { deviceList, startAutoRefresh, stopAutoRefresh } = useDeviceStore();
  const mapRef = useRef(null);

  useEffect(() => {
    fetchBaseMapData();
  }, [fetchBaseMapData]);

  const getPanel = (
    <div style={{ height: '100%' }}>
      <DockStatePanel key={getGuid()} data={deviceList} />
      {/* <FlightPanel /> */}
    </div>
  );

  const getPanel2 = (
    <div style={{ height: '100%' }}>
      {/* <RealTimeTasks  key={getGuid()} deviceList={deviceList}></RealTimeTasks> */}
      <NoticePanel></NoticePanel>
      {/* <JianKongPanel />

        <DangerCountPie /> */}
    </div>
  );

  useEffect(() => {
    // 启动设备数据自动刷新，每60秒更新一次
    startAutoRefresh(60000);

    // 清理函数
    return () => {
      stopAutoRefresh();
    };
  }, [startAutoRefresh, stopAutoRefresh]);

  return (
    <div style={{ height: getBodyH(56) }}>
      <div
        className={`${styles.CradGroup} ${className}`}
        style={{
          position: 'relative',
          width: '100%',
          height: '100%',
        }}
      >
        {IfShowPanel(getBodyH(150), 400, 0, 8, null, null, getPanel, true)}
        {IfShowPanel(getBodyH(150), 320, 8, null, 8, null, getPanel2, true)}

        {/*  {IfShowPanel(98, 200,null, `47.2%`, null,30,<img height={98} width={100} src={BDZL}></img>, true)}*/}
        {/* {IfShowPanel(120, 800, 40, getBodyW(660), null, null, <MainDataPanel></MainDataPanel>, true)} */}
        {/* <AMap data={deviceList} h1={getBodyH(56)}/> */}
        {/* <OrgMap h1={getBodyH(56)}/> */}

        <DJBaseMap data={deviceList} h1={getBodyH(56)}></DJBaseMap>
      </div>
      <div className={styles.shadow}></div>
    </div>
  );
};

export default OrgPage;
