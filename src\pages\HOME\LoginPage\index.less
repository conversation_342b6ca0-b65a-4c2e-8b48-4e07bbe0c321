.login-page {
    height: 100vh;
    width: 100%;
    position: relative;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background-image: url("@/assets/images/login-bg.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;

    .login-page-body {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 129px;
        
        .login-page-head {
            letter-spacing: 4px;
            margin-bottom: 14px;
            font-size: 30px;
            font-weight: 300;
            /* 设置文字颜色为透明，确保背景色显示 */
            color: transparent; 
            /* 设置背景为渐变色 */
            background: linear-gradient(to bottom, #DFEBFF, #0DD3CF); 
            /* 将背景裁剪为文字形状 */
            -webkit-background-clip: text; 
            /* 兼容性处理，确保在非webkit浏览器上也能显示 */
            background-clip: text; 
        }

        .login-page-body-form {
            padding: 67px 51px 95px;
            background-color: transparent;
            position: relative;
            // background: linear-gradient(180deg, #12394D, #0B6571);
            background-image: url("@/assets/images/login-form-bg.png");
            background-size: 100% 100%;
            width: 542px;
            

            .login-page-body-form-title {
               display: flex;
               justify-content: space-between;
               font-size: 24px;
               .titleZc{
                color: white;
               }
               .titleEn{
                color: #4d6e6e;
               }
            }
            .login-bt{
                background-image: url("@/assets/images/login-bt-bg.png");
                background-color: transparent;
                background-size: 100% 100%;
                width: 423px;
            }




            input:-webkit-autofill,
            input:-webkit-autofill:hover,
            input:-webkit-autofill:focus,
            input:-webkit-autofill:active {
            // -webkit-box-shadow: 0 0 0 30px #0A2425 inset !important;
            // box-shadow: 0 0 0 30px #0A2425 inset !important;
            -webkit-text-fill-color: white !important; /* 文本颜色 */
            transition: background-color 9999s ease-in-out 0s;
            -webkit-transition-delay: 99999s;
            transition-delay: 99999s;
            // font-size: 14px;
                // font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            }


            .inputStyle{
                 width: 22vw;
                 background-color: #0A2425;
                 border: 1px solid #0E4E5F;
                 color: white;
                 font-size: 13px;
            }

          
        }
    }

    .login-page-foot {
        height: 35px;
        background: #92c7fb;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 0.6vw;
    }
}