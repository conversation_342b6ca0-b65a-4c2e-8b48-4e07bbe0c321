import { isEmpty } from "@/utils/utils";

const IfShowPanel=(height,width,top,left,right,bottom, child,ifShow,ifSelect,noDiv, zIndex)=>{
    if(isEmpty(ifSelect)){
        ifSelect='none';
    }
    if (ifShow) {
        return (
          <div 
            style={{
              position: 'absolute',
              width,left,top,height,right,bottom,
              transform: left === "50%"? "translateX(-50%)" : "",
              zIndex: zIndex || 1000,
              display: 'block',
              userSelect: ifSelect,
            }}
          >
            {child}
          </div>
        );
      }
      if(!isEmpty(noDiv)) return noDiv;
      return (
        <div>
        </div>
      );
}

export default IfShowPanel;