import React, { useEffect, useRef, useState } from "react";

import { useModel } from "umi";

const KeyboardListener = ({ joyHandle, CameraDrag }) => {
  const [pressedKeys, setPressedKeys] = useState(new Set());
  const device = JSON.parse(localStorage.getItem("device"));

  const { DoCMD2 } = useModel("cmdModel");

  const onKeyDown = (event) => {
    pressedKeys.add(event.key);
    setPressedKeys(new Set(pressedKeys));
    sendCMD();

    //
  };

  const onKeyUp = (event) => {
    pressedKeys.delete(event.key);
    setPressedKeys(new Set(pressedKeys));
    sendCMD();
  };
  const pb1 = (s1) => {
    const p1 = pressedKeys.has(s1);

    return p1;
  };

  const sendCMD = async () => {
    console.log("sendCMD", pressedKeys);
    let w, w2, w3, w4;
    w = 0;
    w2 = 0;
    w3 = 0;
    w4 = 0;
    if (pb1("w") && !pb1("s")) w = 15;
    if (pb1("s") && !pb1("w")) w = -15;
    if (pb1("a") && !pb1("d")) w2 = -15;
    if (pb1("d") && !pb1("a")) w2 = 15;
    if (pb1("q") && !pb1("e")) w3 = -10;
    if (pb1("e") && !pb1("q")) w3 = 10;
    if (pb1("z") && !pb1("c")) w4 = 5;
    if (pb1("c") && !pb1("z")) w4 = -5;
    joyHandle(w, w2, w3, w4);

    if (pb1("ArrowUp")) CameraDrag(6, 0);
    if (pb1("ArrowDown")) CameraDrag(-6, 0);
    if (pb1("ArrowLeft")) CameraDrag(0, -6);
    if (pb1("ArrowRight")) CameraDrag(0, 6);

    if (pb1(" ")) {
      //按下空格
      const gateway_sn = device.SN;
      const topic = `thing/product/${gateway_sn}/drc/down`;
      const data = {
        data: {},
        method: "drone_emergency_stop",
      };
      DoCMD2(topic, data);
    }
  };

  useEffect(() => {
    document.addEventListener("keydown", onKeyDown);
    document.addEventListener("keyup", onKeyUp);

    return () => {
      document.removeEventListener("keydown", onKeyDown);
      document.removeEventListener("keyup", onKeyUp);
    };
  }, []);

  return <div />;
};

export default KeyboardListener;
