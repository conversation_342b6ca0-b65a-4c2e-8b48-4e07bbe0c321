import camera2 from "@/assets/drcImgs/camera2.png";
import camera3 from "@/assets/drcImgs/camera3.png";
import camera4 from "@/assets/drcImgs/camera4.png";
import camera4svg from "@/assets/drcImgs/camera4.svg";
import camera3svg from "@/assets/drcImgs/camera3.svg";
import video2 from "@/assets/drcImgs/video2.png";
import video3 from "@/assets/drcImgs/video3.png";
import video4 from "@/assets/drcImgs/video4.png";
import { getBodyH, isEmpty } from "@/utils/utils";
import { useEffect, useState } from "react";
import { useModel } from "umi";
import { getMinstr } from "../../DRCPage/Panels/helper";
import HanHuaPage from "@/pages/DJI/DevicePage/Panels/HanHuaQi";
import styles from "./CameraPanel.less";
import { Tooltip } from "antd";
import { checkIfFlyer } from "@/utils/utils";

const CameraPanel = () => {
  const w1 = 60;
  const h1 = 240;
  const { fj, fjData } = useModel("droneModel");
  const { DoCMD } = useModel("cmdModel");
  const device = JSON.parse(localStorage.getItem("device"));
  const [ifVideo, setIfVideo] = useState(false);
  const { setModal, setOpen } = useModel("pageModel");

  const changeCamera = (v) => {
    const data = {
      camera_mode: v,
      payload_index: device.Camera2,
    };
    DoCMD(device.SN, "camera_mode_switch", data);

    // if (v == 1) {
    //   VideoSave();
    // } else {
    //   CameraSave();
    // }
  };

  const photoCamera = (v) => {
    const data = { payload_index: device.Camera2 };
    if (fj.data.cameras[0].camera_mode == 0) {
      DoCMD(device.SN, "camera_photo_take", data);
    } else {
      changeCamera(0);
      setTimeout(() => {
        DoCMD(device.SN, "camera_photo_take", data);
      }, 1000);
    }
  };

  const VideoStart = () => {
    const data = { payload_index: device.Camera2 };
    DoCMD(device.SN, "camera_recording_start", data);
  };

  const VideoStop = () => {
    const data = { payload_index: device.Camera2 };
    DoCMD(device.SN, "camera_recording_stop", data);
  };

  const VideoClick = () => {
    if (fj.data.cameras[0].recording_state == 1) {
      VideoStop();
      setIfVideo(false);
    } else {
      changeCamera(1);
      setTimeout(() => {
        VideoStart();
        setIfVideo(true);
      }, 1000);
    }
  };

  const CameraSave = (z) => {
    const data = {
      payload_index: device.Camera2,
      photo_storage_settings: [z],
    };
    DoCMD(device.SN, "photo_storage_set", data);
  };

  const VideoSave = () => {
    const data = {
      payload_index: device.Camera2,
      video_storage_settings: ["current"],
    };
    DoCMD(device.SN, "video_storage_set", data);
  };

  const getVideoImg = () => {
    if (ifVideo) return video3;
    return video4;
  };

  const onHanHuaQi = () => {
    setModal(<HanHuaPage device={device} setOpen={setOpen} />);
    setOpen(true);
  };

  useEffect(() => {
    //
    if (isEmpty(fjData.current)) return;
    if (isEmpty(fjData.current.cameras)) return;
    if (fjData.current.cameras[0].recording_state == 1) {
      setIfVideo(false);
    } else {
      setIfVideo(true);
    }
  }, [fjData.current]);

  //fj.data.cameras[0].camera_mode=0
  if (isEmpty(fj)) return;
  if (isEmpty(fj.data)) return;
  if (isEmpty(fj.data.cameras)) return;
  if (isEmpty(fj.data.cameras[0])) return;

  return (
    <>
      <div className={styles.camerasTool} style={{ top: null, bottom: 160 }}>
        <div className={styles.items}>
          <Tooltip placement="left" title="喊话器">
            <img
              draggable={false}
              src={camera4svg}
              onClick={() => {
                if (checkIfFlyer()) {
                  onHanHuaQi();
                }
              }}
            ></img>
          </Tooltip>
        </div>
        <div className={styles.items}>
          <Tooltip placement="left" title="拍照">
            <img
              draggable={false}
              src={camera3svg}
              onClick={() => {
                if (checkIfFlyer()) {
                  photoCamera();
                }
              }}
            ></img>
          </Tooltip>
        </div>
      </div>
      <div className={styles.camerasTool} style={{ top: null, bottom: 10 }}>
        <div className={styles.items}>
          <Tooltip
            placement="left"
            title={
              fj.data.cameras[0].recording_state == 0 ? "开始录像" : "结束录像"
            }
          >
            <img
              draggable={false}
              src={getVideoImg()}
              onClick={() => {
                if (checkIfFlyer()) {
                  VideoClick();
                }
              }}
            ></img>
          </Tooltip>
        </div>
        <div className={styles.camerasNums}>
          {getMinstr(fj.data.cameras[0].record_time)}
        </div>
      </div>
    </>
  );
};

export default CameraPanel;
