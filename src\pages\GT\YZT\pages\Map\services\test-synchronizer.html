<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MapViewSynchronizer 功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #results {
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 MapViewSynchronizer 增强功能测试</h1>
        <p>这个页面用于测试 MapViewSynchronizer 的事件转发功能增强。</p>
        <button onclick="runTests()">开始测试</button>
        <button onclick="clearResults()">清空结果</button>
    </div>

    <div class="test-container">
        <h2>测试结果</h2>
        <div id="results"></div>
    </div>

    <script type="module">
        // 模拟 MapViewSynchronizer 类（简化版本用于测试）
        class MapViewSynchronizer {
            constructor() {
                this.observers = new Map();
                this.enabled = false;
                this.isUpdating = false;
                this.lastViewState = null;
                this.debugMode = false;
                this.stats = {
                    totalSyncs: 0,
                    successfulSyncs: 0,
                    failedSyncs: 0,
                    lastSyncTime: null
                };
            }

            setEnabled(enabled) {
                this.enabled = Boolean(enabled);
                this.log(`地图视图同步已${this.enabled ? '启用' : '禁用'}`);
                return this.enabled;
            }

            setDebugMode(enabled) {
                this.debugMode = Boolean(enabled);
                this.log(`调试模式已${this.debugMode ? '启用' : '禁用'}`);
                return this.debugMode;
            }

            subscribe(id, mapInstance, callbacks) {
                if (!id || !mapInstance || !callbacks) {
                    console.error('地图视图同步器：无效的订阅参数');
                    return false;
                }

                if (!callbacks.onViewChange && !callbacks.onMapEvent) {
                    console.error('地图视图同步器：必须提供至少一个回调函数');
                    return false;
                }

                this.observers.set(id, { mapInstance, callbacks });
                this.log(`地图 ${id} 已订阅视图同步服务`);
                return true;
            }

            notifyMapEvent(sourceId, eventData) {
                this.stats.totalSyncs++;
                this.stats.lastSyncTime = new Date();
                
                if (!this.enabled) {
                    this.log(`事件同步已禁用，跳过通知 (源: ${sourceId}, 事件: ${eventData?.type})`);
                    return false;
                }

                if (this.isUpdating) {
                    this.log(`已在更新中，跳过事件通知 (源: ${sourceId}, 事件: ${eventData?.type})`);
                    return false;
                }
                
                if (!this.isValidEventData(eventData)) {
                    console.error('地图视图同步器：无效的事件数据', eventData);
                    this.stats.failedSyncs++;
                    return false;
                }

                this.isUpdating = true;

                try {
                    let notifiedCount = 0;
                    this.observers.forEach((observer, id) => {
                        if (id !== sourceId && observer.callbacks.onMapEvent) {
                            try {
                                observer.callbacks.onMapEvent(eventData);
                                notifiedCount++;
                            } catch (error) {
                                console.error(`通知观察者 ${id} 事件时出错`, error);
                                if (observer.callbacks.onError) {
                                    observer.callbacks.onError(error, eventData);
                                }
                            }
                        }
                    });
                    
                    this.log(`已转发事件 ${eventData.type} 到 ${notifiedCount} 个地图 (源: ${sourceId})`);
                    
                    if (notifiedCount > 0) {
                        this.stats.successfulSyncs++;
                        return true;
                    } else {
                        this.stats.failedSyncs++;
                        return false;
                    }
                } catch (error) {
                    console.error('事件通知过程中出错', error);
                    this.stats.failedSyncs++;
                    return false;
                } finally {
                    setTimeout(() => {
                        this.isUpdating = false;
                    }, 50);
                }
            }

            isValidEventData(eventData) {
                if (!eventData || typeof eventData !== 'object') {
                    return false;
                }

                const validTypes = ['pan', 'zoom', 'mousemove', 'mousedown', 'mouseup', 'mouseleave'];
                if (!eventData.type || !validTypes.includes(eventData.type)) {
                    return false;
                }

                if (!eventData.payload || typeof eventData.payload !== 'object') {
                    return false;
                }

                switch (eventData.type) {
                    case 'pan':
                        return typeof eventData.payload.dx === 'number' && 
                               typeof eventData.payload.dy === 'number';
                    
                    case 'zoom':
                        return typeof eventData.payload.delta === 'number';
                    
                    case 'mousemove':
                    case 'mousedown':
                    case 'mouseup':
                        return eventData.payload.screenPoint && 
                               typeof eventData.payload.screenPoint.x === 'number' &&
                               typeof eventData.payload.screenPoint.y === 'number';
                    
                    case 'mouseleave':
                        return true;
                    
                    default:
                        return false;
                }
            }

            getObserverCount() {
                return this.observers.size;
            }

            hasObserver(id) {
                return this.observers.has(id);
            }

            getStats() {
                return { ...this.stats };
            }

            log(message) {
                if (this.debugMode) {
                    console.log(`[MapViewSync] ${message}`);
                }
            }

            reset() {
                this.observers.clear();
                this.enabled = false;
                this.isUpdating = false;
                this.lastViewState = null;
                this.stats = {
                    totalSyncs: 0,
                    successfulSyncs: 0,
                    failedSyncs: 0,
                    lastSyncTime: null
                };
                this.log('地图视图同步服务已重置');
                return true;
            }
        }

        // 全局测试函数
        window.runTests = function() {
            const resultsDiv = document.getElementById('results');

            window.addResult = function(message, type = 'info') {
                const div = document.createElement('div');
                div.className = `test-result ${type}`;
                div.textContent = message;
                resultsDiv.appendChild(div);
                resultsDiv.scrollTop = resultsDiv.scrollHeight;
            }

            window.addResult('🧪 开始测试 MapViewSynchronizer 增强功能...', 'info');

            // 创建同步器实例
            window.synchronizer = new MapViewSynchronizer();
            window.synchronizer.setEnabled(true);
            window.synchronizer.setDebugMode(true);

            // 模拟地图实例和回调
            const mockMapInstance1 = { id: 'map1' };
            const mockMapInstance2 = { id: 'map2' };

            window.receivedEvents = [];
            const mockCallbacks1 = {
                onMapEvent: (eventData) => {
                    window.receivedEvents.push({ target: 'map1', event: eventData });
                    window.addResult(`📍 Map1 收到事件: ${eventData.type}`, 'success');
                },
                onError: (error, eventData) => {
                    window.addResult(`❌ Map1 错误: ${error.message}`, 'error');
                }
            };

            const mockCallbacks2 = {
                onMapEvent: (eventData) => {
                    window.receivedEvents.push({ target: 'map2', event: eventData });
                    window.addResult(`📍 Map2 收到事件: ${eventData.type}`, 'success');
                },
                onError: (error, eventData) => {
                    window.addResult(`❌ Map2 错误: ${error.message}`, 'error');
                }
            };

            // 测试1: 订阅功能
            window.addResult('1️⃣ 测试订阅功能...', 'info');
            window.subscribe1 = window.synchronizer.subscribe('map1', mockMapInstance1, mockCallbacks1);
            window.subscribe2 = window.synchronizer.subscribe('map2', mockMapInstance2, mockCallbacks2);

            window.addResult(`   Map1 订阅结果: ${window.subscribe1 ? '✅ 成功' : '❌ 失败'}`, window.subscribe1 ? 'success' : 'error');
            window.addResult(`   Map2 订阅结果: ${window.subscribe2 ? '✅ 成功' : '❌ 失败'}`, window.subscribe2 ? 'success' : 'error');
            window.addResult(`   观察者数量: ${window.synchronizer.getObserverCount()}`, 'info');

            // 测试2: 事件数据验证
            window.addResult('2️⃣ 测试事件数据验证...', 'info');

            window.validPanEvent = {
                type: 'pan',
                payload: { dx: 10, dy: 20, screenPoint: { x: 100, y: 200 } }
            };

            const invalidPanEvent = {
                type: 'pan',
                payload: { dx: 'invalid' }
            };

            window.validZoomEvent = {
                type: 'zoom',
                payload: { delta: 1, anchorPoint: { x: 100, y: 200 } }
            };

            window.validMouseEvent = {
                type: 'mousemove',
                payload: { screenPoint: { x: 150, y: 250 } }
            };

            window.panValid = window.synchronizer.isValidEventData(window.validPanEvent);
            window.panInvalid = !window.synchronizer.isValidEventData(invalidPanEvent);
            window.zoomValid = window.synchronizer.isValidEventData(window.validZoomEvent);
            window.mouseValid = window.synchronizer.isValidEventData(window.validMouseEvent);

            window.addResult(`   有效 Pan 事件: ${window.panValid ? '✅ 通过' : '❌ 失败'}`, window.panValid ? 'success' : 'error');
            window.addResult(`   无效 Pan 事件: ${window.panInvalid ? '✅ 正确拒绝' : '❌ 失败'}`, window.panInvalid ? 'success' : 'error');
            window.addResult(`   有效 Zoom 事件: ${window.zoomValid ? '✅ 通过' : '❌ 失败'}`, window.zoomValid ? 'success' : 'error');
            window.addResult(`   有效 Mouse 事件: ${window.mouseValid ? '✅ 通过' : '❌ 失败'}`, window.mouseValid ? 'success' : 'error');

            // 测试3: 事件转发
            window.addResult('3️⃣ 测试事件转发...', 'info');

            window.receivedEvents = []; // 清空接收记录

            // 从 map1 发送 pan 事件
            window.panResult = window.synchronizer.notifyMapEvent('map1', window.validPanEvent);
            window.addResult(`   Pan 事件转发结果: ${window.panResult ? '✅ 成功' : '❌ 失败'}`, window.panResult ? 'success' : 'error');

            // 等待第一个事件处理完成，避免 isUpdating 标志冲突
            setTimeout(() => {
                // 从 map1 发送 zoom 事件
                window.zoomResult = window.synchronizer.notifyMapEvent('map1', window.validZoomEvent);
                window.addResult(`   Zoom 事件转发结果: ${window.zoomResult ? '✅ 成功' : '❌ 失败'}`, window.zoomResult ? 'success' : 'error');

                setTimeout(() => {
                    // 从 map2 发送 mouse 事件
                    window.mouseResult = window.synchronizer.notifyMapEvent('map2', window.validMouseEvent);
                    window.addResult(`   Mouse 事件转发结果: ${window.mouseResult ? '✅ 成功' : '❌ 失败'}`, window.mouseResult ? 'success' : 'error');

                    // 继续后续测试
                    window.continueTests();
                }, 100);
            }, 100);
        }

        window.continueTests = function() {

            // 等待异步操作完成后显示结果
            setTimeout(() => {
                addResult(`   实际接收到的事件数量: ${window.receivedEvents.length}`, 'info');
                window.receivedEvents.forEach((item, index) => {
                    addResult(`     ${index + 1}. ${item.target} 收到 ${item.event.type} 事件`, 'info');
                });

                // 测试4: 统计信息
                addResult('4️⃣ 测试统计信息...', 'info');
                const stats = window.synchronizer.getStats();
                addResult(`   总同步次数: ${stats.totalSyncs}`, 'info');
                addResult(`   成功次数: ${stats.successfulSyncs}`, 'info');
                addResult(`   失败次数: ${stats.failedSyncs}`, 'info');
                addResult(`   最后同步时间: ${stats.lastSyncTime}`, 'info');

                // 测试5: 禁用同步
                addResult('5️⃣ 测试禁用同步...', 'info');
                window.synchronizer.setEnabled(false);
                window.receivedEvents = [];

                const disabledResult = window.synchronizer.notifyMapEvent('map1', window.validPanEvent);
                addResult(`   禁用状态下的事件转发: ${disabledResult ? '❌ 意外成功' : '✅ 正确拒绝'}`, disabledResult ? 'error' : 'success');

                setTimeout(() => {
                    addResult(`   禁用状态下接收到的事件数量: ${window.receivedEvents.length} (应该为0)`, window.receivedEvents.length === 0 ? 'success' : 'error');

                    addResult('🎉 测试完成！', 'info');

                    // 验证结果
                    const allTestsPassed =
                        window.subscribe1 && window.subscribe2 &&
                        window.panValid && window.panInvalid && window.zoomValid && window.mouseValid &&
                        window.panResult && window.zoomResult && window.mouseResult &&
                        !disabledResult;

                    addResult(`📊 总体测试结果: ${allTestsPassed ? '✅ 全部通过' : '❌ 存在失败'}`, allTestsPassed ? 'success' : 'error');

                    if (allTestsPassed) {
                        addResult('🚀 MapViewSynchronizer 增强功能验证成功！可以进行下一步任务。', 'success');
                    } else {
                        addResult('⚠️  存在问题，需要进一步调试。', 'error');
                    }
                }, 100);
            }, 300); // 增加等待时间，确保所有事件都处理完成
        };

        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
        };
    </script>
</body>
</html>
