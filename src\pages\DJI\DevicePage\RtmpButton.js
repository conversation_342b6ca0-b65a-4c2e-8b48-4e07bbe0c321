import { HGet2 } from "@/utils/request";
import { Dropdown, Button } from "antd";
import { DownOutlined, SmileOutlined } from "@ant-design/icons";
import { useModel } from "umi";
import { getGuid } from "@/utils/helper";
import { isEmpty } from "@/utils/utils";
import { FJStart, JCStart } from "../DRCPage/Panels/RtmpChange";
import { checkIfFlyer } from "@/utils/utils";

const RtmpButton = ({ sn }) => {
  const { mapUrl, setMapUrl, baseMapLayers } = useModel("mapModel");
  const { setIfFJRtmp, setIfJCRtmp } = useModel("rtmpModel");
  const { DoCMD } = useModel("cmdModel");
  const { showCanvas,setShowCanvas } = useModel("pageModel");
  const device = JSON.parse(localStorage.getItem("device"));

  if (isEmpty(device)) return;

  const goUrl = async (url) => {
    HGet2(url);
  };

  const chageFbl = (id, i) => {
    const data = {
      video_id: id,
      video_quality: i,
    };
    DoCMD(device.SN, "live_set_quality", data);
  };

  const setDiTu = (i) => {
      const selectedLayer = baseMapLayers[i];
      if (selectedLayer) {
        localStorage.setItem("baseMap", selectedLayer.tileUrl);
        setMapUrl(selectedLayer.tileUrl);
      }
    };

  const getItem = (title, onClick) => {
    return {
      key: getGuid(),
      label: <a onClick={onClick}>{title}</a>,
    };
  };

  const changeFBL = (id) => {
    return {
      key: getGuid(),
      label: <a>调整分辨率</a>,
      children: [
        getItem("流畅", () => {
          if (checkIfFlyer()) chageFbl(id, 1);
        }),
        getItem("标清", () => {
          if (checkIfFlyer()) chageFbl(id, 2);
        }),
        getItem("高清", () => {
          if (checkIfFlyer()) chageFbl(id, 3);
        }),
        getItem("超清", () => {
          if (checkIfFlyer()) chageFbl(id, 4);
        }),
        getItem("自适应", () => {
          if (checkIfFlyer()) chageFbl(id, 0);
        }),
      ],
    };
  };

  const items = () => {
    const device = JSON.parse(localStorage.getItem("device"));
    return [
      {
        key: "1",
        label: <a>机场摄像头</a>,
        children: [
          {
            key: "1-1",
            // label: (  <a onClick={()=>goUrl("/api/v1/RtmpSource/Start3")}>开始直播</a>),
            //label: (  <a onClick={()=>{goUrl(`/api/v1/RtmpSource/Start?sn=${device.SN}&device=${device.SN}&camera=165-0-7`);setIfJCRtmp(true)}}>开始直播</a>),
            label: (
              <a
                onClick={() => {
                  if (checkIfFlyer()) {
                    JCStart(device);
                  }
                }}
              >
                开始直播
              </a>
            ),
          },
          {
            key: "1-2",
            // label: (  <a onClick={()=>goUrl("/api/v1/RtmpSource/Close1")}>停止直播</a>),
            label: (
              <a
                onClick={() => {
                  if (checkIfFlyer()) {
                    goUrl(
                      `/api/v1/RtmpSource/Close?sn=${device.SN}&device=${device.SN}&camera=165-0-7`
                    );
                  }
                }}
              >
                停止直播
              </a>
            ),
          },
          changeFBL(`${device.SN}/${device.Camera1}/normal-0`),
        ],
      },
      {
        key: "2",
        label: <a>飞机摄像头</a>,
        children: [
          {
            key: "2-1",
            //  label: (  <a onClick={()=>goUrl("/api/v1/RtmpSource/Start4")}>开始直播</a>),
            label: (
              <a
                onClick={() => {
                  if (checkIfFlyer()) {
                    FJStart(device);
                  }
                }}
              >
                开始直播
              </a>
            ),
          },
          {
            key: "2-2",
            //  label: (  <a onClick={()=>goUrl("/api/v1/RtmpSource/Close2")}>停止直播</a>),
            label: (
              <a
                onClick={() => {
                  if (checkIfFlyer()) {
                    goUrl(
                      `/api/v1/RtmpSource/Close?sn=${device.SN}&device=${device.SN2}&camera=${device.Camera2}`
                    );
                  }
                  setIfFJRtmp(false);
                }}
              >
                停止直播
              </a>
            ),
          },
          changeFBL(`${device.SN2}/${device.Camera2}/normal-0`),
        ],
      },
      {
              key: "3",
              label: <a>底图切换</a>,
              children: baseMapLayers.map((layer, index) => ({
                key: `3-${index}`,
                label: <a onClick={() => setDiTu(index)}>{layer.name}</a>,
              })),
            },
      {
        key: "4",
        label: (
          <a
            onClick={() => {
              if (checkIfFlyer()) {
                goUrl("/api/v1/WayLine/ToHome?sn=" + sn);
              }
            }}
          >
            一键返航
          </a>
        ),
      },
      {
        key: "5",
        label: (
          <a
            onClick={() => {
              if (checkIfFlyer()) {
                console.log("开启/关闭绘制")
                setShowCanvas(!showCanvas);
                console.log("@@@showCanvas",showCanvas)
              }
            }}
          >
            开启/关闭绘制
          </a>
        ),
      },
      // {
      //   key: '5',
      //   label: (  <a onClick={()=>goUrl("/api/v2/Debug/MqttRefrush")}>连接刷新</a>),
      // },
    ];
  };

  return (
    <Dropdown
      menu={{
        items: items(),
      }}
      placement="bottomRight"
      arrow
      //  theme="dark"
      style={
        {
          // zIndex: 1000000,
        }
      }
    >
      <Button type="text">常用命令</Button>
    </Dropdown>
  );
};

export default RtmpButton;
