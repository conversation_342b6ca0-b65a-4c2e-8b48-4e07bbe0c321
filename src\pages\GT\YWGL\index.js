import MyHead from "../components/MyHead"
import commonStyle from "../style/common.less";
import "@/pages/GT/style/antd-common.less";
import MyMenu from './Pages/MyMenu';
import { Modal } from "antd";
import { ConfigProvider } from "antd";
import locale from "antd/locale/zh_CN";
import { queryPage, queryPage2 } from "@/utils/MyRoute";
import { useState, useEffect } from "react";
import { useModel } from "umi";
import theme from 'antd/lib/theme';
import useConfigStore from "@/stores/configStore";

//运维管理
function App() {
  const { headerHeight } = useConfigStore()
  const [collapsed, setCollapsed] = useState(false);
  const { modal, open, setOpen, page, setPage, lastPage, listPage } = useModel("pageModel");

  const handlePageChange = (path) => {

    const pageKey = path.split('/').pop(); // 取最后一段作为页面标识
    setPage(queryPage(pageKey));
  };

  const headList = [
  ];

  return (
    <div className="gt-page">
      <div
        className={commonStyle.gt_back_black}
        style={{
          position: "relative",
          overflow: "hidden",
          minHeight: `calc(100vh - ${headerHeight}px)`,
          display: "flex",
          flexDirection: "column"
        }}
      >
        <div style={{
          display: "flex",
          flex: 1,
          position: "relative",
          overflow: "hidden",
          width: "100vw"
        }}>
          <MyMenu handlePageChange={handlePageChange} setCollapsed={setCollapsed} collapsed={collapsed}></MyMenu>
          <div
            className="blackBackground"
            style={{
              flex: 1,
              marginLeft: collapsed ? 80 : 160,
              transition: "margin-left 0.2s"
            }}
          >
            <ConfigProvider locale={locale}>
              {page}
            </ConfigProvider>
            <Modal
              title={null}
              footer={null}
              onOk={null}
              style={{ paddingBottom: 72.0 }}
              open={open}
              onCancel={() => setOpen(false)}
            >
              {modal}
            </Modal>
          </div>
        </div>
      </div>
    </div>
  );
}
export default App;
