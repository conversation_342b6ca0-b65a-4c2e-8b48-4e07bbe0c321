import { isEmpty } from "@/utils/utils";
import { useState, useRef } from "react";
import { GetMqttClient } from "@/utils/websocket";
import dayjs from "dayjs";
import { getGuid } from "@/utils/helper";
import { message } from "antd";
import <PERSON>rr<PERSON><PERSON> from "@/pages/DJI/WebsocketDemo/error.json";
import CMDJson from "@/pages/DJI/HmsPage/cmdName.json";

export default function cmdModel() {
  const mqttC = useRef({});
  const [unLock, setUnLock] = useState([]);

  const CmdMqttConn = (sn) => {
    if (!isEmpty(mqttC.current)) {
      mqttC.current.end();
    }

    mqttC.current = GetMqttClient();
    mqttC.current.on("message", (topic, message) => {
      updateVal(topic, message);
    });
    mqttC.current.subscribe("thing/product/" + sn + "/services_reply");
  };

  const DoCMD = (sn, method, data) => {
    console.log("DoCMD", sn, method, data);

    if (isEmpty(sn)) return;
    let s = {};
    s.bid = getGuid();
    s.tid = getGuid();
    s.gateway = sn;
    s.data = data;
    s.timestamp = dayjs().valueOf();
    s.method = method;
    let topic = "thing/product/" + sn + "/services";
    if(isEmpty(mqttC.current)) return;
    mqttC.current.publish(topic, JSON.stringify(s));
  };

  const DoCMD2 = (topic, data) => {
    if(isEmpty(mqttC.current)) return;
    mqttC.current.publish(topic, JSON.stringify(data));
  };

  const DoCMD3 = (sn, topic, data) => {
    if(isEmpty(mqttC.current)) return;
    let s = {};
    s.bid = getGuid();
    s.tid = getGuid();
    s.gateway = sn;
    s.data = data;
    s.timestamp = dayjs().valueOf();
    mqttC.current.publish(topic, JSON.stringify(s));
  };

  const getMethod = (x) => {
    const x2 = CMDJson[x];
    if (!isEmpty(x2)) return x2;
    return x;
  };

  const pb1 = (xx) => {
    if (
      xx.includes("camera_screen_drag") ||
      xx.includes("camera_focal_length_set") ||
      xx.includes("live") ||
      xx.includes("camera_aim")
    )
      return false;
    return true;
  };

  const updateVal = (t1, m1) => {
    const xx = JSON.parse(m1);
    //如果是获取日志列表，直接返回
    if (xx.method == "fileupload_list") {
      //setLogList(xx.data.files[0].list);
      return;
    }
    let rl = ErrJson[xx.data.result];
    if (rl == undefined) rl = "错误-" + xx.data.result;

    if (xx !== null && pb1(m1)) {
      message.info(getMethod(xx.method) + ":" + rl);
    }

    if (xx.method == "unlock_license_list") {
      console.log("unlock_license_list", xx.data);
      setUnLock(xx.data);
    }

    if (xx.method == "unlock_license_switch") {
    }
  };

  return { CmdMqttConn, DoCMD, DoCMD2, DoCMD3, unLock };
}
