import React, { useRef, useState, useEffect } from "react";
import {
  MapContainer,
  LayersControl,
  Tooltip,
  LayerGroup,
  Polyline,
  TileLayer,
  GeoJSON,
  useMapEvents,
  Circle,
  Polygon,
} from "react-leaflet";
import "leaflet/dist/leaflet.css";
import L from "leaflet";
import { GetDiTuGps } from "./ditu";
import { isEmpty } from "./../../utils/utils";
import { Location_Market6, Location_Market5 } from "./dt_market";
import { useModel } from "umi";
import DrawWayLinePanel from "./DrawWayLinePanel";
import { ConvertPList, getPList } from "./helper";
import { Wgs84ToGcj02, out_of_china } from "./gps_helper";
import { ZXLine, ZXXPoints } from "./gcline";
import "leaflet-kml";
import kFile from "@/assets/kmls/map.geojson";
import { ZTDT, getLayers } from "./DiTuJson/ditu";
import MeasurePanel from "../DJI/OrgPage/Panels/MeasurePanel";
import { axiosApi } from "@/services/general";
import NoFlyArea from "@/pages/Maps/NoFlyArea";
import useConfigStore from "@/stores/configStore";

const DJBaseMap = (props) => {
  const {setMapSelf} = useConfigStore();
  const { w1, h1, sn, device, showBZBtn, isDrc } = props;
  const { gps, pList } = useModel("gpsModel");
  const { mapUrl, ifWayline } = useModel("mapModel");
  const [pL, setPL] = useState([]);
  const [center, setCenter] = useState([device.Lat, device.Lng]);
  const mapRef = useRef();
  const [zoom, setZoom] = useState(16);
  const [currentLayer, setCurrentLayer] = useState(null); // 切换当前图层
  const [geojsonData, setGeojsonData] = useState(null);  //施工范围
  const isFirstRender = useRef(true); // 初次渲染
  //施工范围
  // useEffect(() => {
  //   const fetchGeoJSON = async () => {
  //     try {
  //       const response = await fetch(
  //         "http://183.222.16.139:9000/300bdf2b-a150-406e-be63-d28bd29b409f/b3dm/kml/Geo2.geojson"
  //       );
  //       if (!response.ok) {
  //         throw new Error("Network response was not ok");
  //       }
  //       const data = await response.json();
  //       setGeojsonData(data);
  //     } catch (error) {}
  //   };

  //   fetchGeoJSON();
  // }, []);
  useEffect(()=>{
    if(mapRef.current){
        mapRef.current.setMaxZoom(23);
        mapRef.current.setMinZoom(3);
      setMapSelf(mapRef.current);
    }
  },[mapRef.current])

  useEffect(() => {
    if (isFirstRender.current) {
      // 第一次渲染后设置标志为false
      isFirstRender.current = false;
    }
    if (geojsonData && mapRef.current) {
      const geoJsonStyle = {
        fillColor: '#FFFF00',  // 填充颜色
        fillOpacity: 0.5       // 填充透明度
      };
      const geoJsonLayer = L.geoJSON(geojsonData,{
        style: geoJsonStyle
      });
      if (geoJsonLayer.getBounds().isValid()) {
        const map = mapRef.current.leafletElement;
        map?.fitBounds(geoJsonLayer.getBounds());
      }
    }
  }, [geojsonData]);

  const map_biaozhu =
    "http://t0.tianditu.gov.cn/cia_w/wmts?" +
    "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
    "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}" +
    "&tk=d26935ae77bbc1fb3a6866a5b6ff573f"; //地图标注
  const getP = (p1) => {
    const pb1 = GetDiTuGps(mapUrl);
    if (pb1) {
      return Wgs84ToGcj02(p1[0], p1[1]);
    }
    return p1;
  };

  const getLine = () => {
    const list = [];
    let n = 0;
    let v1 = [];
    const ZL = ZXXPoints.split(",");
    ZL.forEach((e) => {
      if (n == 1) {
        v1[1] = Number(e);
        list.push([...v1]);
        n = 0;
      } else {
        v1[0] = Number(e);
        n = 1;
      }
    });
  };
  if (isEmpty(device)) return <div></div>;

  useEffect(() => {
    let pL2 = getPList(localStorage.getItem("wayPoints"));
    if (GetDiTuGps(mapUrl)) {
      pL2 = ConvertPList(pL2);
    }
    setPL(pL2);
  }, [mapUrl]);

  useEffect(() => {
    setCenter(getP(gps));
  }, [gps]);

  const updateCenter = async (newCenter, newValue, newZoom) => {
    if (!mapRef.current) return;
    let isLatlng =
      Array.isArray(newCenter) &&
      newCenter.length > 0 &&
      Array.isArray(newCenter[0]) &&
      newCenter[0].length === 2;

    if (newValue?.MapType == 0 || newValue?.MapType == 1) {
      // 切换正射影像图层
      if (currentLayer && currentLayer === newValue.Url) {
        setCurrentLayer(null);
      } else {
        setCurrentLayer(newValue);
      }
      // if (currentLayer) {
      //   mapRef.current.eachLayer((layer) => {
      //     if (layer.options && layer.options.layerType === 'tile') {
      //       mapRef.current.removeLayer(layer);
      //     }
      //   });
      // }
      //  L.tileLayer(newZSUrl.Url, { maxZoom: 20, layerType: 'tile' }).addTo(mapRef.current);
    }
    if (newValue?.MapType == 2) {
      //获取geojson数据
      await fetch(newValue.Url)
        .then((response) => response.json())
        .then((data) => {
          if (geojsonData && geojsonData === data) {
            setGeojsonData(null);
          }
          setGeojsonData(data);
        });
    }
    if (isLatlng) {
      //设置地图中心点和缩放级别
      setCenter(newCenter);
      mapRef.current.setView(newCenter, newZoom);
    }
  };

  const getWayLine = () => {
    const list = [];
    if (isEmpty(pL)) return list;
    if (!ifWayline) return list;
    list.push(<Polyline weight={2} color={"orange"} positions={pL} />);
    return list;
  };

  const getHomeIcon = () => {
    if (isEmpty(device)) return;
    let p2 = [device.Lat, device.Lng];
    if (isEmpty(p2)) return;

    if (GetDiTuGps(mapUrl)) {
      p2 = Wgs84ToGcj02(p2[0], p2[1]);
    }
    return Location_Market5({
      Lat: p2[0],
      Lng: p2[1],
      DeviceName: device.DName,
    });
  };

  const getFJIcon = () => {
    const jcdata1 = JSON.parse(localStorage.getItem("jcdata1"));
    if (isEmpty(gps) || isEmpty(jcdata1) || jcdata1.mode_code == 0) return;
    let p2 = [gps[0], gps[1]];

    if (GetDiTuGps(mapUrl)) {
      p2 = Wgs84ToGcj02(p2[0], p2[1]);
    }

    return Location_Market6({ Lat: p2[0], Lng: p2[1], DeviceName: "无人机" });
  };

  const dt_bz2 =
    "http://t0.tianditu.gov.cn/cia_w/wmts?" +
    "SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles" +
    "&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}" +
    "&tk=d26935ae77bbc1fb3a6866a5b6ff573f";
  return (
    <div>
      <MapContainer
        ref={mapRef}
        layersOptions={null}
        attributionControl={null}
        zoomControl={null}
        preferCanvas={true}
        center={center}
        zoom={zoom}
        style={{
          width: "100%",
          height: isDrc ? h1 : "calc(100vh - 56px)",
          borderRadius: 5.0,
          overflow: "hidden",
        }}
      >
        <TileLayer attribution={null} url={mapUrl} maxZoom={23} maxNativeZoom={18}  subdomains= {['0', '1', '2', '3', '4', '5', '6', '7']} />
        <TileLayer attribution={null} url={dt_bz2} />
        {currentLayer &&
          (currentLayer.MapType == 1 || currentLayer.MapType == 0) && (
            <TileLayer
              key={currentLayer.Url}
              tms={currentLayer.MapType == 1 ? true : false}
              attribution={null}
              url={currentLayer.Url}
              maxZoom={currentLayer?.MaxZoom}
              minZoom={currentLayer?.MinZoom}
              maxNativeZoom={18}
            />
          )}
        {geojsonData && <GeoJSON data={geojsonData} />}
        {mapRef.current && <NoFlyArea needsConversion={GetDiTuGps(mapUrl)}></NoFlyArea>}
        {getLayers()}
        {getHomeIcon()}
        {getFJIcon()}
        {
          <DrawWayLinePanel
            ifDraw={true}
            gps={gps}
            sn={sn}
            baseMap={mapUrl}
            key={sn}
          ></DrawWayLinePanel>
        }
        {getWayLine()}
      </MapContainer>
      <MeasurePanel
        map={mapRef.current}
        left={380}
        right={null}
        updateCenter={updateCenter}
        showSearch={true}
        showOrthofire={true}
        showBZBtn={showBZBtn}
      />
    </div>
  );
};

export default DJBaseMap;
