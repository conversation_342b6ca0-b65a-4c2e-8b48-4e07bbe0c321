# 多屏地图视图同步实现文档

## 功能概述

多屏地图视图同步功能允许在多屏对比模式下，当用户操作任一地图的缩放或平移时，其他地图视图会同步更新，保持所有地图显示相同的区域和缩放级别。该功能基于观察者模式实现，支持 2D 和 3D 地图的混合同步。

## 技术实现

### 1. 观察者模式实现

系统使用观察者模式实现地图视图的同步，主要包含以下组件：

- **MapViewSynchronizer 服务**：核心同步服务，负责管理观察者（地图实例）的订阅和通知
- **useMapViewSync Hook**：React Hook，用于将地图组件与同步服务集成
- **MultiScreenPanel 组件**：提供用户界面控制同步功能的开启/关闭
- **Map 主组件**：管理多屏模式和同步状态，将状态传递给子组件

### 2. 核心文件说明

#### MapViewSynchronizer.js

MapViewSynchronizer 服务是基于观察者模式的核心实现，负责管理地图实例的订阅和视图状态的广播。主要特性包括：

- **观察者管理**：支持地图实例的订阅和取消订阅
- **视图同步控制**：可启用/禁用同步功能
- **视图状态验证**：验证视图状态的有效性，防止无效数据导致错误
- **防重复更新**：比较视图状态，避免重复通知相同的视图变化
- **防无限循环**：使用标志位和源地图排除机制防止同步循环
- **错误处理**：全面的错误捕获和处理机制
- **调试支持**：可选的调试模式，提供详细日志
- **性能统计**：记录同步次数、成功率等统计信息

关键方法：

```javascript
// 设置同步启用状态
setEnabled(enabled) {
  this.enabled = Boolean(enabled);
  this.log(`地图视图同步已${this.enabled ? '启用' : '禁用'}`);
  return this.enabled;
}

// 添加观察者（地图实例）
subscribe(id, mapInstance, callbacks) {
  this.observers.set(id, { mapInstance, callbacks });
  this.log(`地图 ${id} 已订阅视图同步服务`);
  return true;
}

// 通知所有观察者视图变化
notifyViewChange(sourceId, viewState) {
  // 验证和处理逻辑...
  
  // 通知除源地图外的所有观察者
  this.observers.forEach((observer, id) => {
    if (id !== sourceId) {
      observer.callbacks.onViewChange(viewState);
    }
  });
}
```

#### useMapViewSync.js

useMapViewSync 是一个自定义 React Hook，用于将地图组件与同步服务集成。主要特性包括：

- **自动订阅管理**：组件挂载时自动订阅，卸载时自动取消订阅
- **事件监听**：自动监听地图的视图变化事件
- **视图状态转换**：处理不同地图类型的视图状态格式
- **防抖处理**：使用防抖函数避免频繁触发同步
- **错误处理**：捕获和报告同步过程中的错误
- **实时/延迟同步**：支持实时同步和移动结束后同步两种模式
- **手动同步**：提供手动触发同步的方法

关键功能：

```javascript
// 防抖函数，避免频繁触发视图同步
const debounce = (func, wait) => {
  let timeout;
  return function(...args) {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// 处理当前地图的视图变化（使用防抖）
const handleViewChange = useCallback(
  debounce((viewState) => {
    // 验证和处理逻辑...
    mapViewSynchronizer.notifyViewChange(mapId, viewState);
  }, options.debounceTime),
  [mapId, syncEnabled]
);

// 设置地图移动事件监听
useEffect(() => {
  if (!mapInstance || !syncEnabled) return;
  
  // 根据地图类型设置不同的事件监听
  if (mapType === '2D') {
    mapInstance.on('moveend', moveEndHandler);
  } else if (mapType === '3D') {
    mapInstance.camera.moveEnd.addEventListener(moveEndHandler);
  }
  
  // 清理函数...
}, [mapInstance, syncEnabled, mapType]);
```

### 4. 数据流

1. 用户在 MultiScreenPanel 中开启视图同步
2. MapViewSynchronizer 服务的 enabled 状态被设置为 true
3. 用户操作某个地图，触发该地图的视图变化事件
4. 地图组件调用 handleViewChange 方法，传递当前视图状态
5. MapViewSynchronizer 服务接收到通知，并将视图状态广播给其他地图
6. 其他地图接收到视图状态更新，调用各自的 handleExternalViewUpdate 方法更新视图

### 5. 防止无限循环

为防止地图视图更新的无限循环，系统采用了以下策略：

1. **更新标志控制**：使用 isUpdating 标志控制更新状态，防止重复进入更新流程
2. **源地图排除**：在通知过程中，排除触发更新的源地图，避免循环更新
3. **视图状态比较**：比较新旧视图状态，避免处理相同的视图状态
4. **异步更新**：使用 setTimeout 延迟重置更新标志，确保所有更新完成
5. **防抖处理**：使用防抖函数减少频繁更新，避免更新风暴

## 使用方法

1. 在多屏对比模式下，点击右上角的"设置"按钮打开配置面板
2. 在配置面板中，勾选"同步所有地图的缩放和平移"选项
3. 点击"应用配置"按钮
4. 现在操作任一地图，其他地图将同步显示相同的区域

## 扩展性

该实现支持以下扩展：

1. **多地图类型支持**：可以轻松扩展支持更多地图类型（如 Google Maps、MapBox 等）
2. **细粒度控制**：可以添加更细粒度的同步控制（如仅同步缩放但不同步平移）
3. **视图属性扩展**：可以扩展支持更多视图属性的同步（如倾斜角度、旋转等）
4. **性能优化**：可以添加更多性能优化策略（如视图变化阈值、同步频率控制等）
5. **用户界面增强**：可以添加更丰富的用户界面控制（如同步状态指示器、同步历史记录等）

## 注意事项

1. **坐标系差异**：2D 和 3D 地图之间的同步可能存在一定的视觉差异，这是由于不同地图引擎的坐标系统和渲染方式不同导致的
2. **性能考虑**：在低性能设备上，同步多个 3D 地图可能会导致性能问题，建议在这种情况下使用 2D 地图
3. **使用限制**：地图同步功能仅在多屏对比模式下可用，单屏模式下不会启用同步
4. **网络影响**：在加载大量地图数据时，不同地图的加载速度可能不同，这可能导致短暂的视图不同步
5. **浏览器兼容性**：该功能在现代浏览器中表现良好，但在旧版浏览器中可能存在兼容性问题

## 性能优化

为确保地图同步功能的高性能，我们实施了以下优化措施：

1. **防抖处理**：使用防抖函数减少频繁更新，避免在地图平滑移动过程中触发过多同步
2. **视图状态比较**：比较新旧视图状态，只有当视图发生实质性变化时才触发同步
3. **异步更新**：使用异步方式更新地图视图，避免阻塞主线程
4. **选择性事件监听**：根据配置选择性地监听不同类型的事件（moveend vs. move）
5. **资源释放**：组件卸载时自动清理事件监听和订阅，避免内存泄漏

## 测试验证

该功能已经过以下测试验证：

1. **功能测试**：验证基本的同步功能在不同布局和地图类型下正常工作
2. **性能测试**：验证在多个地图实例同时同步时的性能表现
3. **边界测试**：验证在极端情况下（如快速连续操作）的行为
4. **错误恢复测试**：验证在出现错误时的恢复能力
5. **兼容性测试**：验证在不同浏览器和设备上的兼容性


---

### 任务列表 (按优先级排列)

以下是将现有架构（状态同步）重构为“事件转发”模式的具体任务。

#### P0: 核心架构升级 (Foundation)

**任务 1: 增强 `MapViewSynchronizer` 以支持通用事件**

*   **目标**: 让同步服务不仅能通知“视图状态变化”，还能通知任意“地图事件”。
*   **技术细节**:
    1.  扩展 `subscribe` 方法的 `callbacks` 参数，支持 `onMapEvent` 回调
    2.  添加 `notifyMapEvent` 方法用于事件广播
    3.  添加事件类型验证和错误处理
    4.  保持向后兼容性，现有的 `onViewChange` 继续工作

*   **实现要点**:
    ```javascript
    // src/pages/GT/YZT/pages/Map/services/MapViewSynchronizer.js
    class MapViewSynchronizer {
      // 扩展订阅方法
      subscribe(id, mapInstance, callbacks) {
        // callbacks: { onViewChange?, onMapEvent?, onError? }
        this.observers.set(id, { mapInstance, callbacks });
      }

      // 新增事件广播方法
      notifyMapEvent(sourceId, eventData) {
        // 事件数据结构:
        // { type: 'pan', payload: { dx, dy, screenPoint } }
        // { type: 'zoom', payload: { delta, anchorPoint } }
        // { type: 'mousemove', payload: { screenPoint } }
        // { type: 'mousedown', payload: { screenPoint, button } }
        // { type: 'mouseup', payload: { screenPoint, button } }

        if (!this.enabled || !this.isValidEventData(eventData)) return false;

        let successCount = 0;
        this.observers.forEach((observer, id) => {
          if (id !== sourceId && observer.callbacks.onMapEvent) {
            try {
              observer.callbacks.onMapEvent(eventData);
              successCount++;
            } catch (error) {
              console.error(`事件转发到地图 ${id} 失败:`, error);
              if (observer.callbacks.onError) {
                observer.callbacks.onError(error, eventData);
              }
            }
          }
        });

        return successCount > 0;
      }

      // 事件数据验证
      isValidEventData(eventData) {
        const validTypes = ['pan', 'zoom', 'mousemove', 'mousedown', 'mouseup', 'mouseleave'];
        return eventData &&
               typeof eventData === 'object' &&
               validTypes.includes(eventData.type) &&
               eventData.payload;
      }
    }
    ```

*   **测试要点**:
    - 验证事件数据格式正确性
    - 测试错误处理机制
    - 确保向后兼容性

---

#### P1: 实现新的同步逻辑 (Core Logic)

**任务 2: 重构 `useMapViewSync` Hook 以处理事件**

*   **目标**: 切换 Hook 的核心逻辑，从监听 `moveend` 变为监听和处理底层的 DOM 事件。
*   **技术细节**:
    1.  **添加事件接收器**: 创建 `handleExternalMapEvent` 函数，根据接收到的事件类型调用对应的地图 API
    2.  **添加事件捕获器**: 在地图容器上监听原始DOM事件
    3.  **坐标转换处理**: 实现屏幕坐标与地图坐标的转换
    4.  **拖拽状态管理**: 跟踪鼠标操作状态，确保操作连续性
    5.  **性能优化**: 使用节流和防抖机制优化高频事件

*   **实现要点**:
    ```javascript
    // src/pages/GT/YZT/pages/Map/hooks/useMapViewSync.js
    const useMapViewSync = (mapInstance, mapId, syncEnabled, syncMode = 'on_end', options) => {
      // 状态管理
      const isDraggingRef = useRef(false);
      const lastMousePosRef = useRef({ x: 0, y: 0 });
      const [virtualMousePos, setVirtualMousePos] = useState(null);
      const eventThrottleRef = useRef(null);

      // 1. 事件接收器 - 处理来自其他地图的事件
      const handleExternalMapEvent = useCallback((eventData) => {
        if (!mapInstance || isReceivingUpdateRef.current) return;

        const { type, payload } = eventData;
        isReceivingUpdateRef.current = true;

        try {
          // 更新虚拟鼠标位置
          if (payload.screenPoint) {
            setVirtualMousePos(payload.screenPoint);
          }
          if (type === 'mouseleave') {
            setVirtualMousePos(null);
          }

          // 根据地图类型执行相应操作
          if (mapType === '2D') {
            handleLeafletEvent(type, payload);
          } else if (mapType === '3D') {
            handleCesiumEvent(type, payload);
          }
        } finally {
          setTimeout(() => {
            isReceivingUpdateRef.current = false;
          }, 50);
        }
      }, [mapInstance, mapType]);

      // 2D地图事件处理
      const handleLeafletEvent = useCallback((type, payload) => {
        switch (type) {
          case 'pan':
            if (mapInstance.panBy) {
              mapInstance.panBy([payload.dx, payload.dy], { animate: false });
            }
            break;
          case 'zoom':
            if (payload.anchorPoint && mapInstance.zoomIn && mapInstance.zoomOut) {
              const method = payload.delta > 0 ? 'zoomIn' : 'zoomOut';
              mapInstance[method](Math.abs(payload.delta), {
                animate: false,
                anchor: payload.anchorPoint
              });
            }
            break;
        }
      }, [mapInstance]);

      // 3D地图事件处理
      const handleCesiumEvent = useCallback((type, payload) => {
        if (!mapInstance.camera) return;

        switch (type) {
          case 'pan':
            // Cesium相机平移
            const camera = mapInstance.camera;
            const moveVector = new Cesium.Cartesian3(payload.dx, payload.dy, 0);
            camera.move(moveVector, payload.dx * 1000); // 根据实际需要调整系数
            break;
          case 'zoom':
            // Cesium相机缩放
            if (payload.delta > 0) {
              mapInstance.camera.zoomIn(payload.delta * 1000);
            } else {
              mapInstance.camera.zoomOut(Math.abs(payload.delta) * 1000);
            }
            break;
        }
      }, [mapInstance]);

      // 3. 事件捕获器 - 监听当前地图的DOM事件
      useEffect(() => {
        if (!mapInstance || !syncEnabled || syncMode !== 'realtime') return;

        const mapContainer = mapInstance.getContainer ?
          mapInstance.getContainer() :
          mapInstance.canvas; // Cesium使用canvas

        if (!mapContainer) return;

        // 节流函数
        const throttle = (func, limit) => {
          if (eventThrottleRef.current) return;
          eventThrottleRef.current = setTimeout(() => {
            func();
            eventThrottleRef.current = null;
          }, limit);
        };

        const onMouseDown = (e) => {
          isDraggingRef.current = true;
          lastMousePosRef.current = { x: e.clientX, y: e.clientY };

          mapViewSynchronizer.notifyMapEvent(mapId, {
            type: 'mousedown',
            payload: {
              screenPoint: { x: e.clientX, y: e.clientY },
              button: e.button
            }
          });
        };

        const onMouseUp = (e) => {
          isDraggingRef.current = false;

          mapViewSynchronizer.notifyMapEvent(mapId, {
            type: 'mouseup',
            payload: {
              screenPoint: { x: e.clientX, y: e.clientY },
              button: e.button
            }
          });
        };

        const onMouseMove = (e) => {
          const currentPos = { x: e.clientX, y: e.clientY };

          // 广播鼠标位置（节流处理）
          throttle(() => {
            mapViewSynchronizer.notifyMapEvent(mapId, {
              type: 'mousemove',
              payload: { screenPoint: currentPos }
            });
          }, 16); // ~60fps

          // 如果正在拖拽，计算并广播平移事件
          if (isDraggingRef.current) {
            const dx = currentPos.x - lastMousePosRef.current.x;
            const dy = currentPos.y - lastMousePosRef.current.y;

            if (Math.abs(dx) > 1 || Math.abs(dy) > 1) { // 最小移动阈值
              mapViewSynchronizer.notifyMapEvent(mapId, {
                type: 'pan',
                payload: { dx, dy, screenPoint: currentPos }
              });

              lastMousePosRef.current = currentPos;
            }
          }
        };

        const onWheel = (e) => {
          e.preventDefault();

          const delta = e.deltaY > 0 ? -1 : 1;
          const anchorPoint = mapType === '2D' ?
            mapInstance.containerPointToLatLng([e.clientX, e.clientY]) :
            { x: e.clientX, y: e.clientY };

          mapViewSynchronizer.notifyMapEvent(mapId, {
            type: 'zoom',
            payload: {
              delta,
              anchorPoint,
              screenPoint: { x: e.clientX, y: e.clientY }
            }
          });
        };

        const onMouseLeave = () => {
          isDraggingRef.current = false;
          mapViewSynchronizer.notifyMapEvent(mapId, {
            type: 'mouseleave',
            payload: {}
          });
        };

        // 添加事件监听器
        mapContainer.addEventListener('mousedown', onMouseDown);
        mapContainer.addEventListener('mouseup', onMouseUp);
        mapContainer.addEventListener('mousemove', onMouseMove);
        mapContainer.addEventListener('wheel', onWheel, { passive: false });
        mapContainer.addEventListener('mouseleave', onMouseLeave);

        // 清理函数
        return () => {
          mapContainer.removeEventListener('mousedown', onMouseDown);
          mapContainer.removeEventListener('mouseup', onMouseUp);
          mapContainer.removeEventListener('mousemove', onMouseMove);
          mapContainer.removeEventListener('wheel', onWheel);
          mapContainer.removeEventListener('mouseleave', onMouseLeave);

          if (eventThrottleRef.current) {
            clearTimeout(eventThrottleRef.current);
            eventThrottleRef.current = null;
          }
        };
      }, [mapInstance, mapId, syncEnabled, syncMode, mapType]);

      return {
        virtualMousePos,
        // ... 其他现有返回值
      };
    };
    ```

*   **测试要点**:
    - 验证2D/3D地图事件处理正确性
    - 测试高频事件的性能表现
    - 确保坐标转换准确性
    - 验证拖拽操作的连续性

---

#### P2: UI 实现与集成 (UI Layer)

**任务 3: 渲染虚拟鼠标**

*   **目标**: 在非源地图（源地图：真实鼠标所在地图实例）上显示一个跟随移动的鼠标图标。
*   **动作**:
    1.  `useMapViewSync` Hook 返回 `virtualMousePos` 状态。
    2.  在 `OneMap` 组件中，使用这个状态来渲染一个绝对定位的元素。

*   **伪代码 (JSX)**:
    ```jsx
    // src/pages/GT/YZT/pages/Map/components/OneMap/index.js (or similar)
    const { virtualMousePos } = useMapViewSync(...);

    return (
      <div style={{ position: 'relative' }}>
        {/* The actual map container */}
        <div ref={mapRef} className="map-container" />

        {/* Virtual Mouse Element */}
        {virtualMousePos && (
          <CursorIcon style={{
            position: 'absolute',
            left: virtualMousePos.x,
            top: virtualMousePos.y,
            zIndex: 9999,
            pointerEvents: 'none', // Crucial: makes it non-interactive
            transform: 'translate(-50%, -50%)' // Center the icon
          }} />
        )}
      </div>
    );
    ```

**任务 4: 提供同步模式切换选项 (临时，用于调试新的事件转发功能是否正常)**

*   **目标**: 允许用户在旧的“结束时同步”和新的“实时同步”之间切换，便于调试。
*   **动作**:
    1.  在 `MultiScreenPanel` 组件中，将原来的复选框改为一个单选组。
    2.  将选择的模式 (`syncMode: 'realtime' | 'on_end' | 'off'`) 传递下去。
    3.  在 `useMapViewSync` 中，根据 `syncMode` 决定是启用新的事件捕获器，还是旧的 `moveend` 监听器。

---

### Mermaid Diagram: 新的事件转发流程

下面是新流程的序列图，清晰地展示了各个部分如何交互。

```mermaid
sequenceDiagram
    participant User
    participant SourceMapUI as Source Map (UI)
    participant useMapViewSyncSrc as useMapViewSync (Source)
    participant MVS as MapViewSynchronizer
    participant useMapViewSyncTgt as useMapViewSync (Target)
    participant TargetMapUI as Target Map (UI)
    participant VirtualMouse as Virtual Mouse (UI)

    User->>SourceMapUI: 拖动鼠标 (mousemove)
    SourceMapUI->>useMapViewSyncSrc: 捕获 DOM 'mousemove' 事件
    useMapViewSyncSrc->>useMapViewSyncSrc: 计算鼠标位置和拖动增量
    useMapViewSyncSrc->>MVS: notifyMapEvent({type: 'pan', payload: {dx, dy}})
    useMapViewSyncSrc->>MVS: notifyMapEvent({type: 'mousemove', payload: {x, y}})

    MVS->>useMapViewSyncTgt: onMapEvent({type: 'pan', ...})
    useMapViewSyncTgt->>TargetMapUI: map.panBy(dx, dy)
    TargetMapUI-->>TargetMapUI: 地图平滑移动

    MVS->>useMapViewSyncTgt: onMapEvent({type: 'mousemove', ...})
    useMapViewSyncTgt->>VirtualMouse: 更新虚拟鼠标位置 state
    VirtualMouse-->>VirtualMouse: UI 重新渲染，显示在 (x, y)

```

这个任务列表和图表为您提供了一个清晰、可执行的重构路线图。从核心服务的小幅增强开始，到重写 Hook 的核心逻辑，再到最终的 UI 集成，每一步都建立在前一步的基础上。