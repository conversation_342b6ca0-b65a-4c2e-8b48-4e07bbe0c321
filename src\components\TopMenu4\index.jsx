import { Menu, message } from "antd";
import React, { useEffect, useState } from "react";
import { Row, Col, Button, Dropdown } from "antd";
import styles from "./topmenu.less";
import { UserOutlined } from "@ant-design/icons";
import { useModel, history } from "umi";
import { queryPage2 } from "@/utils/MyRoute";
import { ChangeFullScreen } from "@/utils/helper";
import { AddSystemLog, getBodyW2, isEmpty } from "@/utils/utils";
import OrgPage from "@/pages/DJI/OrgPage";
import MyBell from "@/components/MyBell";
import configStore from "@/stores/configStore";
const TopMenu = () => {
  let { systemInfos } = configStore();
  const [current, setCurrent] = useState("");
  const { setPage } = useModel("pageModel");

  const handleClick = (e) => {
    localStorage.removeItem("PageParams");
    setCurrent(e.target.innerText);
    console.log(current);
    setPage(queryPage2(e.target.innerText));
  };
  const Exit = () => {
    localStorage.clear();
    history.push("/DJ/login");
  };

  const items = (u1) => {
    if (u1.Authority == "admin") {
      return [
        {
          key: "0",
          label: <a onClick={handleClick}>用户管理</a>,
        },
        {
          key: "2",
          label: <a onClick={null}>{u1.OrgName}</a>,
        },
        {
          key: "1",
          label: (
            <a
              onClick={async () => {
                await AddSystemLog("退出登录-成功", "");
                setPage(<OrgPage />);
                Exit();
              }}
            >
              退出登录
            </a>
          ),
        },
      ];
    }
    return [
      {
        key: "1",
        label: (
          <a
            onClick={async () => {
              await AddSystemLog("退出登录-成功", "");
              setPage(<OrgPage />);
              Exit();
            }}
          >
            退出登录
          </a>
        ),
      },
    ];
  };

  const UserButton = () => {
    const u1 = JSON.parse(localStorage.getItem("user"));
    if (isEmpty(u1)) {
      Exit();
      return;
    }

    return (
      <Dropdown
        menu={{ items: items(u1) }}
        placement="bottomRight"
        arrow
        className={styles.btn}
      >
        <Button type="text" icon={<UserOutlined />}>
          {" "}
          {u1.Name}
        </Button>
      </Dropdown>
    );
  };

  const getBtn = (title) => {
    return (
      <Button
        type="text"
        onClick={handleClick}
        className={styles.btn}
        key={title}
      >
        {title}
      </Button>
    );
  };

  const getBtn2 = (title) => {
    return (
      <Col>
        <Button
          type="text"
          onClick={handleClick}
          className={styles.btn}
          key={title}
        >
          {title}
        </Button>
      </Col>
    );
  };

  const items2 = (sL) => {
    let userinfo = JSON.parse(localStorage.getItem("user")); //用户信息
    if (isEmpty(userinfo)) {
      Exit();
      return;
    }
    let Mail = userinfo.Mail; //要取消的系统选项
    const list = [];
    let nn = 0;
    if (Mail == null) {
      sL.forEach((e) => {
        nn++;
        list.push({
          key: nn + "",
          label: <a onClick={handleClick}>{e}</a>,
        });
      });
      return list;
    } else {
      let cancelList = Mail.split("，"); //要取消的系统选项
      let sLchanged = sL.filter((item) => !cancelList.includes(item)); //取消后得到的系统选项
      sLchanged.forEach((e) => {
        nn++;
        list.push({
          key: nn + "",
          label: <a onClick={handleClick}>{e}</a>,
        });
      });

      return list;
    }
  };

  const GetBtn2 = (title, sL) => {
    return (
      <Col>
        <Dropdown
          menu={{
            items: items2(sL),
          }}
        >
          <Button className={styles.btn} type="text" key={title}>
            {title}
          </Button>
        </Dropdown>
      </Col>
    );
  };

  return (
    <div
      className={styles.headBar}
      style={{
        backgroundSize: systemInfos?.logo ? "100% auto" : "auto 100%",
      }}
    >
      <div className={styles.menuLeft}>
        {getBtn2("态势感知")}
        {GetBtn2("设备管理", [
          "设备列表",
          "视频九宫格",
          "设备日志",
          "解禁文件",
          "电子围栏",
        ])}
        {getBtn2("航线管理")}
        {GetBtn2("任务管理", ["定时任务", "三方任务"])}
        {/* {GetBtn2("任务记录", ["航行记录", "任务记录", "飞行统计"])} */}
        {/* {getBtn2("设备管理")} */}
        {/* {GetBtn2("设备管理", ["设备列表"])} */}
      </div>
      <div
        className={styles.headCenter}
        onClick={() => {
          ChangeFullScreen();
        }}
      >
        {systemInfos?.logo && (
          <span
            className={styles.logo}
            style={{
              background: `url(${systemInfos?.logo})`,
              backgroundSize: "100% 100%",
            }}
          ></span>
        )}
        <span className={styles.titleBox}>
          <div className={styles.title}>{systemInfos?.title}</div>
          <div className={styles.smallTitle}>{systemInfos?.engName}</div>
        </span>
        {/* <h2 className={styles.vintage}>中铁十七局集团AI无人机智能巡检与应急管理系统<span>1323</span></h2> */}
      </div>
      <div className={styles.menuRight}>
        {/* {getBtn('航行记录')}
            {getBtn('航拍照片')} */}
        {/* {GetBtn2("飞行数据", ["航行记录", "航拍照片", "航拍视频","任务归档"])} */}
        {/* {GetBtn2("飞行数据", ["航行记录", "航拍照片", "航拍视频"])} */}

        {GetBtn2("飞行记录", ["航行记录", "任务记录", "飞行统计"])}
        {/* {GetBtn2("数据管理", ["航拍照片", "航拍视频"])} */}

        {GetBtn2("数据管理", [
          "航拍照片",
          "航拍视频",
          "正射影像",
          "正射对比",
          "三维模型",
          "三维对比",

          // "视频拼接",
          //"地图建模",
          "地图管理",
          "建模任务",
        ])}

        {GetBtn2("AI识别", [
          "AI模型",
          "AI模型2",
          "AI模型3",
          "图像识别任务",
          "视频识别任务",
          // '报告生成任务',
          // "视频拼接",
          // "地图建模",
          "事件处理",
          "目标识别",
        ])}

        {GetBtn2("系统配置", [
          "组织管理",
          "用户管理",
          "系统日志",
          // "消息列表",
          "公告发布",
          // "图片对比",
        ])}
        {/* <div style={{ width: "107px" }}></div> */}
        {/* {getBtn2('事件处理')} */}
        <div className={styles.userInfoBox}>
          <MyBell></MyBell>
          <div className={styles.userInfo}> {UserButton()}</div>
        </div>
      </div>
      {/* <Button type="text"  onClick={handleClick} className={styles.btn}  key="系统管理">系统管理</Button> */}
    </div>
  );
};

export default TopMenu;
