import React, { useState, memo, useCallback } from 'react'
import { Card, Radio, Button, Space, Divider, message } from 'antd'
import { LayoutOutlined, SyncOutlined, SettingOutlined } from '@ant-design/icons'
import styles from './index.module.less'
import mapViewSynchronizer from '../../services/MapViewSynchronizer'

/**
 * 多屏对比配置面板组件
 * 提供布局选择、视图同步等配置选项
 */
const MultiScreenPanel = memo(({
  layout = '1x2',
  syncView = false,
  onLayoutChange,
  onSyncViewChange,
  onClose,
  className = ''
}) => {
  const [currentLayout, setCurrentLayout] = useState(layout)
  const [currentSyncView, setCurrentSyncView] = useState(syncView)

  // 布局选项配置
  const layoutOptions = [
    { label: '1×2 (左右)', value: '1x2', icon: '⬜⬜' },
    { label: '2×1 (上下)', value: '2x1', icon: '⬜\n⬜' },
    { label: '2×2 (四宫格)', value: '2x2', icon: '⬜⬜\n⬜⬜' },
    { label: '1×3 (三列)', value: '1x3', icon: '⬜⬜⬜' },
    { label: '3×1 (三行)', value: '3x1', icon: '⬜\n⬜\n⬜' }
  ]

  // 处理布局变化
  const handleLayoutChange = useCallback((e) => {
    try {
      const newLayout = e.target.value
      setCurrentLayout(newLayout)
      if (onLayoutChange) {
        onLayoutChange(newLayout)
      }
      message.success(`布局已切换为 ${layoutOptions.find(opt => opt.value === newLayout)?.label}`)
    } catch (error) {
      console.error('布局切换失败:', error)
      message.error('布局切换失败，请重试')
    }
  }, [onLayoutChange])

  // 处理视图同步变化
  const handleSyncViewChange = useCallback((e) => {
    try {
      const newSyncView = e.target.checked
      setCurrentSyncView(newSyncView)
      
      // 设置同步器的启用状态
      mapViewSynchronizer.setEnabled(newSyncView)
      
      if (onSyncViewChange) {
        onSyncViewChange(newSyncView)
      }
      message.success(newSyncView ? '已启用视图同步' : '已禁用视图同步')
    } catch (error) {
      console.error('视图同步设置失败:', error)
      message.error('视图同步设置失败，请重试')
    }
  }, [onSyncViewChange])

  // 处理关闭面板
  const handleClose = useCallback(() => {
    try {
      if (onClose) {
        onClose()
      }
    } catch (error) {
      console.error('关闭配置面板失败:', error)
    }
  }, [onClose])

  // 处理应用配置按钮的点击
  const handleApplyConfig = useCallback(() => {
    try {
      // 应用布局配置
      if (onLayoutChange) {
        onLayoutChange(currentLayout)
      }
      
      // 应用同步视图配置
      if (onSyncViewChange) {
        onSyncViewChange(currentSyncView)
      }
      
      // 设置同步器的启用状态
      mapViewSynchronizer.setEnabled(currentSyncView)
      
      message.success('配置已应用')
    } catch (error) {
      console.error('应用配置失败:', error)
      message.error('应用配置失败，请重试')
    }
  }, [currentLayout, currentSyncView, onLayoutChange, onSyncViewChange])

  return (
    <div className={`${styles.multiScreenPanel} ${className}`} role="dialog" aria-label="多屏对比配置面板">
      <Card
        title={
          <Space>
            <LayoutOutlined />
            多屏对比配置
          </Space>
        }
        size="small"
        className={styles.configCard}
        extra={
          <Button
            type="text"
            size="small"
            onClick={handleClose}
            className={styles.closeButton}
            aria-label="关闭配置面板"
            title="关闭配置面板"
          >
            ×
          </Button>
        }
      >
        {/* 布局选择 */}
        <div className={styles.configSection}>
          <h4>
            <LayoutOutlined style={{ marginRight: 8 }} />
            布局模式
          </h4>
          <Radio.Group
            value={currentLayout}
            onChange={handleLayoutChange}
            className={styles.layoutOptions}
          >
            {layoutOptions.map(option => (
              <Radio.Button
                key={option.value}
                value={option.value}
                className={styles.layoutOption}
              >
                <div className={styles.layoutPreview}>
                  <span className={styles.layoutIcon}>{option.icon}</span>
                  <span className={styles.layoutLabel}>{option.label}</span>
                </div>
              </Radio.Button>
            ))}
          </Radio.Group>
        </div>

        <Divider />

        {/* 视图同步 */}
        <div className={styles.configSection}>
          <h4>
            <SyncOutlined style={{ marginRight: 8 }} />
            视图同步
          </h4>
          <div className={styles.syncOptions}>
            <label className={styles.syncOption}>
              <input
                type="checkbox"
                checked={currentSyncView}
                onChange={handleSyncViewChange}
              />
              <span className={styles.syncLabel}>同步所有地图的缩放和平移</span>
            </label>
            <p className={styles.syncDescription}>
              启用后，操作任一地图的缩放或平移将同步到所有地图
            </p>
          </div>
        </div>

        <Divider />

        {/* 操作按钮 */}
        <div className={styles.configActions}>
          <Space>
            <Button
              type="primary"
              icon={<SettingOutlined />}
              size="small"
              onClick={handleApplyConfig}
            >
              应用配置
            </Button>
            <Button
              size="small"
              onClick={handleClose}
            >
              关闭面板
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  )
})

export default MultiScreenPanel
