.toggle-button-container {
  position: absolute;
  bottom: 80px;
  right: 20px;
  z-index: 1000;
  // 测试-需要
  // bottom: 35px;
  // right: 415px;
  // z-index: 1002;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 10px;

  .toggle-button {
    cursor: pointer;
    box-sizing: border-box;
    border-radius: 50%;
    border: 2px solid #ffffff;
    height: 40px;
    width: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
    padding: 0;
    overflow: hidden;
    z-index: 2;
    
    &:hover {
      box-shadow: 0 4px 10px rgba(0,0,0,0.3);
      transform: translateY(-2px);
    }
    
    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 6px rgba(0,0,0,0.2);
    }

    &.expanded {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24,144,255,0.2);
    }
    
    img {
      width: 48px;
      height: 48px;
      object-fit: cover;
    }
  }

  .map-list {
    position: absolute;
    bottom: 50px;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    padding: 8px;
    width: 200px;
    max-height: 300px;
    overflow-y: auto;
    animation: slideIn 0.3s ease;

    .map-item {
      display: flex;
      align-items: center;
      padding: 8px;
      cursor: pointer;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        background-color: #f5f5f5;
      }

      &.active {
        background-color: #e6f7ff;
        color: #1890ff;
      }

      img {
        width: 24px;
        height: 24px;
        margin-right: 8px;
        object-fit: cover;
        border-radius: 4px;
      }

      .map-name {
        flex: 1;
        font-size: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #555;
      }
    }
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}