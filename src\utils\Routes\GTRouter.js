import { getGuid } from "@/utils/helper";


// import LargeScreenDisplay from "@/pages/GT/DZZH/Pages/HomePage";
import LargeScreenDisplay from "@/pages/GT/DZJC/index";
import IntelligentFly from "@/pages/GT/DZZH/Pages/RecordsPage";
import MonitorDisposal from "@/pages/GT/DZZH/Pages/MonitorDisposal";
import IntelligentFly2 from "@/pages/GT/GDBH/Pages/IntelligentFly";
import RecordsPage from "@/pages/GT/components/RecordsPage";
import DetailPage from "@/pages/GT/components/RecordsDetailPage";
import TempLandPage from '@/pages/GT/LSYD/Pages/TempLandPage';
import TaskInspection from '@/pages/GT/LSYD/Pages/TaskInspection';
import AIAnalysis from '@/pages/GT/LSYD/Pages/AIAnalysis';
import OnePicPage from '@/pages/GT/ZZCH/Pages/OnePicPage';
import OneMap from '@/pages/GT/YZT/pages/Map';
import IntelligentInspection from '@/pages/GT/ZZCH/Pages/IntelligentInspection';
import TaskManagement from "@/pages/GT/ZZCH/Pages/TaskManagement";
import TargetRecognition from "@/pages/GT/ZZCH/Pages/TaskManagement/Pages/TargetRecognition";
import TargetRecognition2 from "@/pages/GT/ZZCH/Pages/TaskManagement/Pages/TargetRecognition2";
import VideoManagement from '@/pages/GT/ZZCH/Pages/VideoManagement';
import Orthophoto from '@/pages/GT/ZZCH/Pages/VideoManagement/Pages/2DOrthophoto';
import ThreeDModel from "@/pages/GT/ZZCH/Pages/VideoManagement/Pages/3DModel";
import MapDataDirectory from "@/pages/GT/YWGL/Pages/MapManagement";
import MapBaseConfig from "@/pages/GT/YWGL/Pages/MapBaseConfig";
import BaseMapConfig from "@/pages/GT/YWGL/Pages/BaseMapConfig";
import MediaDataPage from "@/pages/DJI/MediaDataPage/index";
import MediaDataPage2 from "@/pages/DJI/MediaDataPage/index2";
import DataAcquisition from "@/pages/GT/DZJC/pages/dataAcquisition";
import DisasterAnalysis from "@/pages/GT/DZJC/pages/disasterAnalysis";
import DataManagement from "@/pages/GT/DZJC/pages/dataManagement";
export const GTRouter = [
  {
    title: "大屏展示",
    key: "/dj/tsgz",
    // children: <LargeScreenDisplay key={getGuid()} />,
    children: <LargeScreenDisplay key={getGuid()} />,

  },
  {
    title: "智能巡飞",
    key: "/gt/DZZH/znxf",
    children: <IntelligentFly key={getGuid()} />,
  },
  {
    title: "巡飞记录",
    key: "/gt/DZZH/znxf/xjjl",
    children: <RecordsPage type='DZZH' />,
  },
  {
    title: "巡检详情",
    key: "/gt/gy/znxf/xjxq",
    children: <DetailPage key={getGuid()} />,
  },
  {
    title: "临时用地管理",
    key: "/dj/tsgz",
    children: <TempLandPage key={getGuid()} />,
  },
  {
    title: "任务巡检",
    key: "/gt/LSYD/rwxj",
    children: <TaskInspection key={getGuid()} />,
  },
  {
    title: "巡检记录",
    key: "/gt/LSYD/rwxj/xjjl",
    children: <RecordsPage type='LSYD' />,
  },
  // {
  //   title: "AI分析",
  //   key: "/dj/tsgz",
  //   children: <AIAnalysis key={getGuid()} />,
  // },
  {
    title: "一张图",
    key: "/dj/zzch/yzt",
    children: <OneMap key={getGuid()} />,
  },
  {
    title: "智能巡检",
    key: "/gt/ZZCH/znxj",
    children: <IntelligentInspection key={getGuid()} />,
  },
  {
    title: "巡检数据",
    key: "/gt/zzch/znxj/xjsj",
    children: <RecordsPage type='ZZCH' />,
  },
  {
    title: "素材管理",
    key: "/gt/ZZCH/scgl",
    children: <VideoManagement key={getGuid()} />,
  },
  {
    title: "二维正射图",
    key: "/gt/ZZCH/scgl/Orthophoto",
    children: <Orthophoto key={getGuid()} />,
  },
  {
    title: "三维模型图",
    key: "/dj/tsgz",
    children: <ThreeDModel key={getGuid()} />,
  },
  {
    title: "任务管理",
    key: "/gt/ZZCH/rwgl",
    children: <TaskManagement key={getGuid()} />,
  },
  {
    title: "目标识别",
    key: "/gt/ZZCH/rwgl/mbsb",
    children: <TargetRecognition key={getGuid()} />,
  },
  {
    title: "识别详情",
    key: "/gt/ZZCH/rwgl/sbxq",
    children: <TargetRecognition2 key={getGuid()} />,
  },
  {
    title: "地图基本设置",
    key: "/dj/tsgz",
    children: <MapBaseConfig key={getGuid()} />,
  },
  {
    title: "地图底图配置",
    key: "/dj/tsgz",
    children: <BaseMapConfig key={getGuid()} />,
  },
  {
    title: "地图数据管理",
    key: "/dj/tsgz",
    children: <MapDataDirectory key={getGuid()} />,
  },
  {
    title: "一张图资源管理系统",
    key: "/gt/yzt",
    children: <OneMap key={getGuid()} />,
  },
  {
    title: "航拍图片",
    key: "/dj/scgl/zzch/MediaDataPage#",
    children: <MediaDataPage key={getGuid()} doNotShowLastButton={true} />,
  },
  {
    title: "航拍影像",
    key: "/dj/scgl/zzch/MediaDataPage2#",
    children: <MediaDataPage2 key={getGuid()} doNotShowLastButton={true} />,
  },
  {
    title: "数据采集",
    key: "/gt/ZZCH/sjcj",
    children: <DataAcquisition key={getGuid()} />,
  },
  {
    title: "地灾分析",
    key: "/gt/ZZCH/dzfx",
    children: <DisasterAnalysis key={getGuid()} />,
  },{
    title: "数据管理",
    key: "/gt/ZZCH/sjgl",
    children: <DataManagement key={getGuid()} />,
  },
  {
    title: "监测处置",
    key: "/gt/ZZCH/jdcz",
    children: <MonitorDisposal key={getGuid()} />,
  }
  

  




];
