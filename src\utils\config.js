// src/utils/config.js
import { useConfigStore } from "../stores/configStore";
  const parsedUrl = new URL(window.location.href);

// 获取 MQTT WebSocket URL
export const getMqttWsUrl = () => {
  if (parsedUrl.protocol === "https:") {
    return useConfigStore.getState().config.mqtt.wssUrl;
  }
  return useConfigStore.getState().config.mqtt.wsUrl;
};

// 获取 MQTT TCP 地址
export const getMqttTcpAddress = () => {
  return useConfigStore.getState().config.mqtt.tcpAddress;
};

export const getRtmpAddr = () => {
  return useConfigStore.getState().config.rtmp.rtmpAddr;
};

export const getRtmpAIUrl = () => {
  return useConfigStore.getState().config.rtmp.aiUrl;
};

// 获取 RTMP WebRTC URL
export const getRtmpWebrtcUrl = () => {
  if (parsedUrl.protocol === "https:") {
    return useConfigStore.getState().config.rtmp.webrtcUrlSsl;
  }
  return useConfigStore.getState().config.rtmp.webrtcUrl;
};

// 获取 RTMP HTTP URL
export const getRtmpHttpUrl = () => {
  return useConfigStore.getState().config.rtmp.httpUrl;
};

// 获取 RTMP 分享 URL
export const getRtmpShareUrl = (sn) => {
  return `${useConfigStore.getState().config.rtmp.shareUrl}${sn}`;
};

// 获取 OSS 配置
export const getOssConfig = () => {
  const { oss } = useConfigStore.getState().config;
  return {
    url: oss.url,
    bucket: oss.bucket,
  };
};
