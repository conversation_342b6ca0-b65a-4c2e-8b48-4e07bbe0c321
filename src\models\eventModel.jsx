
import {  isEmpty } from "@/utils/utils";
import { useState, useRef } from "react";
import { GetMqttClient } from "@/utils/websocket";

export default function eventModel() {

    const mqttC=useRef({})
    const [hms,setHms]=useState({})
    const drcStatus=useRef({})
    const joyReason=useRef('正常飞行')
    const EventMqttConn = (sn) => {
        if (!isEmpty(mqttC.current)) {
            mqttC.current.end();
            setHms({});
            drcStatus.current={}
        }
      
        mqttC.current = GetMqttClient()
        mqttC.current.on("message", (topic, message) => {
            updateVal(topic,message);
       });
       mqttC.current.unsubscribe('#')
        mqttC.current.subscribe("thing/product/"+sn+"/events");
      
    }
    const joyList=["遥控器失联","低电量返航","低电量降落","靠近限飞区","遥控器夺权（例如：触发了返航，B控夺权）"]

    const updateVal=(t1,m1)=>{
        const xx=JSON.parse( m1);
        console.log('hms',xx);
        if (xx.method=='hms') {
            setHms(xx.data)
        }
        if (xx.method=='drc_status_notify') {
            drcStatus.current=xx.data;
        }
        if (xx.method=='drc_status_notify') {
            console.log('drc','mode_code_reason_drc_status_notify',joyList[xx.data.reason]);
                
            joyReason.current=joyList[xx.data.reason];
        }
        if (xx.method=='joystick_invalid_notify') {
            console.log('drc','mode_code_reason_joystick_invalid_notify',joyList[xx.data.reason]);
                
            joyReason.current=joyList[xx.data.reason];
        }
    }

   
    
    return {hms,drcStatus,joyReason, EventMqttConn };
};