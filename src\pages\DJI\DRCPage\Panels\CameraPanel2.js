import camera2 from "@/assets/drcImgs/camera2.png";
import camera3 from "@/assets/drcImgs/camera3.png";
import video2 from "@/assets/drcImgs/video2.png";
import video3 from "@/assets/drcImgs/video3.png";
import { isEmpty } from "@/utils/utils";
import { useModel } from "umi";
import {getGrayStyle} from './helper';
import { color } from "echarts";
import { getGuid } from "@/utils/helper";
import dayjs from "dayjs";
import { Popover,ConfigProvider } from 'antd';
import whiteHotImg from '@/assets/images/white_hot.png'
import blackHotImg from '@/assets/images/black_hot.png'
import redHotImg from '@/assets/images/red_hot.png'
import iceFire from '@/assets/images/ice_fire.png'
import color1 from '@/assets/images/color_1.png'
import color2 from '@/assets/images/color_2.png'
import greenHot from '@/assets/images/green_hot.png'
import ironBow from '@/assets/images/iron_bow_1.png'
import rainBow from '@/assets/images/rain_bow.png'
import rain from '@/assets/images/rain.png'

const CameraPanel2=()=>{
    const w1=150
    const h1=50
    const {fj}=useModel('droneModel');
    const {DoCMD,DoCMD2}=useModel('cmdModel');
    const  device = JSON.parse(localStorage.getItem('device'));
    const {cameraJT,setCameraJT}=useModel('rtmpModel');
      const { fjVideo } = useModel("stateModel");

    const colorParams = [
        {value:0,name:"白热",img:whiteHotImg},
        {value:1,name:"黑热",img:blackHotImg},
        {value:2,name:"描红",img:redHotImg},
        {value:3,name:"医疗",img:greenHot},
        {value:5,name:"彩虹1",img:rainBow},
        {value:6,name:"铁红",img:ironBow},
        {value:8,name:"北极",img:iceFire},
        {value:11,name:"熔岩",img:color1},
        {value:12,name:"热铁",img:color2},
        {value:13,name:"彩虹2",img:rain},
    ]
    const content = (
        <div  style={{width:130,display:'flex',flexWrap:'wrap',justifyContent:"space-between"}}>
        {colorParams.map((item)=>{
            return (
                <div style={{cursor:'pointer'}} onClick={()=>{CameraIRType(item.value)}}>
                    <img src={item.img} alt="" style={{width:60}}/>
                    <div style={{width:60,textAlign:'center'}}>
                        {item.name}
                    </div>
                </div>
            )
        })}
      </div>
      );

    const CameraJT = (z,) => {
        const data = {
            "video_id": `${device.SN2}/${device.Camera2}/normal-0`,
            "video_type": z
        }
        DoCMD(device.SN, "live_lens_change", data)
        setCameraJT(z);

        if(z=='ir'){
           setTimeout(CameraIRType, 1000);
        }
       // goPost("/api/v1/Camera/Control?sn=" + sn + "&m1=" + "live_lens_change", data)
    }

    const CameraIRType=(value = 0)=> {
        if(fjVideo.video_type != "ir"){
            const data = {
                "video_id": `${device.SN2}/${device.Camera2}/normal-0`,
                "video_type": "ir"
            }
            DoCMD(device.SN, "live_lens_change", data)
            setCameraJT("ir");
        }
        let data={}
        data[device.Camera2]={"thermal_current_palette_style": value}
        let s = {}
        s.bid = getGuid();
        s.tid = getGuid();
       // s.gateway = sn
        s.data = data
        s.timestamp = dayjs().valueOf();
        DoCMD2(`thing/product/${device.SN}/property/set`, s)  
    }


    const changeCamera=(v)=>{
       const data= {
            "camera_mode": v,
            "payload_index": device.Camera2,
       }
       DoCMD(device.SN,'camera_mode_switch',data);
    }

    const ifHaveIR = () => {
        if (device.Model2.includes('T')) {
            return true;
        }
        return false;
    }

    if(isEmpty(fj)) return;
    const fSty={userSelect:'none', opacity:0.6,cursor:'pointer',  fontFamily:'MiSan', textAlign:'center',fontSize:14.0,color:'white',fontWeight:'bold',paddingTop:8.0}

     return <div style={{ position:'absolute', top:80,right:40, height:h1,width:w1,zIndex:1010}}>
        <div style={{...getGrayStyle(54,32,0,0,'3px 0px 0px 3px'),...fSty}} onClick={()=>CameraJT('wide')}>广角</div>
        <div style={{...getGrayStyle(54,32,0,56,'0px 0px 0px 0px'),...fSty}} onClick={()=>CameraJT('zoom')}>变焦</div>
        {ifHaveIR()?
<ConfigProvider
    theme={{
        components:{
            Popover:{
                titleMinWidth:130
            }
        }
    }}
>
          <Popover content={content} title="红外调色盘">
           <div style={{...getGrayStyle(54,32,0,112,'0px 3px 3px 0px'),...fSty}} onClick={()=>CameraJT('ir')}>红外</div>
        </Popover>
        </ConfigProvider>
        :null}
     </div> 
    
    
}

export default CameraPanel2;