import { HGet2, HPost2 } from "@/utils/request"
import {  IsAQQ } from '@/pages/Maps/helper';
import { message } from "antd";
import { Get2 } from "@/services/general";


const TakeOff=(sn)=>{
    HGet2("/api/v1/FlyTo/TakeOff?sn="+sn)
}

export const WayLinePause=(sn)=>{
    HGet2("/api/v1/WayLine/Pause?sn="+sn)
}

export const WayLineRecory=(sn)=>{
    HGet2("/api/v1/WayLine/Recory?sn="+sn)
}

export const ToHome=(sn)=>{
    HGet2("/api/v1/WayLine/ToHome?sn="+sn)
}

export const cancelToHome = (sn)=>{
    // 取消返航
    Get2("/api/v1/WayLine/ToHomeCancel?sn="+sn);
}

const DeBug=async(cmd)=>{
    HGet2("/api/v1/Debug/CMD?cmd="+cmd)
}


export const CheckIfCanFly=async (sn)=>{
        
    const pb1=await Get2('/api/v1/Device/GetIfOnline?sn='+sn);
    
    if(!pb1){
        message.error('机场未在线，无法飞行！');
        return false;
    }
    const a1=await Get2('/api/v1/Device/GetJcOsdData1?sn='+sn);
    if(a1.mode_code!=0){
        message.info('请等待机场空闲时执行飞行任务！');
        return false;
    }
    return true;
}


export const CameraDrag = (SendCMD,x, y) => {
    const data = {
        "locked": false,
        "payload_index": "80-0-0",
        "pitch_speed": x,
        "yaw_speed": y
    }
    SendCMD("camera_screen_drag",data);
    // console.log("@@@@date",Date.now(),"Date:",new Date())
   // goPost("/api/v1/Camera/Control?sn=" + sn + "&m1=" + "camera_screen_drag", data)
}

export const CamerAIM = (SendCMD,x, y) => {
    const device = JSON.parse(localStorage.getItem('device'))
    const data = {
        "camera_type": "wide",
        "locked": false,
        "payload_index": device.Camera2,
        "x": x,
        "y": y
    }
    console.log('CamerAIM',data);
    SendCMD("camera_aim",data);
   // goPost("/api/v1/Camera/Control?sn=" + sn + "&m1=" + "camera_screen_drag", data)
}


export const CameraZoom = (SendCMD, z) => {

    const key='CameraZoom'
    const val=z
    const x1=localStorage.getItem(key)
    if(x1===val) {
        console.log('CameraZoom',val,x1);
        return;}
    localStorage.setItem(key,val)        
    if(z<2)z=2;
    if(z>200)z=200;
    const data = {
        "camera_type": "zoom",
        "payload_index": "80-0-0",
        "zoom_factor": z
    }
    SendCMD("camera_focal_length_set", data)
    //goPost("/api/v1/Camera/Control?sn=" + sn + "&m1=" + "camera_focal_length_set", data)
}

export const CameraIRZoom = (SendCMD, z) => {
    if(z<2)z=2;
    if(z>20)z=20;
    const data = {
        "camera_type": "ir",
        "payload_index": "80-0-0",
        "zoom_factor": z
    }
    SendCMD("camera_focal_length_set", data)
   // goPost("/api/v1/Camera/Control?sn=" + sn + "&m1=" + "camera_focal_length_set", data)
}

export const CameraSave=(SendCMD)=>{
    const data = {
        "payload_index": "80-0-0",
        "photo_storage_settings": [
          'current',
        ]
    }
    SendCMD("photo_storage_set", data)
}



export const CameraJT = (SendCMD, z) => {
    const data = {
        "video_id": "1581F6Q8D23CT00A5N49/80-0-0/normal-0",
        "video_type": z
    }
    SendCMD("live_lens_change", data)
   // goPost("/api/v1/Camera/Control?sn=" + sn + "&m1=" + "live_lens_change", data)
}

export const CameraPai=(SendCMD,fj)=>{
    console.log('CameraPai',fj.cameras[0].payload_index)
    // if(!fj.cameras[0].photo_storage_settings.includes("current")){
    //     //console.log('CameraPai',fj.cameras[0])
    //     CameraSave(SendCMD);
    // }
    const data={"payload_index": fj.cameras[0]}
    SendCMD("camera_photo_take", data)
}


export const CameraRefrush=(SendCMD,z)=>{
   
    const data={"payload_index": "80-0-0","reset_mode": z}
    SendCMD("gimbal_reset", data)
}


export const FlyTheLine = (d1,pL,height) => {
    const data = {
      "PList":pL,
      "SN":d1.SN,
      "Lat":d1.Lat,
      "Lng":d1.Lng,
      "Height":height
    }
    HPost2("/api/v1/WayLine/FlyTo", data)
}

export const SaveTheLine = (d1,pL,height) => {
    const data = {
      "PList":pL,
      "SN":d1.SN,
      "Lat":d1.Lat,
      "Lng":d1.Lng,
      "Height":height
    }
    
    HPost2("/api/v1/WayLine/Upload2", data)
}


export const FlyToXuanTing = (sn, lat, lng, h1) => {
    
    const xx = IsAQQ(lat, lng)
    if (!xx) {
      message.info("该点不在安全区，禁止手动飞行！");
      return;
    }
   // HGet2("/api/v1/FlyTo/FlyToPoint?sn=" + sn + "&lat=" + lat + "&lng=" + lng + "&h1=" + h1)
    HGet2("/api/v1/WayLine/XuanTing?sn=" + sn + "&lat=" +lat+"&lng=" +lng+"&h1=" +h1);
}

export const FlyToPoint = (sn, lat, lng, h1) => {
    
    const xx = IsAQQ(lat, lng)
    if (!xx) {
      message.info("该点不在安全区，禁止手动飞行！");
      return;
    }
   HGet2("/api/v1/FlyTo/FlyToPoint?sn=" + sn + "&lat=" + lat + "&lng=" + lng + "&h1=" + h1)
   //  HGet2("/api/v1/WayLine/XuanTing?sn=" + sn + "&lat=" +lat+"&lng=" +lng+"&h1=" +h1);
}


export const FlyToPoint2 = (sn, lat, lng, h1) => {
    
    const xx = IsAQQ(lat, lng)
    if (!xx) {
      message.info("该点不在安全区，禁止手动飞行！");
      return;
    }
   HGet2("/api/v1/WayLine/FlyToPoint?sn=" + sn + "&lat=" + lat + "&lng=" + lng + "&h1=" + h1)
   //  HGet2("/api/v1/WayLine/XuanTing?sn=" + sn + "&lat=" +lat+"&lng=" +lng+"&h1=" +h1);
}



export const changeCamera=(device,v)=>{
       
    const data= {
         "camera_mode": v,
         "payload_index": device.Camera2,
    }
    DoCMD(device.SN,'camera_mode_switch',data);

    if(v==1){
         VideoSave();
    }else{
         CameraSave();
    }
 }

 export const photoCamera=(device)=>{
     const data={"payload_index": device.Camera2}
     DoCMD(device.SN,"camera_photo_take", data)
  }

  export const VideoStart=(device)=>{
     const data={"payload_index": device.Camera2}
     DoCMD(device.SN,"camera_recording_start", data)
  }

  export const VideoStop=(device)=>{
     const data={"payload_index": device.Camera2}
     DoCMD(device.SN,"camera_recording_stop", data)
  }

  export const VideoClick=(device)=>{
     if(fj.data.cameras[0].recording_state==1){
         VideoStop(device);
         setIfVideo(false);
     }else{
         VideoStart(device);
         setIfVideo(true);
     }
  }

  const CameraSave2=()=>{
     const data = {
         "payload_index": device.Camera2,
         "photo_storage_settings": [
            'current',
         ]
     }
     DoCMD(device.SN,"photo_storage_set", data)
 }

 const VideoSave=()=>{
     const data = {
         "payload_index": device.Camera2,
         "video_storage_settings": [
           'current',
         ]
     }
     DoCMD(device.SN,"video_storage_set", data)
 }