import { useState, useEffect, useRef } from 'react';
import { message, Slider, Input, InputNumber, Tooltip } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import { Cesium } from "umi";
import { getBodyH, isEmpty } from '@/utils/utils';
import { axiosApi } from '@/services/general';
import { useModel } from 'umi';
import { queryPage2 } from '@/utils/MyRoute';
import useInspectionRouteHooks from './hooks/InspectionRouteHooks';
import MapControl from '@/hooks/mapControl';
import WaypointList from './components/WaypointList';
import TopToolbar from './components/TopToolbar';
import WaypointInfo from './components/WaypointInfo';
import ActionListInfo from './components/ActionListInfo';
import UseHelp from './components/UseHelp';
import KeyboardTip from './components/KeyboardTip';
import KeyboardTip2 from './components/KeyboardTip2';
import WayLineSeting from './components/WayLineSeting';
import * as turf from '@turf/turf'

const WayLine3DPage = ({ wayLineData }) => {
    const { setModal, setOpen, setPage, lastPage } = useModel('pageModel')
    let [viewerData, setviewerData] = useState(null)
    let {
        add_all_function,
        delete_function,
        item_height_change_function,
        SwitchWaypoint,
        SwitchHanger,
        PTZ_headingPitchRoll_Change,
        EditseleteIndex,
        addNotFlyZone
    } = useInspectionRouteHooks(
        computePitch,
        computeZoomFactor,
        waylinePush,
        PListItemHeightChange,
        PListItemPositionChange,
        PListItemPositionDelete,
        seleteIndexChange,
        setviewerData,
        getGlobalHeadingAngle,
        getIsParallelLine);
    // 航线信息
    let [wayline, setWayline] = useState({
        mission_id: 0,
        distance: 0,
        author: '',//作者
        create_time: Date.now(),//创建时间(时间)
        update_time: Date.now(),//更新时间(时间)
        title: '新建航点航线',//航线名称
        mission_config_vo: {
            fly_to_wayline_mode: 'safely',//飞向首航点模式（safely：安全模式pointToPoint：倾斜飞行模式）
            finish_action: 'goHome',//航线结束动作
            exit_on_rc_lost: 'executeLostAction',//失控是否继续执行航线
            execute_rc_lost_action: 'goBack',//goBack：返航。飞行器从失控位置飞向起飞点landing：降落。飞行器从失控位置原地降落hover：悬停。飞行器从失控位置悬停
            take_off_security_height: 70,//安全起飞高度
            global_transitional_speed: 10,//全局航线过渡速度
            global_rth_height: 70,//全局返航高度
            // ellipsoid_height: 120,//全局航线高度
            drone_enum_value: 67,//飞行器机型主类型
            drone_sub_enum_value: 1,//飞行器机型子类型
            payload_enum_value: 53,//负载机型主类型
            payload_position_index: 0,//负载挂载位置
        },
        waypoint_folder_vo: {
            auto_flight_speed: 10,   //全局航线飞行速度
            global_height: 120,   //全局航线高度，相对起飞点高度
            waypoint_heading_mode: "followWayline",    //飞行器航偏角模式飞行器偏航角模式followWayline：沿航线方向。飞行器机头沿着航线方向飞至下一航点manually：手动控制。飞行器在飞至下一航点的过程中，用户可以手动控制飞行器机头朝向fixed：锁定当前偏航角。飞行器机头保持执行完航点动作后的飞行器偏航角飞至下一航点
            template_type: "waypoint",   //预定义模板类型
            template_id: 1,    //模板ID ：template.kml和waylines.wpml文件中，将使用该id将模板与所生成的可执行航线进行关联。
            execute_height_mode: 'WGS84',   //执行高度模式WGS84：椭球高模式，即以海平面为基准 relativeToStartPoint：相对起飞点高度模式，即以起飞点的海拔高度为基准
            global_waypoint_turn_mode: "toPointAndStopWithDiscontinuityCurvature",    //全局航点类型(全局航点转弯模式）固定传：toPointAndStopWithDiscontinuityCurvature
            global_use_straight_line: true,   //全局航段轨迹是否尽量贴合直线true:航段轨迹尽量贴合两点连线false:航段轨迹全程为曲线
            waypoint_heading_path_mode: "followBadArc",    //飞行器偏航角转动方向clockwise：顺时针旋转飞行器偏航角counterClockwise：逆时针旋转飞行器偏航角followBadArc：沿最短路径旋转飞行器偏航角
            gimbal_pitch_mode: "usePointSetting",    //全云台俯仰角控制模式manual：手动控制usePointSetting：使用各航点的设置
            waypoint_heading_angle: 0,
            finish_action: 'goHome',//完成动作
            height_mode: 'WGS84',
            coordinate_mode: 'WGS84',
            takeOffPointLat: 0,
            takeOffPointLng: 0,
            takeOffPointHeight: 0,
            global_payload_lens_index: ['zoom', 'wide'],//全局存储方式
        },//航线模版信息
        mission_point_list: [],//每个航点的信息，包括动作、偏航角，速度高度，经纬度等信息

        PList: [],
        WaylineName: '航线名称',
        WaylineType: '2',
        WanLineId: '',
        SN: null,
        GlobalSpeed: 10,
        GlobalHeadingAngle: '',
        IsParallelLine:true
    })
    // 航线信息
    let waylineCopy = useRef({
        mission_id: 0,
        distance: 0,
        author: '',//作者
        create_time: Date.now(),//创建时间(时间)
        update_time: Date.now(),//更新时间(时间)
        title: '新建航点航线',//航线名称
        mission_config_vo: {
            fly_to_wayline_mode: 'safely',//飞向首航点模式（safely：安全模式pointToPoint：倾斜飞行模式）
            finish_action: 'goHome',//航线结束动作
            exit_on_rc_lost: 'executeLostAction',//失控是否继续执行航线
            execute_rc_lost_action: 'goBack',//goBack：返航。飞行器从失控位置飞向起飞点landing：降落。飞行器从失控位置原地降落hover：悬停。飞行器从失控位置悬停
            take_off_security_height: 70,//安全起飞高度
            global_transitional_speed: 10,//全局航线过渡速度
            global_rth_height: 70,//全局返航高度
            // ellipsoid_height: 120,//全局航线高度
            drone_enum_value: 67,//飞行器机型主类型
            drone_sub_enum_value: 1,//飞行器机型子类型
            payload_enum_value: 53,//负载机型主类型
            payload_position_index: 0,//负载挂载位置
        },
        waypoint_folder_vo: {
            auto_flight_speed: 10,   //全局航线飞行速度
            global_height: 120,   //全局航线高度，相对起飞点高度
            waypoint_heading_mode: "followWayline",    //飞行器航偏角模式飞行器偏航角模式followWayline：沿航线方向。飞行器机头沿着航线方向飞至下一航点manually：手动控制。飞行器在飞至下一航点的过程中，用户可以手动控制飞行器机头朝向fixed：锁定当前偏航角。飞行器机头保持执行完航点动作后的飞行器偏航角飞至下一航点
            template_type: "waypoint",   //预定义模板类型
            template_id: 1,    //模板ID ：template.kml和waylines.wpml文件中，将使用该id将模板与所生成的可执行航线进行关联。
            execute_height_mode: 'WGS84',   //执行高度模式WGS84：椭球高模式，即以海平面为基准 relativeToStartPoint：相对起飞点高度模式，即以起飞点的海拔高度为基准
            global_waypoint_turn_mode: "toPointAndStopWithDiscontinuityCurvature",    //全局航点类型(全局航点转弯模式）固定传：toPointAndStopWithDiscontinuityCurvature
            global_use_straight_line: true,   //全局航段轨迹是否尽量贴合直线true:航段轨迹尽量贴合两点连线false:航段轨迹全程为曲线
            waypoint_heading_path_mode: "followBadArc",    //飞行器偏航角转动方向clockwise：顺时针旋转飞行器偏航角counterClockwise：逆时针旋转飞行器偏航角followBadArc：沿最短路径旋转飞行器偏航角
            gimbal_pitch_mode: "usePointSetting",    //全云台俯仰角控制模式manual：手动控制usePointSetting：使用各航点的设置
            waypoint_heading_angle: 0,
            finish_action: 'goHome',//完成动作
            height_mode: 'WGS84',
            coordinate_mode: 'WGS84',
            takeOffPointLat: 0,
            takeOffPointLng: 0,
            takeOffPointHeight: 0,
            global_payload_lens_index: ['zoom', 'wide'],//全局存储方式
        },//航线模版信息
        mission_point_list: [],//每个航点的信息，包括动作、偏航角，速度高度，经纬度等信息

        PList: [],
        WaylineName: '航线名称',
        WaylineType: '2',
        WanLineId: '',
        SN: null,
        GlobalSpeed: 10,
        GlobalHeadingAngle: '',
        IsParallelLine: true
    })
    // 选中的航点索引
    const [seleteIndex, setSeleteIndex] = useState(null);

    // 页面载入
    useEffect(() => {
        if (wayLineData) {
            setWaylineCopy({
                ...JSON.parse(wayLineData.Remarks)
            })
            add_all_function(JSON.parse(wayLineData.Remarks).PList)
        }
    }, []);
    // 添加航点
    function waylinePush(index, latitude, longitude, height, globeHeight) {
        waylineCopy.current.PList.splice(index + 1, 0, {
            Index: waylineCopy.current.PList.length,
            Lat: latitude,
            Lng: longitude,
            Height: height,
            global_height: 100,
            ground_height: globeHeight,
            Action: '',
            ActionList: [],
            PName: `航点`,
            PType: 'Waypoint',
            Speed: waylineCopy.current.GlobalSpeed,
            IfStop: false,
        })
        waylineCopy.current.PList.forEach((item, index) => {
            item.PName = `航点${index + 1}`
            item.Index = index
        })
        setWayline({ ...waylineCopy.current })
    }
    // 单个航点高度改变
    function PListItemHeightChange(index, height) {
        waylineCopy.current.PList[index].Height = height
        waylineCopy.current.PList[index].global_height = height - waylineCopy.current.PList[index].ground_height
        setWayline({ ...waylineCopy.current, })
    }
    // 单个航点位置改变
    function PListItemPositionChange(index, Lat, Lng) {
        waylineCopy.current.PList[index].Lat = Lat
        waylineCopy.current.PList[index].Lng = Lng
        setWayline({ ...waylineCopy.current, })
    }
    // 删除某个航点
    function PListItemPositionDelete(index) {
        waylineCopy.current.PList.splice(index, 1)
        waylineCopy.current.PList.forEach((item, ind) => {
            item.Index = ind
            item.PName = `航点${ind + 1}`
        })
        setWayline({ ...waylineCopy.current })
        if (waylineCopy.current.PList.length === 0) {
            seleteIndexChange(null)
        } else if (waylineCopy.current.PList.length === 1) {
            seleteIndexChange(0)
        } else if (waylineCopy.current.PList.length > 1) {
            if (index === 0) {
                seleteIndexChange(0)
            } else if (index < waylineCopy.current.PList.length - 1) {
                seleteIndexChange(index)
            } else if (index >= waylineCopy.current.PList.length - 1) {
                seleteIndexChange(waylineCopy.current.PList.length - 1)
            }
        }
    }
    // seleteIndex改变
    function seleteIndexChange(index) {
        setSeleteIndex(index)
        SwitchWaypoint(index)
        EditseleteIndex(index)
        if (index === null) {
            return
        }
        if (waylineCopy.current.PList[index].PType === '0') {
            PTZ_headingPitchRoll_Change({
                heading: Cesium.Math.toRadians(waylineCopy.current.PList[index].ActionList[0].ActionParam[0]),
                pitch: Cesium.Math.toRadians(waylineCopy.current.PList[index].ActionList[1].ActionParam[1]),
                roll: 0
            })
        } else if (waylineCopy.current.PList[index].PType === '3') {
            PTZ_headingPitchRoll_Change({
                heading: Cesium.Math.toRadians(waylineCopy.current.PList[index].ActionList[0].ActionParam[1]),
                pitch: Cesium.Math.toRadians(waylineCopy.current.PList[index].ActionList[0].ActionParam[0]),
                roll: 0
            })
        }
    }
    // 保存航线
    function onSave() {
        if (wayline.SN === "") {
            message.error(`请选择机库`);
            return
        }
        if (wayline.WaylineName === "") {
            message.error(`请输入航线名称`);
            return
        }
        console.log({
            SN: wayline.SN,
            PList: wayline.PList,
            WaylineName: wayline.WaylineName,
            WaylineType: wayline.WaylineType,
            WanLineId: wayline.WanLineId
        });

        axiosApi('/api/v1/WayLine/Create', "POST", {
            SN: wayline.SN,
            PList: wayline.PList,
            WaylineName: wayline.WaylineName,
            WaylineType: wayline.WaylineType,
            WanLineId: wayline.WanLineId,
            GlobalSpeed: wayline.GlobalSpeed,
            GlobalHeadingAngle: wayline.GlobalHeadingAngle
        }).then((res) => {
            if (res.code === 1) {
                message.success(`航线${wayline.WanLineId ? '修改' : '创建'}成功`);
                setPage(queryPage2('航线列表'))
            }
        })
    }
    // 计算方向角
    function computeHeading(index) {
        let angle = 0
        if (index !== 0) {
            let coordItem = [[waylineCopy.current.PList[index - 1].Lng, waylineCopy.current.PList[index - 1].Lat], [waylineCopy.current.PList[index].Lng, waylineCopy.current.PList[index].Lat]]
            //通过 turf 计算两个点的方向向量
            angle = turf.rhumbBearing(coordItem[0], coordItem[1])
        }
        return angle
    }
    // 计算变焦倍数
    function computeZoomFactor(index) {
        let zoom = 1
        if ((index !== null) && waylineCopy.current.PList[index]) {
            waylineCopy.current.PList[index].ActionList.forEach((item, index) => {
                if ((item.ActionName === '3') && item.ActionParam[0] === 'zoom') {
                    zoom = item.ActionParam[1]
                }
            })
        }
        return zoom
    }
    // 计算到某航点为止的pitch
    function computePitch(Index) {
        let pitch = 0
        if ((Index !== null) && waylineCopy.current.PList) {
            waylineCopy.current.PList.forEach((item, index) => {
                if (index <= Index) {
                    item.ActionList.length && item.ActionList.forEach((ite, ind) => {
                        if ((ite.ActionName === '0')) {
                            pitch = ite.ActionParam[1]
                        } else if ((ite.ActionName === '3')) {
                            pitch = ite.ActionParam[0]
                        }
                    })
                }
            })
        }
        return pitch
    }
    function getGlobalHeadingAngle() {
        return waylineCopy.current.GlobalHeadingAngle
    }
    function getIsParallelLine() {
        return waylineCopy.current.IsParallelLine
    }
    function getItemDirection(index) {
        return waylineCopy.current.PList[index]
    }
    // 切换禁飞区
    function SwitchNotFlyZone(SN) {
        axiosApi(`/api/v1/FlyArea/GetCurrent?SN=${SN}`, "GET", {}).then((res) => {
            if (res) {
                addNotFlyZone(res.data)
            }
        })
    }
    function setWaylineCopy(newWaylineCopy) {
        waylineCopy.current = newWaylineCopy
        setWayline({ ...waylineCopy.current })
    }
    return (
        <div style={{ height: '100%',padding:12, background: '#ffffff', display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }}>
            <div style={{ width: '15%', height: '100%' }} >
                <WaypointList wayline={wayline} seleteIndex={seleteIndex} seleteIndexChange={seleteIndexChange} delete_function={delete_function} />
            </div>
            <div style={{ width: '60%', height: '100%' }} >
                <div style={{ height: '100%', width: '100%', position: 'relative' }} id="cesisss">
                    <TopToolbar wayline={wayline} wayLineData={wayLineData} addNotFlyZone={addNotFlyZone} SwitchHanger={SwitchHanger} onSave={onSave} SwitchNotFlyZone={SwitchNotFlyZone} setWaylineCopy={setWaylineCopy} />
                    <KeyboardTip />
                    <KeyboardTip2 />
                    <UseHelp />
                    <div style={{ position: 'absolute', right: 30, bottom: 50, zIndex: 1, }}>
                        <MapControl viewerData={viewerData} />
                    </div>
                </div>
            </div>
            <div style={{ width: '25%', height: '100%', paddingTop: '20px', }} >
                <div style={{ height: '100%', position: 'relative' }} >
                    <div style={{ height: '60%', paddingLeft: '10px', paddingRight: '10px' }}>
                        <div style={{ height: '100%' }}>
                            {/* <WayLineSeting wayline={wayline} setWaylineCopy={setWaylineCopy}></WayLineSeting> */}
                            {seleteIndex !== null && <WaypointInfo wayline={wayline} seleteIndex={seleteIndex} item_height_change_function={item_height_change_function} PTZ_headingPitchRoll_Change={PTZ_headingPitchRoll_Change} setWaylineCopy={setWaylineCopy} computeHeading={computeHeading} computePitch={computePitch} />}
                            {seleteIndex !== null && (wayline.PList[seleteIndex].ActionList && wayline.PList[seleteIndex].ActionList.length !== 0) && <ActionListInfo wayline={wayline} seleteIndex={seleteIndex} PTZ_headingPitchRoll_Change={PTZ_headingPitchRoll_Change} setWaylineCopy={setWaylineCopy} />}
                        </div>
                    </div>
                    <div style={{ width: '100%', aspectRatio: (4 / 3), position: 'absolute', bottom: 0 }} id="cesisss2"></div>
                </div>
            </div>
        </div>
    )
};

export default WayLine3DPage;
