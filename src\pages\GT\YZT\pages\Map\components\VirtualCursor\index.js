import React, { useEffect, useState, useRef } from 'react';
import styles from './index.module.less';

/**
 * 虚拟鼠标组件
 * 在实时同步模式下显示其他地图的鼠标位置和操作状态
 * 
 * @param {Object} props - 组件属性
 * @param {Object} props.position - 鼠标位置 {x, y}
 * @param {boolean} props.isDragging - 是否正在拖拽
 * @param {boolean} props.isZooming - 是否正在缩放
 * @param {React.RefObject} props.mapContainerRef - 地图容器的引用
 * @param {boolean} props.visible - 是否显示虚拟鼠标
 * @param {string} props.className - 额外的CSS类名
 */
const VirtualCursor = ({
  position,
  isDragging = false,
  isZooming = false,
  mapContainerRef,
  visible = true,
  className = ''
}) => {
  const [adjustedPosition, setAdjustedPosition] = useState(null);
  const [isInBounds, setIsInBounds] = useState(false);
  const cursorRef = useRef(null);

  // 计算相对于地图容器的坐标
  useEffect(() => {
    if (!position || !mapContainerRef?.current || !visible) {
      setAdjustedPosition(null);
      setIsInBounds(false);
      return;
    }
    
    try {
      const containerRect = mapContainerRef.current.getBoundingClientRect();
      const adjustedPos = {
        x: position.x - containerRect.left,
        y: position.y - containerRect.top
      };
      
      // 确保虚拟鼠标在地图容器内
      const inBounds = adjustedPos.x >= 0 && 
                      adjustedPos.x <= containerRect.width &&
                      adjustedPos.y >= 0 && 
                      adjustedPos.y <= containerRect.height;
      
      if (inBounds) {
        setAdjustedPosition(adjustedPos);
        setIsInBounds(true);
      } else {
        setAdjustedPosition(null);
        setIsInBounds(false);
      }
    } catch (error) {
      console.warn('虚拟鼠标位置计算失败:', error);
      setAdjustedPosition(null);
      setIsInBounds(false);
    }
  }, [position, mapContainerRef, visible]);

  // 获取鼠标图标
  const getCursorIcon = () => {
    if (isDragging) return '✋';
    if (isZooming) return '🔍';
    return '👆';
  };

  // 获取鼠标状态的CSS类
  const getCursorStateClass = () => {
    let stateClass = '';
    if (isDragging) stateClass += ` ${styles.dragging}`;
    if (isZooming) stateClass += ` ${styles.zooming}`;
    return stateClass;
  };

  // 如果位置无效或不在边界内，不渲染
  if (!adjustedPosition || !isInBounds || !visible) {
    return null;
  }

  return (
    <div
      ref={cursorRef}
      className={`${styles.virtualCursor} ${getCursorStateClass()} ${className}`}
      style={{
        left: 0,
        top: 0,
        transform: `translate(${adjustedPosition.x}px, ${adjustedPosition.y}px)`
      }}
      role="presentation"
      aria-hidden="true"
    >
      <span className={styles.cursorIcon}>
        {getCursorIcon()}
      </span>
      
      {/* 可选的状态指示器 */}
      {(isDragging || isZooming) && (
        <div className={styles.statusIndicator}>
          {isDragging && <span className={styles.dragIndicator}>拖拽中</span>}
          {isZooming && <span className={styles.zoomIndicator}>缩放中</span>}
        </div>
      )}
    </div>
  );
};

export default VirtualCursor;
