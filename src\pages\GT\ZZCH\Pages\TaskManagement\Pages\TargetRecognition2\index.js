import { queryPage } from '@/utils/MyRoute';
import { useModel } from "umi";
import { useState, useEffect, useCallback } from 'react';
import { Card, Table, Button, Modal, Form, Input, Select, DatePicker, Checkbox, InputNumber, Radio, Upload, message, Spin, Splitter, Tooltip, Image } from 'antd';
import { TransformWrapper, TransformComponent, } from 'react-zoom-pan-pinch';
import LastPageButton from '@/components/LastPageButton';
import { axiosApi } from "@/services/general";
import { timeFormat } from "@/utils/helper";
import { dataSourceHandler } from './helper'
import TableCols from './table';

// 识别详情页面
const TargetRecognition = () => {
  const { setPage, pageData } = useModel("pageModel");

  const [dataSource, setDataSource] = useState();
  const [currentTablePage, setCurrentTablePage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [pic1, setPic1] = useState();
  const [pic2, setPic2] = useState();
  const [selectedRowId, setSelectedRowId] = useState();
  const [isDJPage, setIsDJPage] = useState(false);

  const [transformState, setTransformState] = useState({
    scale: 1,
    positionX: 0,
    positionY: 0
  });

  const handlePageChange = (page) => {
    setPage(queryPage(page));
  };

  const getDefaultData = async (record) => {
    try {
      setLoading(true);
      const url = `/api/v1/AITask/GetTaskByID?id=${record.ID}`
      const res = await axiosApi(url, 'GET', null);
      const processdData = dataSourceHandler(res.data);
      setDataSource(processdData);
      // 默认显示第一张图片
      setPic1(processdData.result[0].OldPath)
      setPic2(processdData.result[0].NewPath)
      setSelectedRowId(processdData.result[0].ID); //让第一张图片行高亮
      setLoading(false);
    } catch (error) {
      console.log(error);
    }
  }

  // 同步处理函数
  const handleTransform = useCallback((newState) => {
    setTransformState({
      scale: newState.scale,
      positionX: newState.positionX,
      positionY: newState.positionY
    });
  }, []);

  // 生成图片容器
  const createImagePanel = (imgSrc) => (
    <div style={{
      flex: 1,
      maxWidth: 'calc(50% - 8px)',
      height: '100%',
      border: '1px solid #f0f0f0',
    }}>
      <TransformWrapper
        key={`${transformState.scale}-${transformState.positionX}`}
        initialScale={transformState.scale}
        initialPositionX={transformState.positionX}
        initialPositionY={transformState.positionY}
        onZoom={(e) => handleTransform(e.state)}
        onPanningStop={(e) => handleTransform(e.state)}
        minScale={0.5}
        maxScale={3}
        doubleClick={{ disabled: true }}>
        {({ resetTransform }) => (
          <TransformComponent>
            <div
              style={{
                width: '100%',
                height: '100%',
                cursor: 'pointer',
              }}
              onDoubleClick={(e) => {
                e.stopPropagation();
                setTransformState({ scale: 1, positionX: 0, positionY: 0 });
              }}>
              <Image
                preview={false}
                style={{ width: '100%', height: '100%' }}
                src={imgSrc}
                placeholder={
                  <div style={{
                    background: '#fff',
                    height: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center'
                  }}>
                    <Spin size="large" />
                  </div>
                }
              />
            </div>
          </TransformComponent>
        )}
      </TransformWrapper>
    </div>
  );

  useEffect(() => {
    const record = JSON.parse(localStorage.getItem('record'));
    const pageType = localStorage.getItem('isDJPage');
    setIsDJPage(pageType === 'true');
    getDefaultData(record);
  }, [])
  return (
    <div>
      <Card
        title={isDJPage ? <LastPageButton title="识别详情"/> : pageData?.title}
        style={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          flex: 1,
          overflow: 'hidden',
          padding: 0,
        }}
      >
        <div style={{
          display: 'flex',
          height: '100%',
          padding: 0,
          gap: 16
        }}>
          {/* 左侧信息板块 */}
          <div style={{
            flex: '0 0 300px',
            borderRight: '1px solid #f0f0f0',
            paddingRight: 0,
            paddingRight: 16
          }}>
            {dataSource && (
              <>
                <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
                  <div>
                    <strong>任务名称:</strong> {dataSource.task.TaskName}
                  </div>
                  <div>
                    <strong>任务类型:</strong> {dataSource.task.TaskType}
                  </div>
                  <div>
                    <strong>模型名称:</strong> {dataSource.task.ModelName}
                  </div>
                  <div>
                    <strong>识别项目:</strong> {dataSource.task.Aclsname}
                  </div>
                  <div>
                    <strong>创建时间:</strong> {timeFormat(dataSource.task.CreateTime)}
                  </div>
                  <div>
                    <strong>结束时间:</strong> {timeFormat(dataSource.task.EndTime)}
                  </div>
                </div>
                <br></br>
                {/* 表格部分 */}
                <div style={{
                  height: `calc(100vh - 400px)`,
                  overflow: 'hidden'
                }}>
                  <Table
                    // 自定义分页
                    pagination={{
                      simple: true,
                      pageSize: 7,
                      showSizeChanger: false,
                      current: currentTablePage,
                      total: dataSource.result.length,
                      showTotal: (total) => (
                        <span className="custom-pagination-text">
                          共 {Math.ceil(total / 7)} 条 第 {currentTablePage}/{Math.ceil(total / 7)} 页
                        </span>
                      ),
                      onChange: (page) => setCurrentTablePage(page),
                      className: "custom-pagination-container"
                    }}
                    rowKey={(record) => record.ID}
                    loading={loading}
                    bordered
                    dataSource={dataSource.result}
                    columns={TableCols(currentTablePage)}
                    size='small'
                    scroll={{
                      scrollToFirstRowOnChange: true,
                      y: `calc(100vh - 500px)`,  
                    }}
                    onRow = {(record) => {
                      return {
                        onClick: () => {
                          setPic1(record.OldPath)
                          setPic2(record.NewPath)
                          // 设置当前选中行ID
                          setSelectedRowId(record.ID);
                        },
                        style: {
                          // 当前选中行背景色
                          background: record.ID === selectedRowId ? '#b8e6d932' : 'none',
                          cursor: 'pointer'
                        }
                      };
                    }}
                  />
                </div>
              </>

            )}
          </div>

          {/* 右侧图片板块 */}
          <div style={{
            flex: 1,
            display: 'flex',
            flexDirection: 'row',
            gap: 16,
            height: '100%',
            alignItems: 'flex-start'
          }}>
            {createImagePanel(pic1)}
            {createImagePanel(pic2)}
          </div>
        </div>
      </Card>
    </div>

  );
};
export default TargetRecognition;