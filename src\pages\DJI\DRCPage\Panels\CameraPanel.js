import camera2 from "@/assets/drcImgs/camera2.png";
import camera3 from "@/assets/drcImgs/camera3.png";
import camera4 from "@/assets/drcImgs/camera4.png";

import video2 from "@/assets/drcImgs/video2.png";
import video3 from "@/assets/drcImgs/video3.png";
import video4 from "@/assets/drcImgs/video4.png";
import { HGet2 } from "@/utils/request";
import { Get2 } from '@/services/general';
import { isEmpty } from "@/utils/utils";
import { useEffect, useState, useRef } from "react";
import { useModel } from "umi";
import { getMinstr } from "./helper";
import { Modal, Tooltip, Input, InputNumber, message, Spin } from "antd";
import { SyncOutlined } from "@ant-design/icons";
import HanHuaPage from "../../DevicePage/Panels/HanHuaQi";

const CameraPanel = () => {

  const w1 = 60;
  const h1 = 240;
  const { fj, fjData } = useModel("droneModel");
  const { DoCMD } = useModel("cmdModel");
  const device = JSON.parse(localStorage.getItem("device"));
  const [ifVideo, setIfVideo] = useState(false);

  const [isModalOpen, setIsModalOpen] = useState(false);

  const changeCamera = (v) => {
    const data = {
      camera_mode: v,
      payload_index: device.Camera2,
    };
    DoCMD(device.SN, "camera_mode_switch", data);

    // if (v == 1) {
    //   VideoSave();
    // } else {
    //   CameraSave();
    // }
  };

  const photoCamera = (v) => {
    const data = { payload_index: device.Camera2 };
    if (fj?.data?.cameras[0]?.camera_mode == 0) {
      DoCMD(device.SN, "camera_photo_take", data);
    } else {
      changeCamera(0);
      setTimeout(() => {
        DoCMD(device.SN, "camera_photo_take", data);
      }, 1000);
    }
  };

  const VideoStart = () => {
    const data = { payload_index: device.Camera2 };
    DoCMD(device.SN, "camera_recording_start", data);
  };

  const VideoStop = () => {
    const data = { payload_index: device.Camera2 };
    DoCMD(device.SN, "camera_recording_stop", data);
  };

  const VideoClick = () => {
    if (fj?.data?.cameras[0]?.recording_state == 1) {
      VideoStop();
      setIfVideo(false);
    } else {
      changeCamera(1);
      setTimeout(() => {
        VideoStart();
        setIfVideo(true);
      }, 1000);
    }
  };

  const CameraSave = () => {
    const data = {
      payload_index: device.Camera2,
      photo_storage_settings: ["current"],
    };
    DoCMD(device.SN, "photo_storage_set", data);
  };

  const VideoSave = () => {
    const data = {
      payload_index: device.Camera2,
      video_storage_settings: ["current"],
    };
    DoCMD(device.SN, "video_storage_set", data);
  };

  const getVideoImg = () => {
    if (ifVideo) return video3;
    return video4;
  };



  useEffect(() => {
    //
    if (isEmpty(fjData.current)) return;
    if (isEmpty(fjData.current?.cameras)) return;
    if (fjData.current?.cameras[0]?.recording_state == 1) {
      setIfVideo(false);
    } else {
      setIfVideo(true);
    }
  }, [fjData.current]);

  const onHanHuaQi = () => {
    setIsModalOpen(true)
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const getCameraName = () => {
    if (isEmpty(fj.data)) return "";
    if (isEmpty(fj.data.cameras)) return "";

    if (fj.data?.cameras[0]?.recording_state == 0) return "开始录像"
    return "结束录像"
  }
  // if(isEmpty(fj)) return;
  // if(fj.data.cameras[0].camera_mode==0){
  return (
    <>
      <div>
        <Modal
          open={isModalOpen}
          onCancel={handleCancel}
          destroyOnClose={true}
          footer={null}
        >
          <HanHuaPage device={device} setOpen={setIsModalOpen} />
        </Modal>
      </div>

      <div
        draggable={false}
        style={{
          userSelect: "none",
          position: "absolute",
          opacity: 0.6,
          top: 170,
          right: 40,
          height: h1,
          width: w1,
          zIndex: 1010,
        }}
      >
        <div style={{ userSelect: "none", padding: 6.0 }}>
          <Tooltip placement="left" title="喊话器drc">
            <img
              style={{ cursor: "pointer", userSelect: "none" }}
              draggable={false}
              height={42}
              width={42}
              src={camera4}
              onClick={() => onHanHuaQi()}
            ></img>
          </Tooltip>
        </div>
        <div style={{ userSelect: "none", marginTop: 32.0, marginLeft: 6 }}>
          <Tooltip placement="left" title="拍照">
            <img
              style={{ cursor: "pointer", userSelect: "none" }}
              draggable={false}
              height={45}
              width={45}
              src={camera3}
              onClick={() => {
                photoCamera();
              }}
            ></img>
          </Tooltip>
        </div>

        <div style={{ userSelect: "none", marginTop: 32.0, marginLeft: 6 }}>
          <Tooltip
            placement="left"
            title={
              getCameraName()
            }
          >
            <img
              style={{ cursor: "pointer", userSelect: "none" }}
              draggable={false}
              height={45}
              width={45}
              src={getVideoImg()}
              onClick={() => VideoClick()}
            ></img>
          </Tooltip>
        </div>
        <div
          style={{
            userSelect: "none",
            // marginTop: 5.0,
            marginLeft: 13.0,
            fontFamily: "MiSan",
            color: "red",
          }}
        >
          {getMinstr(fj.data ? fj.data?.cameras[0].record_time : '')}
        </div>
      </div>
    </>
  );
};

export default CameraPanel;
