import { getBody<PERSON>, getBodyW, getBodyW2 } from "@/utils/utils";
import imgPG from "@/assets/images/bg.png";
import styles from "./indexPage.css";
import { useState, useEffect, useRef } from "react";
import IfShowPanel from "@/components/IfShowPanel";
import DangerCountPie from "./Panels/DangerCountPie2.js";
import DJBaseMap from "./Panels/OrgMap2";
import LeafletMap from "@/pages/GT/components/Map/2DMap";
import CesiumMap from "@/pages/GT/components/Map/3DMap";
import AMap from "./Panels/AMapContainer";
import { axiosApi } from "@/services/general";
import useDeviceStore from "@/stores/deviceStore";
import DataPanel from "./Panels/DataPanel";
import JianKongPanel from "./Panels/JianKongPanel";
import ZhiBanPanel from "./Panels/ZhiBanPanel";
import FlightPanel from "./Panels/FlightTaskPanel";
import DockStatePanel from "./Panels/DockPanel";
import MainDataPanel from "./Panels/MainDataPanel";
import RealTimeTasks from "./Panels/RealTimeTasks";
import { getGuid } from "@/utils/helper";
import NoticePanel from "./Panels/NoticePanel";
import { useModel } from "umi";
import VideoPanel from "./components/VideoPanel";

const OrgPage = ({className}) => {
  const { fetchBaseMapData } = useModel('mapModel');
  const { deviceList, startAutoRefresh, stopAutoRefresh } = useDeviceStore();
  const mapRef = useRef(null);
  const [mapType, setMapType] = useState('2D');
  const djMapRef = useRef(null);
  const [showVideoPanel, setShowVideoPanel] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [currentRoute, setCurrentRoute] = useState(null);

  useEffect(() => {
    fetchBaseMapData();
  }, [fetchBaseMapData]);

  // 处理机场点击，移动地图中心到该位置
  const handleAirportClick = (airportInfo) => {
    // 如果有地图引用，调用地图组件的方法移动中心
    if (djMapRef.current && djMapRef.current.setMapCenter) {
      // djMapRef.current.setMapCenter(airportInfo.lat, airportInfo.lng);
      djMapRef.current.flyToCenter(airportInfo.lat, airportInfo.lng);
    }
  };

  // 显示航线的函数，用于在地图上直接绘制航线
  const handleShowRoute = (routeData) => {
    setCurrentRoute(routeData);
    // 使用原系统风格绘制航线
    if (djMapRef.current && djMapRef.current.showWayLineOriginal) {
      djMapRef.current.showWayLineOriginal(routeData);
    }
  };

  // 处理视频图标点击，显示视频面板
  const handleVideoClick = (device) => {
    setSelectedDevice(device);
    setShowVideoPanel(true);
  };

  // 关闭视频面板
  const handleCloseVideoPanel = () => {
    setShowVideoPanel(false);
  };

  const getPanel = (
    <div style={{ height: "100%" }}>
      <DockStatePanel
        key="dock-panel"
        data={deviceList}
        onAirportClick={handleAirportClick}
        onVideoClick={handleVideoClick}
        onShowRoute={handleShowRoute}
      />
      {/* <FlightPanel /> */}
    </div>
  );

  const getPanel2 = (
    <div style={{ height: "100%" }}>
      {/* <RealTimeTasks  key={getGuid()} deviceList={deviceList}></RealTimeTasks> */}
      <NoticePanel></NoticePanel>
      {/* <JianKongPanel />

        <DangerCountPie /> */}
    </div>
  );

  // 获取所有航线
  const getFlightTask = async (sn) => {
    try {
      const res = await axiosApi('/api/v1/FlightTask/GetSurveyFlightTaskByAirportSn', 'GET', {sn});
      return res?.data || [];
    }
    catch (error) {
      console.log(error);
    }
  };
  useEffect(() => {
    // 启动设备数据自动刷新，每60秒更新一次
    startAutoRefresh(60000);

    // 清理函数
    return () => {
      stopAutoRefresh();
    };
  }, [startAutoRefresh, stopAutoRefresh]);



  return (
    <div style={{ height: getBodyH(56) }}>
      <div
        className={`${styles.CradGroup} ${className}`}
        style={{
          position: "relative",
          width: "100%",
          height: "100%",
        }}
      >
        {/* 设备列表 */}
        {IfShowPanel(getBodyH(0), 500, 0, null, 0, null, getPanel, true, null, null, 1001)}
        {/* 告警信息 */}
        {IfShowPanel(getBodyH(150), 320, 8, null, 8, null, getPanel2, true)}

        {/*  {IfShowPanel(98, 200,null, `47.2%`, null,30,<img height={98} width={100} src={BDZL}></img>, true)}*/}
        {/* {IfShowPanel(120, 800, 40, getBodyW(660), null, null, <MainDataPanel></MainDataPanel>, true)} */}
        {/* <AMap data={deviceList} h1={getBodyH(56)}/> */}
        {/* <OrgMap h1={getBodyH(56)}/> */}

        <DJBaseMap ref={djMapRef} data={deviceList} h1={getBodyH(56)} currentRoute={currentRoute}></DJBaseMap>

        {/* 视频面板 */}
        <VideoPanel 
          device={selectedDevice} 
          visible={showVideoPanel} 
          onClose={handleCloseVideoPanel} 
        />
      </div>
      {/* <div className={styles.shadow}></div> */}
    </div>
  );
};

export default OrgPage;
