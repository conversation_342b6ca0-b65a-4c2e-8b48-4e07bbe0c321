import { Outlet, useLocation } from 'umi';

// 公共样式
import commonStyle from '@/pages/SI/style/common.less';
// antd 公共样式
import '@/pages/SI/style/antd-common.less';
// 全局主题配置
import SITheme from '@/pages/SI/style/theme';
// 页面样式
import './index.less';
import useConfigStore from "@/stores/configStore";
// 滚动条样式
import '@/pages/SI/style/scrollbar.less';

import { useModel, history } from 'umi';
import { ConfigProvider } from 'antd';
import locale from 'antd/locale/zh_CN';
// 组件
import MyHead from '@/pages/SI/components/MyHead';
import MyHead2 from '@/pages/SI/components/MyHead/index2';
import { useEffect } from 'react';

// 主页面容器 通过路由跳转，再由各自的组件进行布局，page切换
function App() {
  const { systemInfos, setHeaderHeight } = useConfigStore()

  const handlePageChange = page => {
    history.push(`/SI/${page}`);
  };
  const headList = [
    { label: '低空监测', key: 'index' },
    { label: '飞控中心', key: 'controlCenter' },
    { label: 'AI算法仓', key: 'ai' },
    { label: '监测处置', key: 'monitorDisposal' },
    { label: '行业应用', key: 'patrol' },
    // { label: '系统管理', key: 'system' },
  ];

  useEffect(() => {
    function fc() {
      if (location?.pathname === '/SI/system') {
        //没有导航栏，设置各页面stores分发header高度为0, 隐藏导航栏
        setHeaderHeight(0)
      }
    }
    fc()
  }, [location])

  // 登录页面
  const location = useLocation();
  if (location.pathname === '/SI/login') {
    return <Outlet />;
  }

  if (location.pathname === '/SI/system') {
    return (
      <>
        {/* <MyHead2 /> */}
        <ConfigProvider
          locale={locale}
          theme={SITheme}
        >
          <Outlet />
        </ConfigProvider>
      </>

    );
  }

  return (
    <div className="gt-page1">
      <div className={commonStyle.gt_back_black} style={{ position: 'relative', overflow: 'hidden', height: '100vh' }}>
        {/* <MyHead headList={headList} handlePageChange={handlePageChange} /> */}
        <MyHead2 />
        <div className="si-container blackBackground">
          {/* 配置国际化 */}
          <ConfigProvider
            locale={locale}
            theme={SITheme}
          >
            <Outlet />
          </ConfigProvider>
        </div>
      </div>
    </div>
  );
}

export default App;
