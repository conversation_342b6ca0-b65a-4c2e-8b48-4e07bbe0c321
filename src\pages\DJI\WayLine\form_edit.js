import {
  Input,
  Radio,
  Descriptions,
  Select,
  Modal,
  message,
  Upload,
  Switch,
  Button
} from "antd";
import { InboxOutlined } from "@ant-design/icons";
import React, { useState, useEffect } from "react";
import { Get2, Post2, axiosApi } from "@/services/general";
import { isEmpty,downloadFile2,getImgUrl } from "@/utils/utils";
const { Dragger } = Upload;

const WayLineEditForm = ({ w1, refrush }) => {
  const [wName, setWName] = useState(w1.WayLineName);
  const [canSee, setCanSee] = useState(false);

  let [wayLineId, setWayLineId] = useState(w1.WanLineId);
  let [isZS, setIsZS] = useState(0);
  let [ZsFBL, setZsFBL] = useState();
  let [IsSW, setIsSW] = useState(0);
  let [SwQlh, setSwQlh] = useState(false);
  let [SwWlys, setSwWlys] = useState();
  let [SwJhys, setSwJhys] = useState();
  let [fileName, setFileName] = useState();
  let [StyleFile, setStyleFile] = useState();
  const token = localStorage.getItem("token");

  let TextureCompressionList = [
    { value: 0, label: "无纹理压缩" },
    { value: 1, label: "WEBP压缩" },
    { value: 2, label: "KTX压缩" },
    { value: 3, label: "CRN压缩" },
    { value: 4, label: "KTX2.0压缩" },
  ];
  let GeometricCompressionList = [
    { value: 0, label: "无几何压缩" },
    { value: 1, label: "DRACO压缩" },
  ];

  const getWayLineDetailData = async () => {
    // 获取航线详情
    const formData = new FormData();
    formData.append("wayLineId", wayLineId);
    const res = await axiosApi(
      "/api/v1/WayLine/WayLineDetailSelect",
      "POST",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
    console.log("res", res);

    if (res && res.data) {
      setIsZS(res.data.IsZS);
      setZsFBL(res.data.ZsFBL);
      setIsSW(res.data.IsSW);
      setSwQlh(res.data.SwQlh);
      setSwWlys(res.data.SwWlys);
      setSwJhys(res.data.SwJhys);
      setStyleFile(res.data.StyleFile);
    }
  };

  let currentFile;
  const upProps = {
    name: "file",
    multiple: true,
    headers: {
      authorization: "authorization-text",
      auth: token,
    },
    beforeUpload: (file) => {
      setFileName(file.name);
      currentFile = file; // 保存当前文件
      return false; // 阻止自动上传
    },
  };

  const WayLineDetailSave = async () => {
    // 上传文件
    let file = new Blob([currentFile]);
    const reader = new FileReader();

    reader.onload = async (event) => {
      const formData = new FormData();
      formData.append("file", file, fileName);
      formData.append("wayLineId", wayLineId);
      formData.append("isZS", isZS);
      formData.append("ZsFBL", ZsFBL);
      formData.append("IsSW", IsSW);
      formData.append("SwQlh", SwQlh);
      formData.append("SwWlys", SwWlys);
      formData.append("SwJhys", SwJhys);

      try {
        const res = await axiosApi(
          "/api/v1/WayLine/WayLineDetailSave",
          "POST",
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
        message.success("编辑成功");
      } catch (error) {
        message.error("保存失败，请重试");
      }
    };

    reader.onerror = (error) => {
      console.error("文件读取错误:", error);
      message.error("文件读取失败，请重试");
    };
    reader.readAsArrayBuffer(file);
  };
  const handleClearState = () => {
    setIsZS(null);
    setZsFBL(null);
    setIsSW(null);
    setSwQlh(null);
    setSwWlys(null);
    setSwJhys(null);
  };
  const Save = async () => {
    WayLineDetailSave();
    const xx = await Post2("/api/v1/WayLine/Update", {
      ...w1,
      WayLineName: wName,
    });

    setCanSee(false);
    refrush();
  };

  const getWayLineDetail = () => {
    return (
      <>
        <Descriptions.Item label="生成正射DOM" span={24}>
          <Radio.Group value={isZS} onChange={(e) => setIsZS(e.target.value)}>
            <Radio key={1} value={1}>
              是
            </Radio>
            <Radio key={0} value={0}>
              否
            </Radio>
          </Radio.Group>
        </Descriptions.Item>
        {isZS == 1 && (
          <>
            <Descriptions.Item label="正射分辨率" bordered span={24}>
              <Input
                value={ZsFBL}
                onChange={(e) => {
                  setZsFBL(e.target.value);
                }}
                allowClear
              />
            </Descriptions.Item>
          </>
        )}
        <Descriptions.Item label="生成三维B3DM" span={24}>
          <Radio.Group value={IsSW} onChange={(e) => setIsSW(e.target.value)}>
            <Radio key={1} value={1}>
              是
            </Radio>
            <Radio key={0} value={0}>
              否
            </Radio>
          </Radio.Group>
        </Descriptions.Item>

        {IsSW == 1 && (
          <>
            <Descriptions.Item label="三维是否轻量化">
              <Switch
                onChange={(e) => {
                  setSwQlh(e);
                }}
                checkedChildren="开启"
                unCheckedChildren="关闭"
              />
            </Descriptions.Item>
            <Descriptions.Item label="纹理压缩">
              <Select
                allowClear
                onClear={() => setSwWlys(null)}
                placeholder={"选择纹理压缩类型"}
                defaultValue={TextureCompressionList[SwWlys * 1]?.label}
                onSelect={(e) => {
                  setSwWlys(e);
                }}
              >
                {TextureCompressionList.map((item) => {
                  return (
                    <Select.Option key={item.value} data={item.value}>
                      {item.label}
                    </Select.Option>
                  );
                })}
              </Select>
            </Descriptions.Item>
            <Descriptions.Item label="几何压缩" span={24}>
              <Select
                allowClear
                onClear={() => setSwJhys(null)}
                placeholder={"选择几何压缩类型"}
                defaultValue={GeometricCompressionList[SwJhys * 1]?.label}
                onSelect={(e) => {
                  setSwJhys(e);
                }}
              >
                {GeometricCompressionList.map((item) => {
                  return (
                    <Select.Option key={item.value} data={item.value}>
                      {item.label}
                    </Select.Option>
                  );
                })}
              </Select>
            </Descriptions.Item>
          </>
        )}
        <Descriptions.Item label={"上传文件"} span={24}>
          <Dragger {...upProps}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            {StyleFile && <p className="ant-upload-text">重新上传文件</p>}
          </Dragger>
          {StyleFile && (
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                marginTop: 10,
              }}
            >
              <Button type="dashed" size="small">
                当前文件：{StyleFile.slice(StyleFile.indexOf("/")+1, StyleFile.length)}
              </Button>
              <Button type="primary" size="small" onClick={()=>{
                downloadFile2(getImgUrl(StyleFile))
              }}>
                下载
              </Button>
            </div>
          )}
        </Descriptions.Item>
      </>
    );
  };

  return (
    <div>
      <a
        style={{color: "#18B092"}}
        onClick={() => {
          setCanSee(true);
          getWayLineDetailData();
        }}
      >
        编辑
      </a>{" "}
      <Modal
        title={null}
        onOk={Save}
        open={canSee}
        onCancel={() => {
          setCanSee(false);
          handleClearState();
        }}
        style={{ minWidth: 900, overflowX: "auto" }}
      >
        <div>
          <Descriptions title="航线编辑" column={2} colon={false} bordered>
            <Descriptions.Item label="航线名称" span={24}>
              <Input
                defaultValue={wName}
                onChange={(e) => {
                  setWName(e.target.value);
                }}
                allowClear
              />
            </Descriptions.Item>

            {getWayLineDetail()}
          </Descriptions>
        </div>
      </Modal>
    </div>
  );
};

export default WayLineEditForm;
