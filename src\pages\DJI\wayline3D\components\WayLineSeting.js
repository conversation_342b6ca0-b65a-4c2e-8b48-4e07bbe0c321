import { useState, useEffect, useRef } from 'react';
import { Slider, InputNumber, Tooltip } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
function WayLineSeting({ wayline, setWaylineCopy }) {
    function GlobalSpeedChange(value) {
        wayline.PList.forEach((item, index) => {
            if (item.Speed === wayline.GlobalSpeed) {
                item.Speed = value
            }
        })
        wayline.GlobalSpeed = value
        setWaylineCopy({ ...wayline, })
    }
    function GlobalHeadingAngleChange(e) {
        let res = e.target.value
        if (res > 180) {
            res = 180
        } else if (res < -180) {
            res = -180
        }
        wayline.GlobalHeadingAngle = res
        setWaylineCopy({ ...wayline, })
    }

    return <div style={{ width: '100%' }}>
        <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>全局航线速度</div>
        <Slider
            marks={{
                '1': '1',
                '5': '5',
                '10': '10',
                '15': '15',
            }}
            size="small"
            min={1}
            max={15}
            precision={2}
            style={{ width: '95%' }}
            value={wayline.GlobalSpeed}
            onChange={(e) => { GlobalSpeedChange(e) }} />
        <div style={{ fontSize: 12, color: '#666', marginBottom: '3px' }}>飞行器偏航角</div>
        <Tooltip placement="bottom" title='输入完成后按回车确认'>
            <InputNumber
                style={{ width: '100%' }}
                min={-180}
                max={180}
                value={wayline.GlobalHeadingAngle}
                addonAfter={<Tooltip placement="left" title='清空偏航角'>
                    <DeleteOutlined onClick={() => {
                        setWaylineCopy({ ...wayline, GlobalHeadingAngle : '' })
                    }} /></Tooltip>}
                onPressEnter={(e) => {
                    GlobalHeadingAngleChange(e)
                }}>
            </InputNumber>
        </Tooltip>
    </div>
}
export default WayLineSeting;