import { LockOutlined, UserOutlined } from "@ant-design/icons";
import { Form, Input, Button, message } from "antd";
import { useEffect, useState } from "react";
import LoadPanel from "@/components/IfShowPanel/load_panel";
import { history, useModel, useSearchParams, useParams } from "umi";
import configStore from "@/stores/configStore";
import styles from "./index.less";
import { HGet2, HPost2 } from "@/utils/request";
import { isEmpty } from "@/utils/utils";
import { axiosApi } from "@/services/general";

const LoginPage = (props) => {
  let { systemInfos } = configStore();
  const { userLogin = {}, submitting } = props;
  const [load, setIsload] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();

  const submitData = (data) => {
    if (isEmpty(data)) return;
    if (!isEmpty(data.err)) {
      message.info(data.err);
      return;
    }

    localStorage.setItem("token", data.token);
    localStorage.setItem("user", JSON.stringify(data.user));
    localStorage.setItem("orgId", data.user.OrgCode);
    localStorage.setItem("orgName", data.user.OrgName);
    localStorage.setItem("third_party_list", JSON.stringify(data.third_party_list));

    // if (window.location.href.indexOf("token") > 0) {
    //   let url = new URL(window.location.href);
    //   url.searchParams.delete("token");
    //   url.searchParams.delete("json");
    //   // window.location.href = url.href.split('#')[0] + "#/DJ/login";
    // } else {
    //   history.push("/DJ/index");
    //   console.log("登录成功");
    // }
    history.push("/DJ/index");
  };
  const handleSubmit = async (e) => {
    const data = await HGet2(
      "/api/v2/User/Login?userName=" + e.userName + "&password=" + e.password
    );
    // const data=await HPost2('/api/v2/User/Login',{Id:e.userName,Password:e.password});
    submitData(data);
    console.log("login", data);
    
  };

  useEffect(() => {
    const getByToken = async () => {
      const params = new URLSearchParams(window.location.search);
      const apiToken = params.get("api_token");
      const token = params.get("token");

      if (isEmpty(apiToken) && isEmpty(token)) {
        setIsload(true);
        return;
      }

      let data;
      if (apiToken) {
        data = await HGet2("/api/v2/User/LoginByTokenDFM?token=" + apiToken);
      } else if (token) {
        await axiosApi(`api/v2/verifyToken`, "POST", { token: token }).then(
          (res) => {
            if (res && res.user) {
              data = { ...res, token: token };
            }
          }
        );
      }

      if (data) {
        submitData(data);
        setIsload(true);
      }
      //清除地址栏参数
      const url = new URL(window.location.href);
      url.searchParams.delete("token");
      url.searchParams.delete("api_token");
      window.history.replaceState({}, document.title, url.toString());
    };

    getByToken();
    localStorage.removeItem("PageIndexTitle");
  }, []);

  const fff = (
    <div className={styles.appLogin}>
      <div className={styles.appLoginForm} style={{ width: 450, height: 300 }}>
        <div style={{}}>
          <h2
            style={{ width: "100%", textAlign: "center", marginBottom: 12.0 }}
            className="title"
          >
            {systemInfos?.title ? systemInfos.title : "无人机应急管理系统"}
          </h2>
        </div>
        <div className="body">
          <Form
            name="basic-login"
            // form={loginForm}
            initialValues={{ remember: true, agreement: true }}
            onFinish={handleSubmit}
            autoComplete="off"
          >
            <Form.Item
              name="userName"
              rules={[{ required: true, message: "请输入用户名" }]}
            >
              <Input
                size="large"
                prefix={<UserOutlined className="site-form-item-icon" />}
                placeholder="账户"
              />
            </Form.Item>
            <Form.Item
              name="password"
              rules={[{ required: true, message: "请输入密码" }]}
            >
              <Input.Password
                size="large"
                prefix={<LockOutlined className="site-form-item-icon" />}
                placeholder="密码"
              />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit" block size="large">
                登录
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    </div>
  );

  if (!load) {
    return <LoadPanel></LoadPanel>;
  }

  return fff;
};

export default LoginPage;

// import { LockOutlined, UserOutlined } from "@ant-design/icons";
// import { Form, Input, Button, message } from "antd";
// import { useEffect, useState } from "react";
// import LoadPanel from "@/components/IfShowPanel/load_panel";
// import { history, useModel, useSearchParams, useParams } from "umi";
// import configStore from "@/stores/configStore";
// import styles from "./index.less";
// import { HGet2, HPost2 } from "@/utils/request";
// import { getBodyH, isEmpty } from "@/utils/utils";
// import { Post2, Post3 } from "@/services/general";
// import { allCS } from "./ceshi";
// import { Get2 } from "@/services/general";

// const LoginPage = (props) => {
//   let { systemInfos } = configStore();
//   const { userLogin = {}, submitting } = props;
//   const [load, setIsload] = useState(false);
//   const [searchParams, setSearchParams] = useSearchParams();

//   const submitData = (data) => {
//     //
//     if (isEmpty(data)) return;
//     if (!isEmpty(data.err)) {
//       message.info(data.err);
//       return;
//     }

//     localStorage.setItem("token", data.token);
//     localStorage.setItem("user", JSON.stringify(data.user));
//     localStorage.setItem("orgId", data.user.OrgCode);
//     localStorage.setItem("orgName", data.user.OrgName);

//     if (window.location.href.indexOf("token") > 0) {
//       let url = new URL(window.location.href);
//       url.searchParams.delete("token");
//       url.searchParams.delete("json");
//       url.searchParams.delete("system");
//       window.location.href = url.href.split("#")[0] + "#/DJ/login";
//     } else {
//       history.push("/DJ/index");
//     }
//     console.log(data);
//   };
//   const handleSubmit = async (e) => {
//     const data = await HGet2(
//       "/api/v2/User/Login?userName=" + e.userName + "&password=" + e.password
//     );
//     console.log("login", data);
//     submitData(data);
//   };

//   useEffect(() => {
//     const getByToken = async () => {
//       const params = new URLSearchParams(window.location.search);
//       const p1 = params.get("api_token");
//       if (isEmpty(p1)) {
//         setIsload(true);
//         return;
//       }
//       // const data=await Post3('/api/v2/User/Login2',p1);
//       const data = await HGet2("/api/v2/User/LoginByTokenDFM?token=" + p1);

//       submitData(data);
//       setIsload(true);
//     };

//     getByToken();
//     localStorage.removeItem("PageIndexTitle");
//   }, []);

//   const fff = (
//     <div className={styles.appLogin}>
//       <div className={styles.appLoginForm} style={{ width: 470, height: 300 }}>
//         <div
//           style={{
//             width: "",
//             display: "flex",
//             marginBottom: 12,
//           }}
//         >
//           <h2
//             style={{
//               width: "400px",
//               textAlign: "center",
//               marginBottom: 12.0,
//               whiteSpace: "nowrap",
//             }}
//             className="title"
//           >
//             {systemInfos?.title? systemInfos.title : "无人机应急管理系统"}
//           </h2>
//         </div>
//         <div className="body">
//           <Form
//             name="basic-login"
//             // form={loginForm}
//             initialValues={{ remember: true, agreement: true }}
//             onFinish={handleSubmit}
//             autoComplete="off"
//           >
//             <Form.Item
//               name="userName"
//               rules={[{ required: true, message: "请输入用户名" }]}
//             >
//               <Input
//                 size="large"
//                 prefix={<UserOutlined className="site-form-item-icon" />}
//                 placeholder="账户"
//               />
//             </Form.Item>
//             <Form.Item
//               name="password"
//               rules={[{ required: true, message: "请输入密码" }]}
//             >
//               <Input.Password
//                 size="large"
//                 prefix={<LockOutlined className="site-form-item-icon" />}
//                 placeholder="密码"
//               />
//             </Form.Item>
//             <Form.Item>
//               <Button type="primary" htmlType="submit" block size="large">
//                 登录
//               </Button>
//             </Form.Item>
//           </Form>
//         </div>
//       </div>
//     </div>
//   );

//   if (!load) {
//     return <LoadPanel></LoadPanel>;
//   }

//   return fff;
// };

// export default LoginPage;
