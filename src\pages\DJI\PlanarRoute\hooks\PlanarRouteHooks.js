import { useState, useEffect, useRef } from 'react';
import { Cesium } from "umi";
import { message } from 'antd';
import { GetCesiumViewer, computeBuffer, Cartesian3_TO_Position, computePlanarRoute, computeInterval, PointImg3, computeAngle, computeGSD, computeIntervalTime, modelToEnum } from '@/utils/cesium_help';
const usePlanarRouteHooks = (getWayline, setviewer, setModel) => {
    // 大地图viewer
    const viewer = useRef(null);
    // 鼠标移动点位置
    let move_position = useRef([]);
    // 实体集合
    let CesiumEntitys = useRef({
        positions: [],
        polyline_CesiumEntity: null,
        polygon_CesiumEntity: null,
        points: []
    });
    // cesium事件
    let handlerPoint = useRef(null)
    // 移动选中的点
    let move_entity_index = useRef(null);
    // 延时器
    let timer = useRef(null)
    // 鼠标移动方式
    let cursor = useRef('')
    // 正在拖动的航点索引
    let move_index = useRef(null)
    // 多边形各边中点实体数组
    let Midpoint_cesiumObjectEntity_Array = useRef([]);
    // 航线实体
    let wayline_polyline = useRef(null);
    let wayline_position = useRef([]);
    let model = useRef('M4TD');
    // 航线信息
    let wayline = useRef({
        PList: [],
        WaylineName: '航线名称',
        WaylineType: '3',
        WanLineId: '',
        SN: null,
        GlobalSpeed: 10,
        GlobalHeadingAngle: '',
        payload_lens_index: ["visable"],
        AreaPoint: [],
        shoot_time: 0,
        line_rate: 80,
        side_rate: 70,
        GSD: 3.56,
        fly_height: 100,
        center_line_angle: 0
    })
    // 禁飞区实体合集
    let nfzList = useRef([])
    // 围栏实体合集
    let dfenceList = useRef([])
    // 页面载入
    useEffect(() => {
        viewer.current = GetCesiumViewer('cesisss')
        viewer.current.cesiumWidget.screenSpaceEventHandler.removeInputAction(
            Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
        );
        viewer.current.scene.camera.setView({
            destination: Cesium.Cartesian3.fromDegrees(103.99092644903143, 30.76325334137705, 5000),
            orientation: {
                heading: 0,
                pitch: Cesium.Math.toRadians(-90),
                roll: 0,
            },
        });
        setviewer(viewer.current)
        document.getElementById('cesisss').oncontextmenu = function () {//地图上取消浏览器默认的右击事件
            return false;
        }
        // 添加cesium事件
        handlerPoint.current = new Cesium.ScreenSpaceEventHandler(viewer.current.scene.canvas)
        //鼠标事件
        LEFT_DOWN()
        LEFT_UP()
        MOUSE_MOVE()
        RIGHT_CLICK()
        return () => {
            destroy()
        };
    }, []);
    // 鼠标左键按下事件
    function LEFT_DOWN() {
        handlerPoint.current.setInputAction(function (e) {
            var pickedObject = viewer.current.scene.pick(e.position);
            if (move_entity_index.current !== null) {
                timer.current = setTimeout(() => {
                    if (pickedObject.id.point) {
                        viewer.current._container.style.cursor = "move";
                        cursor.current = "move"
                        move_index.current = Number(pickedObject.id.index)
                        viewer.current.scene.screenSpaceCameraController.enableRotate = false;//禁止旋转
                    }
                    clearTimeout(timer.current)
                    timer.current = null
                }, 80)
            } else {
                timer.current = setTimeout(() => {
                    clearTimeout(timer.current)
                    timer.current = null
                }, 80)
            }
        }, Cesium.ScreenSpaceEventType.LEFT_DOWN);
    }
    // 鼠标左键抬起事件
    function LEFT_UP() {
        handlerPoint.current.setInputAction(function (e) {
            if (timer.current) {
                clearTimeout(timer.current)
                if (move_entity_index.current === null) {
                    if (wayline.current.PList.length) {
                        return
                    }
                    var ray = viewer.current.camera.getPickRay(e.position);
                    var cartesian = viewer.current.scene.globe.pick(ray, viewer.current.scene);
                    if (!cartesian) {//underfind说明地图还没加载成功
                        return
                    }
                    let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(cartesian, viewer.current)
                    CesiumEntitys.current.positions.push(longitude, latitude, height)
                    CesiumEntitys.current.points.push(viewer.current.entities.add({
                        name: `节点`,
                        index: CesiumEntitys.current.points.length,
                        position: new Cesium.Cartesian3.fromDegrees(longitude, latitude, height),
                        point: {
                            show: true,
                            pixelSize: 12,
                            color: Cesium.Color.fromCssColorString('#fff'), // 点的颜色
                            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                        },
                    }))
                    wayline_polyline.current && creatArea()
                    if (!CesiumEntitys.current.polyline_CesiumEntity) {
                        CesiumEntitys.current.polyline_CesiumEntity = viewer.current.entities.add({
                            polyline: {
                                show: true,
                                positions: new Cesium.CallbackProperty(() => {
                                    return new Cesium.Cartesian3.fromDegreesArrayHeights([...CesiumEntitys.current.positions, ...move_position.current, CesiumEntitys.current.positions[0], CesiumEntitys.current.positions[1], CesiumEntitys.current.positions[2]]);
                                }, false),
                                // 宽度
                                width: 5,
                                // 线的颜色
                                material: Cesium.Color.fromCssColorString('#409EFF'),
                                clampToGround: true,
                                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                            },
                            polygon: {
                                hierarchy: new Cesium.CallbackProperty(() => {
                                    return new Cesium.PolygonHierarchy(new Cesium.Cartesian3.fromDegreesArrayHeights([...CesiumEntitys.current.positions, ...move_position.current]))
                                }, false),
                                // 宽度
                                width: 5,
                                // 线的颜色
                                material: Cesium.Color.fromCssColorString('#409EFF').withAlpha(0.3),
                                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                            },
                        })
                    }
                } else {
                }
            } else {
                console.log('长按事件');
                viewer.current._container.style.cursor = "";
                cursor.current = ""
                move_index.current = null
                viewer.current.scene.screenSpaceCameraController.enableRotate = true;//开启旋转
            }
        }, Cesium.ScreenSpaceEventType.LEFT_UP);
    }
    //鼠标移动事件
    function MOUSE_MOVE() {
        handlerPoint.current.setInputAction(function (e) {
            const cartesian = viewer.current.scene.pickPosition(e.endPosition) //获取点击位置的地理坐标
            var pickedObject = viewer.current.scene.pick(e.endPosition);
            if (!cartesian) {//underfind说明地图还没加载成功
                return
            }
            let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(cartesian, viewer.current)
            move_position.current = [longitude, latitude, height]
            if (pickedObject && pickedObject.id && pickedObject.id.name === '节点') {
                if (move_entity_index.current === null) {
                    move_entity_index.current = pickedObject.id.index
                    CesiumEntitys.current.points[move_entity_index.current].point.color = Cesium.Color.RED
                } else {
                    if (move_entity_index.current >= 0) {
                        CesiumEntitys.current.points[move_entity_index.current].point.color = Cesium.Color.WHITE
                    } else {
                        Midpoint_cesiumObjectEntity_Array.current[-move_entity_index.current - 1].point.color = Cesium.Color.WHITE
                    }
                    move_entity_index.current = pickedObject.id.index
                    CesiumEntitys.current.points[move_entity_index.current].point.color = Cesium.Color.RED
                }
            } else if (pickedObject && pickedObject.id && pickedObject.id.name === '中点') {
                if (move_entity_index.current === null) {
                    move_entity_index.current = pickedObject.id.index
                    Midpoint_cesiumObjectEntity_Array.current[-move_entity_index.current - 1].point.color = Cesium.Color.RED
                } else {
                    if (move_entity_index.current >= 0) {
                        CesiumEntitys.current.points[move_entity_index.current].point.color = Cesium.Color.WHITE
                    } else {
                        Midpoint_cesiumObjectEntity_Array.current[-move_entity_index.current - 1].point.color = Cesium.Color.WHITE
                    }
                    move_entity_index.current = pickedObject.id.index
                    Midpoint_cesiumObjectEntity_Array.current[-move_entity_index.current - 1].point.color = Cesium.Color.RED
                }
            } else {
                if (move_entity_index.current !== null) {
                    if (move_entity_index.current >= 0) {
                        CesiumEntitys.current.points[move_entity_index.current].point.color = Cesium.Color.WHITE
                    } else {
                        Midpoint_cesiumObjectEntity_Array.current[-move_entity_index.current - 1].point.color = Cesium.Color.WHITE
                    }
                    move_entity_index.current = null
                }
            }
            if (cursor.current === "move") {
                viewer.current._container.style.cursor = "move";
                if (move_index.current >= 0) {
                    CesiumEntitys.current.points[move_index.current].position.setValue(new Cesium.Cartesian3.fromDegrees(longitude, latitude, globeHeight))
                    CesiumEntitys.current.positions.splice((move_index.current * 3), 3, longitude, latitude, globeHeight)
                    creatArea()
                    const point1 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[(move_index.current) * 3], CesiumEntitys.current.positions[(move_index.current) * 3 + 1], CesiumEntitys.current.positions[(move_index.current) * 3 + 2]) // 第一个点的坐标（x、y、z）
                    let point2 = null
                    if (move_index.current === Midpoint_cesiumObjectEntity_Array.current.length - 1) {
                        point2 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[0], CesiumEntitys.current.positions[1], CesiumEntitys.current.positions[2]) // 第二个点的坐标（x、y、z）
                    } else {
                        point2 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[(move_index.current) * 3 + 3], CesiumEntitys.current.positions[(move_index.current) * 3 + 4], CesiumEntitys.current.positions[(move_index.current) * 3 + 5]) // 第二个点的坐标（x、y、z）
                    }
                    const centerPosition = midpointCoordinates(point1, point2)
                    Midpoint_cesiumObjectEntity_Array.current[move_index.current].position.setValue(centerPosition)
                    let point3 = null
                    if ((move_index.current) === 0) {
                        point3 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[CesiumEntitys.current.positions.length - 3], CesiumEntitys.current.positions[CesiumEntitys.current.positions.length - 2], CesiumEntitys.current.positions[CesiumEntitys.current.positions.length - 1]) // 第二个点的坐标（x、y、z）
                    } else {
                        point3 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[(move_index.current) * 3 - 3], CesiumEntitys.current.positions[(move_index.current) * 3 - 2], CesiumEntitys.current.positions[(move_index.current) * 3 - 1]) // 第二个点的坐标（x、y、z）
                    }
                    const centerPosition2 = midpointCoordinates(point1, point3)
                    if ((move_index.current) === 0) {
                        Midpoint_cesiumObjectEntity_Array.current[Midpoint_cesiumObjectEntity_Array.current.length - 1].position.setValue(centerPosition2)
                    } else {
                        Midpoint_cesiumObjectEntity_Array.current[move_index.current - 1].position.setValue(centerPosition2)
                    }
                } else {
                    Midpoint_cesiumObjectEntity_Array.current[-move_index.current - 1].position.setValue(new Cesium.Cartesian3.fromDegrees(longitude, latitude, globeHeight))
                    Midpoint_cesiumObjectEntity_Array.current[-move_index.current - 1].point.pixelSize.setValue(12)
                    Midpoint_cesiumObjectEntity_Array.current[-move_index.current - 1].name = '节点'
                    CesiumEntitys.current.points.splice((-move_index.current), 0, Midpoint_cesiumObjectEntity_Array.current[-move_index.current - 1])
                    Midpoint_cesiumObjectEntity_Array.current.splice((-move_index.current - 1), 1)
                    CesiumEntitys.current.points.forEach((item, index) => {
                        item.index = index
                    })
                    CesiumEntitys.current.positions.splice(((-move_index.current) * 3), 0, longitude, latitude, height)

                    const point1 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[(-move_index.current) * 3], CesiumEntitys.current.positions[(-move_index.current) * 3 + 1], CesiumEntitys.current.positions[(-move_index.current) * 3 + 2]) // 第一个点的坐标（x、y、z）
                    let point2 = null
                    if ((-move_index.current) === CesiumEntitys.current.points.length - 1) {
                        point2 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[0], CesiumEntitys.current.positions[1], CesiumEntitys.current.positions[2]) // 第二个点的坐标（x、y、z）
                    } else {
                        point2 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[(-move_index.current) * 3 + 3], CesiumEntitys.current.positions[(-move_index.current) * 3 + 4], CesiumEntitys.current.positions[(-move_index.current) * 3 + 5]) // 第二个点的坐标（x、y、z）
                    }
                    let Midpoint1 = viewer.current.entities.add({
                        name: '中点',
                        index: (move_index.current),
                        position: midpointCoordinates(point1, point2),
                        point: {
                            color: Cesium.Color.WHITE,
                            pixelSize: 8,
                            disableDepthTestDistance: Number.POSITIVE_INFINITY,
                            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                        },
                    })
                    let point3 = null
                    if ((-move_index.current - 1) === 0) {
                        point3 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[CesiumEntitys.current.positions.length - 3], CesiumEntitys.current.positions[CesiumEntitys.current.positions.length - 2], CesiumEntitys.current.positions[CesiumEntitys.current.positions.length - 1]) // 第二个点的坐标（x、y、z）
                    } else {
                        point3 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[(-move_index.current) * 3 - 3], CesiumEntitys.current.positions[(-move_index.current) * 3 - 2], CesiumEntitys.current.positions[(-move_index.current) * 3 - 1]) // 第二个点的坐标（x、y、z）
                    }
                    let Midpoint2 = viewer.current.entities.add({
                        name: '中点',
                        index: (move_index.current - 1),
                        position: midpointCoordinates(point1, point3),
                        point: {
                            color: Cesium.Color.WHITE,
                            pixelSize: 8,
                            disableDepthTestDistance: Number.POSITIVE_INFINITY,
                            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                        },
                    })
                    Midpoint_cesiumObjectEntity_Array.current.splice((-move_index.current - 1), 0, Midpoint2, Midpoint1)
                    Midpoint_cesiumObjectEntity_Array.current.forEach((item, index) => {
                        item.index = -index - 1
                    })
                    CesiumEntitys.current.points.forEach((item, index) => {
                        item.point.color = Cesium.Color.WHITE
                    })
                    Midpoint_cesiumObjectEntity_Array.current.forEach((item, index) => {
                        item.point.color = Cesium.Color.WHITE
                    })
                    move_index.current = -move_index.current
                }
            }
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    }
    //右击事件
    function RIGHT_CLICK() {
        handlerPoint.current.setInputAction(async function (e) {
            var pickedObject = viewer.current.scene.pick(e.position);
            if (pickedObject && pickedObject.id && pickedObject.id.name === '节点') {
                delete_node(pickedObject.id.index)
            } else if (pickedObject && pickedObject.id && pickedObject.id.name === '中点') {
                var ray = viewer.current.camera.getPickRay(e.position);
                var cartesian = viewer.current.scene.globe.pick(ray, viewer.current.scene);
                let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(cartesian, viewer.current)
                move_index.current = Number(pickedObject.id.index)
                if (move_index.current < 0) {
                    Midpoint_cesiumObjectEntity_Array.current[-move_index.current - 1].position.setValue(new Cesium.Cartesian3.fromDegrees(longitude, latitude, globeHeight))
                    Midpoint_cesiumObjectEntity_Array.current[-move_index.current - 1].point.pixelSize.setValue(12)
                    Midpoint_cesiumObjectEntity_Array.current[-move_index.current - 1].name = '节点'
                    CesiumEntitys.current.points.splice((-move_index.current), 0, Midpoint_cesiumObjectEntity_Array.current[-move_index.current - 1])
                    Midpoint_cesiumObjectEntity_Array.current.splice((-move_index.current - 1), 1)
                    CesiumEntitys.current.points.forEach((item, index) => {
                        item.index = index
                    })
                    CesiumEntitys.current.positions.splice(((-move_index.current) * 3), 0, longitude, latitude, height)
                    const point1 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[(-move_index.current) * 3], CesiumEntitys.current.positions[(-move_index.current) * 3 + 1], CesiumEntitys.current.positions[(-move_index.current) * 3 + 2]) // 第一个点的坐标（x、y、z）
                    let point2 = null
                    if ((-move_index.current) === CesiumEntitys.current.points.length - 1) {
                        point2 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[0], CesiumEntitys.current.positions[1], CesiumEntitys.current.positions[2]) // 第二个点的坐标（x、y、z）
                    } else {
                        point2 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[(-move_index.current) * 3 + 3], CesiumEntitys.current.positions[(-move_index.current) * 3 + 4], CesiumEntitys.current.positions[(-move_index.current) * 3 + 5]) // 第二个点的坐标（x、y、z）
                    }
                    let Midpoint1 = viewer.current.entities.add({
                        name: '中点',
                        index: (move_index.current),
                        position: midpointCoordinates(point1, point2),
                        point: {
                            color: Cesium.Color.WHITE,
                            pixelSize: 8,
                            disableDepthTestDistance: Number.POSITIVE_INFINITY,
                            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                        },
                    })
                    let point3 = null
                    if ((-move_index.current - 1) === 0) {
                        point3 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[CesiumEntitys.current.positions.length - 3], CesiumEntitys.current.positions[CesiumEntitys.current.positions.length - 2], CesiumEntitys.current.positions[CesiumEntitys.current.positions.length - 1]) // 第二个点的坐标（x、y、z）
                    } else {
                        point3 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[(-move_index.current) * 3 - 3], CesiumEntitys.current.positions[(-move_index.current) * 3 - 2], CesiumEntitys.current.positions[(-move_index.current) * 3 - 1]) // 第二个点的坐标（x、y、z）
                    }
                    let Midpoint2 = viewer.current.entities.add({
                        name: '中点',
                        index: (move_index.current - 1),
                        position: midpointCoordinates(point1, point3),
                        point: {
                            color: Cesium.Color.WHITE,
                            pixelSize: 8,
                            disableDepthTestDistance: Number.POSITIVE_INFINITY,
                            heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                        },
                    })
                    Midpoint_cesiumObjectEntity_Array.current.splice((-move_index.current - 1), 0, Midpoint2, Midpoint1)
                    Midpoint_cesiumObjectEntity_Array.current.forEach((item, index) => {
                        item.index = -index - 1
                    })
                    CesiumEntitys.current.points.forEach((item, index) => {
                        item.point.color = Cesium.Color.WHITE
                    })
                    Midpoint_cesiumObjectEntity_Array.current.forEach((item, index) => {
                        item.point.color = Cesium.Color.WHITE
                    })
                    move_index.current = -move_index.current
                }
            } else {
                if (CesiumEntitys.current.positions.length < 9) {
                    message.error(`面状航线至少三个点`);
                    return
                }
                viewer.current.entities.remove(CesiumEntitys.current.polyline_CesiumEntity)
                CesiumEntitys.current.polyline_CesiumEntity = viewer.current.entities.add({
                    name: `标注线`,
                    polyline: {
                        show: true,
                        positions: new Cesium.CallbackProperty(() => {
                            return new Cesium.Cartesian3.fromDegreesArrayHeights([...CesiumEntitys.current.positions, CesiumEntitys.current.positions[0], CesiumEntitys.current.positions[1], CesiumEntitys.current.positions[2]]);
                        }, false),
                        // 宽度
                        width: 5,
                        // 线的颜色
                        material: Cesium.Color.fromCssColorString('#409EFF'),
                        clampToGround: true,
                        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                    },
                    polygon: {
                        hierarchy: new Cesium.CallbackProperty(() => {
                            return new Cesium.PolygonHierarchy(new Cesium.Cartesian3.fromDegreesArrayHeights([...CesiumEntitys.current.positions]))
                        }, false),
                        // 宽度
                        width: 5,
                        // 线的颜色
                        material: Cesium.Color.fromCssColorString('#409EFF').withAlpha(0.3),
                        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
                    },
                })
                GenerateMidpoint()
                creatArea()
            }
        }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    }
    // 生成多边形各边中点
    function GenerateMidpoint() {
        for (let i = 0; i < CesiumEntitys.current.positions.length; i += 3) {
            const point1 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[i], CesiumEntitys.current.positions[i + 1], CesiumEntitys.current.positions[i + 2]) // 第一个点的坐标（x、y、z）
            let point2 = null
            if (i < CesiumEntitys.current.positions.length - 3) {
                point2 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[i + 3], CesiumEntitys.current.positions[i + 4], CesiumEntitys.current.positions[i + 5]) // 第二个点的坐标（x、y、z）
            } else {
                point2 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[0], CesiumEntitys.current.positions[1], CesiumEntitys.current.positions[2]) // 第二个点的坐标（x、y、z）
            }
            Midpoint_cesiumObjectEntity_Array.current.push(viewer.current.entities.add({
                name: '中点',
                index: ((Midpoint_cesiumObjectEntity_Array.current.length + 1) * -1),
                position: midpointCoordinates(point1, point2),
                point: {
                    color: Cesium.Color.WHITE,
                    pixelSize: 8,
                    disableDepthTestDistance: Number.POSITIVE_INFINITY,
                    heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                },
            }))
        }
    }
    // 计算中点坐标
    function midpointCoordinates(pointA, pointB,) {
        const centerPosition2 = new Cesium.Cartesian3()
        Cesium.Cartesian3.midpoint(pointA, pointB, centerPosition2)
        return centerPosition2
    }
    // 销毁函数
    function destroy() {
        viewer.current.entities.removeAll();
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    }
    // 创建或更新Area
    async function creatArea() {
        let AreaPoint = []
        for (let i = 0; i < CesiumEntitys.current.positions.length; i += 3) {
            AreaPoint.push([CesiumEntitys.current.positions[i], CesiumEntitys.current.positions[i + 1], CesiumEntitys.current.positions[i + 2]])
        }
        let interval = computeInterval(model.current, wayline.current.GSD, wayline.current.side_rate)
        let res3 = await computePlanarRoute(viewer.current, CesiumEntitys.current.positions, interval, wayline.current.fly_height, wayline.current.center_line_angle)
        waylineChange({
            ...wayline.current,
            AreaPoint: AreaPoint,
            shoot_time: computeIntervalTime(model.current, wayline.current.GSD, wayline.current.line_rate, wayline.current.GlobalSpeed),
            PList: computeAngle(res3, false).map((item, index) => {
                return {
                    Index: index,
                    Lat: item[1],
                    Lng: item[0],
                    Height: item[2],
                    Action: '',
                    ActionList: [],
                    PName: ``,
                    PType: '',
                    Speed: wayline.current.GlobalSpeed,
                    IfStop: true,
                    global_height: '',
                    ground_height: '',
                    waypoint_heading_angle: item[3],
                }
            })
        })
        wayline_position.current = [...res3].flat(Infinity)
        if (!wayline_polyline.current) {
            wayline_polyline.current = viewer.current.entities.add({
                polyline: {
                    positions: new Cesium.CallbackProperty(() => {
                        return new Cesium.Cartesian3.fromDegreesArrayHeights(wayline_position.current);
                    }, false),
                    // 宽度
                    width: 3,
                    // 线的颜色
                    material: Cesium.Color.fromCssColorString('#0aed8a'),
                    clampToGround: new Cesium.CallbackProperty(() => {
                        return Cesium.Math.toDegrees(viewer.current.scene.camera.pitch) <= -80 ? true : false;
                    }, false),
                    show: new Cesium.CallbackProperty(() => {
                        return cursor.current === "move" ? false : true
                    }, false),
                },
                position: new Cesium.CallbackProperty(() => {
                    return Cesium.Cartesian3.fromDegrees(wayline_position.current[0], wayline_position.current[1], wayline_position.current[2]);
                }, false),
                billboard: {
                    image: PointImg3('s'),
                    color: Cesium.Color.WHITE,
                    scale: 1,
                    verticalOrigin: Cesium.VerticalOrigin.BOTTOM,  //垂直位置
                    horizontalOrigin: Cesium.HorizontalOrigin.CENTER,//水平位置
                    heightReference: new Cesium.CallbackProperty(() => {
                        return Cesium.Math.toDegrees(viewer.current.scene.camera.pitch) <= -80 ? Cesium.HeightReference.CLAMP_TO_GROUND : Cesium.HeightReference.NONE;
                    }, false),
                    show: new Cesium.CallbackProperty(() => {
                        return cursor.current === "move" ? false : true
                    }, false),
                },
            })
        }
    }
    // 删除节点
    function delete_node(index) {
        if (CesiumEntitys.current.points.length <= 3) {
            message.error(`面状航线至少需要三个点`);
            return
        }
        let point1 = null
        let point2 = null
        if (index === 0) {
            point1 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[3], CesiumEntitys.current.positions[4], CesiumEntitys.current.positions[5])
            point2 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[CesiumEntitys.current.positions.length - 3], CesiumEntitys.current.positions[CesiumEntitys.current.positions.length - 2], CesiumEntitys.current.positions[CesiumEntitys.current.positions.length - 1])
        } else if (index === CesiumEntitys.current.points.length - 1) {
            point1 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[0], CesiumEntitys.current.positions[1], CesiumEntitys.current.positions[2])
            point2 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[CesiumEntitys.current.positions.length - 6], CesiumEntitys.current.positions[CesiumEntitys.current.positions.length - 5], CesiumEntitys.current.positions[CesiumEntitys.current.positions.length - 4])
        } else {
            point1 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[(index + 1) * 3], CesiumEntitys.current.positions[(index + 1) * 3 + 1], CesiumEntitys.current.positions[(index + 1) * 3 + 2])
            point2 = Cesium.Cartesian3.fromDegrees(CesiumEntitys.current.positions[(index - 1) * 3], CesiumEntitys.current.positions[(index - 1) * 3 + 1], CesiumEntitys.current.positions[(index - 1) * 3 + 2])
        }
        let midpoint = midpointCoordinates(point1, point2)
        viewer.current.entities.remove(CesiumEntitys.current.points[index])
        CesiumEntitys.current.points.splice(index, 1)
        CesiumEntitys.current.positions.splice(index * 3, 3)
        CesiumEntitys.current.points.forEach((item, index) => {
            item.index = index
        })
        if (index === 0) {
            Midpoint_cesiumObjectEntity_Array.current[Midpoint_cesiumObjectEntity_Array.current.length - 1].position.setValue(midpoint)
        } else {
            Midpoint_cesiumObjectEntity_Array.current[index - 1].position.setValue(midpoint)
        }
        viewer.current.entities.remove(Midpoint_cesiumObjectEntity_Array.current[index])
        Midpoint_cesiumObjectEntity_Array.current.splice(index, 1)
        Midpoint_cesiumObjectEntity_Array.current.forEach((item, index) => {
            item.index = -index - 1
        })
        move_entity_index.current = null
        move_index.current = null
        creatArea()
    }
    // 航线信息改变
    function waylineChange(data) {
        wayline.current = data
        getWayline && getWayline(wayline.current)
    }
    // 修改航线信息
    function setWayline(data) {
        wayline.current = data
        getWayline && getWayline(wayline.current)
        wayline_polyline.current && creatArea()
    }
    // 飞机型号改变
    function modelChange(data) {
        model.current = data
        setModel(model.current)
        let drone = modelToEnum(model.current)
        wayline.current.drone_enum = drone.drone_enum
        wayline.current.payload_enum = drone.payload_enum
        wayline.current.GSD = computeGSD(model.current, wayline.current.fly_height)
        getWayline && getWayline(wayline.current)
    }
    // 航线回显
    function RouteFeedback(wayLineInfo) {
        wayLineInfo.AreaPoint.forEach((item, index) => {
            CesiumEntitys.current.positions.push(...item)
            CesiumEntitys.current.points.push(viewer.current.entities.add({
                name: `节点`,
                index: CesiumEntitys.current.points.length,
                position: new Cesium.Cartesian3.fromDegrees(...item),
                point: {
                    show: true,
                    pixelSize: 10,
                    color: Cesium.Color.fromCssColorString('#fff'), // 点的颜色
                    heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                },
            }))
        })
        CesiumEntitys.current.polyline_CesiumEntity = viewer.current.entities.add({
            name: `标注线`,
            polyline: {
                show: true,
                positions: new Cesium.CallbackProperty(() => {
                    return new Cesium.Cartesian3.fromDegreesArrayHeights([...CesiumEntitys.current.positions, CesiumEntitys.current.positions[0], CesiumEntitys.current.positions[1], CesiumEntitys.current.positions[2]]);
                }, false),
                // 宽度
                width: 5,
                // 线的颜色
                material: Cesium.Color.fromCssColorString('#409EFF'),
                clampToGround: true,
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
            },
            polygon: {
                hierarchy: new Cesium.CallbackProperty(() => {
                    return new Cesium.PolygonHierarchy(new Cesium.Cartesian3.fromDegreesArrayHeights([...CesiumEntitys.current.positions]))
                }, false),
                // 宽度
                width: 5,
                // 线的颜色
                material: Cesium.Color.fromCssColorString('#409EFF').withAlpha(0.3),
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
            },
        })
        GenerateMidpoint()
        wayline_position.current = [wayLineInfo.PList.map((item, index) => {
            return [item.Lng, item.Lat, item.Height]
        })].flat(Infinity)
        wayline_polyline.current = viewer.current.entities.add({
            polyline: {
                positions: new Cesium.CallbackProperty(() => {
                    return new Cesium.Cartesian3.fromDegreesArrayHeights(wayline_position.current);
                }, false),
                // 宽度
                width: 3,
                // 线的颜色
                material: Cesium.Color.fromCssColorString('#0aed8a'),
                clampToGround: new Cesium.CallbackProperty(() => {
                    return Cesium.Math.toDegrees(viewer.current.scene.camera.pitch) <= -80 ? true : false;
                }, false),
                show: new Cesium.CallbackProperty(() => {
                    return cursor.current === "move" ? false : true
                }, false),
            },
            position: new Cesium.CallbackProperty(() => {
                return Cesium.Cartesian3.fromDegrees(wayline_position.current[0], wayline_position.current[1], wayline_position.current[2]);
            }, false),
            billboard: {
                image: PointImg3('s'),
                color: Cesium.Color.WHITE,
                scale: 1,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,  //垂直位置
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,//水平位置
                heightReference: new Cesium.CallbackProperty(() => {
                    return Cesium.Math.toDegrees(viewer.current.scene.camera.pitch) <= -80 ? Cesium.HeightReference.CLAMP_TO_GROUND : Cesium.HeightReference.NONE;
                }, false),
                show: new Cesium.CallbackProperty(() => {
                    return cursor.current === "move" ? false : true
                }, false),
            },
        })
    }
    return {
        wayline,
        setWayline,
        modelChange,
        RouteFeedback,
        waylineChange
    }
}
export default usePlanarRouteHooks;