import styles from "./login.less";
import { LockOutlined, UserOutlined } from "@ant-design/icons";
import { Form, Input, Button, message, Checkbox } from "antd";
import { useEffect, useState } from "react";
import { history, useSearchParams } from "umi";
import { HGet2 } from "@/utils/request";
import { isEmpty } from "@/utils/utils";
import {  axiosApi } from "@/services/general";
import "./login.css"
export default function LoginPage() {
  const [load, setIsload] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();
  let [loginName,setLoginName] = useState("")
  const [form] = Form.useForm()
  let system = searchParams.get("system") || "si";

  const submitData = (data) => {
    if (isEmpty(data)) return;
    if (!isEmpty(data.err)) {
      message.info(data.err);
      return;
    }

    localStorage.setItem("token", data.token);
    localStorage.setItem("user", JSON.stringify(data.user));
    localStorage.setItem("orgId", data.user.OrgCode);
    localStorage.setItem("orgName", data.user.OrgName);
    localStorage.setItem("system", system);

    if (window.location.href.indexOf("token") > 0) {
      let url = new URL(window.location.href);
      url.searchParams.delete("token");
      url.searchParams.delete("json");
      url.searchParams.delete("system");
      window.location.href = url.href.split("#")[0] + "#/HOME/login?system=";
    } else {
      history.replace('/SI/index');
    }
    console.log(data);
  };
  const handleSubmit = async (e) => {
    const data = await HGet2(
      "/api/v2/User/Login?userName=" + e.userName + "&password=" + e.password
    );
    submitData(data);
  };

  useEffect(() => {
    const isRemeber = localStorage.getItem('isRemeber')
    form.setFieldValue('isChecked',JSON.parse(isRemeber||false))


    const getByToken = async () => {
      const params = new URLSearchParams(window.location.search);
      const p1 = params.get("api_token");
      if (isEmpty(p1)) {
        setIsload(true);
        return;
      }
      // const data=await Post3('/api/v2/User/Login2',p1);
      const data = await HGet2("/api/v2/User/LoginByTokenDFM?token=" + p1);
      submitData(data);
      setIsload(true);
    };

    getByToken();
    localStorage.removeItem("PageIndexTitle");

      getLogin()
  }, []);


// 获取登录页名字
const getLogin = async ()=>{
  let res = await axiosApi("/api/v2/APPConfig/Get", "GET");
  setLoginName(res.app.title)
}


const checkChange = (value)=>{
  localStorage.setItem('isRemeber',value.target.checked)
}


  const rederForm = () => {
    return (
      <Form
        form={form}
        name="basic-login"
        initialValues={{ remember: true, agreement: true }}
        onFinish={handleSubmit}
        autoComplete="off"
        style={{
          display: "flex",
          justifyContent: "space-around",
          flexDirection: "column",
          gap: "10px",
        }}
      >
        <Form.Item>
          <div className={styles['login-page-body-form-title']}>
            <span className={styles['titleZc']}>欢迎登录</span>
            <span className={styles['titleEn']}>Welcome</span>
          </div>
        </Form.Item>
        <Form.Item
          name="userName"
          rules={[{ required: true, message: "请输入用户名" }]}
        >
          <Input
            size="large"
            prefix={
              <UserOutlined
                className="site-form-item-icon"
                style={{ color: "white" }}
              />
            }
            placeholder="账户"
            className={styles['inputStyle']}
          />
        </Form.Item>
        <Form.Item
          name="password"
          rules={[{ required: true, message: "请输入密码" }]}
        >
          <Input.Password
            size="large"
            prefix={
              <LockOutlined
                className="site-form-item-icon"
                style={{ color: "white" }}
              />
            }
            placeholder="密码"
             className={styles['inputStyle']}
          />

        </Form.Item>
        <Form.Item name="isChecked"  valuePropName="checked">
          <Checkbox className="remeberPW" onChange={checkChange}>记住密码</Checkbox>
        </Form.Item>
        <Form.Item>
          {/* <Button
            type="primary"
            htmlType="submit"
            block
            size="large"
            onMouseEnter={(e) => {
              e.currentTarget.style.background = "#00a771";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = "#4aa2f8";
            }}
            style={{ background: "#4aa2f8" }}
          >
            登录
          </Button> */}
          <Button
            type="primary"
            htmlType="submit"
            block
            size="large"
            className={styles["login-bt"]}
          >
            登录
          </Button>
        </Form.Item>
      </Form>
    );
  };
  return (
    <div className={styles["login-page"]}>
      {/* <div className={styles["login-page-head"]}>
        云端智行-无人机行业应用平台
      </div> */}
      <div className={styles["login-page-body"]}>
        <div className={styles["login-page-head"]}>
         {loginName}
        </div>
        <div className={styles["login-page-body-form"]}>

          {rederForm()}
        </div>
      </div>
      {/* <div className={styles["login-page-foot"]}>成都北斗众联科技有限公司</div> */}
    </div>
  );
}
