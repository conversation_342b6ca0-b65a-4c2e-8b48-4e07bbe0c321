import { Input, Form, Radio, Button, Modal, message, Select } from "antd";
import { useState, useEffect } from "react";
import { isEmpty } from "@/utils/utils";
import "dayjs/locale/zh-cn";
import { HPost2 } from "@/utils/request";
import { Get2 } from "@/services/general";
import { SM4Util } from "sm4util";

const userTypeList = ["系统管理员", "无人机飞手", "普通用户"];

const GetDepartment = () => {};

const CronAddForm = (props) => {
  const { UserList, sn, refrush } = props;
  const [ph, setPH] = useState("");
  const [password, setPassword] = useState("");
  const [userNM, setUserName] = useState("");
  const [name, setName] = useState("");
  const [uType, setUType] = useState("无人机飞手");
  const [dList, setDList] = useState([]);
  const [depart, setDpart] = useState({});

  const getDList = async () => {
    let pst = await Get2("/api/v1/Department/GetAllList", {});
    if (isEmpty(pst)) pst = [];
    setDList(pst);
  };

  useEffect(() => {
    getDList();
  }, []);

  const GetSM4 = (data) => {
    const sm4 = new SM4Util();
    const miStr1 = sm4.encryptDefault_ECB(data);
    return miStr1;
  };
  function clearAll() {
    setPH(null);
    setPassword(null);
    setUserName(null);
    setName(null);
    setUType(null);
  };
  const onSave = async (e) => {
    // const user = JSON.parse(localStorage.getItem("user"));

    // console.log("CronAddForm", user);
    // if (isEmpty(depart)) {
    //   message.info("先选择部门");
    //   return;
    // }
    if (!name) {
      return message.warning("请输入用户名称");
    }
    for (let item of UserList) {
      if (item.Name === name) {
        return message.warning("用户名称重复,请换一个试试");
      }
    }
    if (!uType) {
      return message.warning("请选择用户权限");
    }
    if (!userNM) {
      return message.warning("请输入登录名");
    }
    if (!password) {
      return message.warning("请输入登录密码");
    }
    const phoneRegex = /^1\d{10}$/;
    if (ph && !phoneRegex.test(ph)) {
      message.warning("请输入有效电话号码!");
      return;
    }

    const data = {
      Name: name,
      UserName: userNM,
      Password: password,
      Phone: ph,
      Authority: uType,
      DepartCode: depart.DCode,
      DepartName: depart.DName,
    };

    const xx = await HPost2("/api/v1/UserInfo/Add", data);
    if (isEmpty(xx.err)) {
      message.success("创建成功！");
      clearAll();
    }else{
      message.error(xx.err);
    }
    refrush();
  };

  const getTypeList = (xL) => {
    const list = [];
    xL.map((p) => {
      list.push(<Radio value={p}>{p}</Radio>);
    });
    return list;
  };

  const onDT = (values) => {
    setDt(values);
  };

  const onName = (e) => {
    console.log("onName values of form: ", e.target.value);
    setName(e.target.value);
  };

  const getDSelect = (dList) => {
    const list = [];
    dList.forEach((e) => {
      list.push(
        <Select.Option key={e.DCode} data={e} value={e.DCode}>
          {e.DName}
        </Select.Option>
      );
    });
    console.log("CronAddForm", list);

    return list;
  };

  const onChange = (values) => {
    const xx = dList.find((item) => {
      return item.DCode === values;
    });
    console.log("onChange values of form: ", xx);
    setDpart(xx);
  };

  return (
    <Form
      labelCol={{
        span: 4,
      }}
      wrapperCol={{
        span: 18,
      }}
      layout="horizontal"
      style={{
        maxWidth: 600,
      }}
    >
      {/* <Form.Item label="所属部门">
        <Select onSelect={onChange}>{getDSelect(dList)}</Select>
      </Form.Item> */}

      <Form.Item label="用户姓名">
        <Input onChange={(e) => setName(e.target.value)}></Input>
      </Form.Item>
      <Form.Item label="用户权限">
        <Radio.Group
          defaultValue={uType}
          onChange={(e) => {
            setUType(e.target.value);
          }}
        >
          {getTypeList(userTypeList)}
        </Radio.Group>
      </Form.Item>
      <Form.Item label="登录名">
        <Input onChange={(e) => setUserName(e.target.value)}></Input>
      </Form.Item>

      <Form.Item label="密码">
        <Input onChange={(e) => setPassword(e.target.value)}></Input>
      </Form.Item>

      <Form.Item label="电话">
        <Input onChange={(e) => setPH(e.target.value)}></Input>
      </Form.Item>

      <Form.Item label={" "} colon={false}>
        <Button type="primary" onClick={onSave}>
          保存
        </Button>
      </Form.Item>
    </Form>
  );
};

export default CronAddForm;
