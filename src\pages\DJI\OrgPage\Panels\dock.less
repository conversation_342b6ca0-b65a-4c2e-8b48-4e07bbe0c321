/* 滚动条整体样式 */
.wrapper::-webkit-scrollbar {
  width: 5px;
}

/* 滚动条thumb(滑块)样式 */
.wrapper::-webkit-scrollbar-thumb {
  border-radius: 50%; /* 圆角 */
  background-color: rgba(118, 220, 251, 0.886);
  background: linear-gradient(to bottom, rgba(118, 220, 251, 0.301), #0ac8b0);
}

/* 滚动条hover状态下thumb(滑块)样式 */
.wrapper::-webkit-scrollbar-thumb:hover {
  background-color: rgba(118, 220, 251, 0.553);
}

/* 滚动条上下箭头样式 */
.wrapper::-webkit-scrollbar-button {
  background-color: #ccc;
  display: none;
}

/* 滚动条左右箭头样式 */
.wrapper::-webkit-scrollbar-button:start:decrement,
.wrapper::-webkit-scrollbar-add-button {
  display: none;
}

.wrapper::-webkit-scrollbar-button:end:increment,
.wrapper::-webkit-scrollbar-sub-button {
  display: none;
}

.section {
  width: 100%;
  margin-bottom: 12px;
}

.sectionTitle {
  color: white;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  padding: 5px 10px 10px;
  margin: 0 10px 10px 10px;
  // background-color: rgb(90 132 147 / 80%);
  // border-radius: 4px 4px 0 0;
  // border: 1px solid #0b8f75;
  // 背景图
  background-image: url('../../../SI/assets/image/title_bg.png');
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-position: 0 10px;
}

.sectionContent {
  padding: 0 10px;
}

.airportSection {
  height: 100%;
  // overflow-y: auto;
}

.airportList {
  // display: flex;
  // flex-direction: column;
  // gap: 8px;
  max-height: calc(100% - 40px);
  overflow-y: auto;
}

.airportContainer {
  // background: rgba(0, 66, 128, 0.4);
  border-radius: 4px;
  width: 100%;
  transition: all 0.3s ease;
  overflow: hidden;
  margin: 4px 0;
}

.airportContainer.expanded {
  // background: rgba(0, 66, 128, 0.6);
}

.airportItem {
  display: grid;
  grid-template-columns: 40px minmax(60px, 150px) 40px 40px 30px 30px 30px;
  column-gap: 5px;
  align-items: center;
  padding: 10px 15px;
  color: white;
  cursor: pointer;
  position: relative;
  height: 50px;
  background-image: url('../../../SI/assets/image/airportItem_bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  // background-position: -22px 0;
  
  /* 离线状态样式 */
  &.offline {
    background-image: url('../../../SI/assets/image/airportItem_bg_offline.png');
    cursor: not-allowed;
    opacity: 0.7;
  }
}

.airportIcon {
  width: 24px;
  height: 13px;
  justify-self: start;
  background-image: url('../../../SI/assets/image/airportItem.svg');
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-position: 0 100%;
  display: inline-block;
  grid-column: 1;
}

.airportName {
  font-size: 14px;
  font-weight: bold;
  justify-self: start;
  grid-column: 2;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
  display: block;
  padding-right: 5px;
  box-sizing: border-box;
}

.airportName:hover {
  cursor: pointer;
  color: #4dabd9;
}

/* 离线状态下禁用名称点击 */
.airportItem.offline .airportName:hover {
  cursor: not-allowed;
  color: white;
}

.airportOnline {
  color: #52c41a;
  background: rgba(82, 196, 26, 0.2);
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  justify-self: center;
  grid-column: 3;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  // 离线状态
  &.offline {
    color: #ff4d4f;
    background: rgba(255, 77, 79, 0.2);
  }
}

/* 离线状态的颜色 */
// .offline {
//   color: #ff4d4f !important;
//   background: rgba(255, 77, 79, 0.2) !important;
// }

.airportOnline:empty {
  display: none;
}

.airportStatus {
  color: white;
  background: rgba(24, 144, 255, 0.2);
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  justify-self: center;
  grid-column: 4;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  // width: 100%;
}

.airportStatus.flying {
  color: #faad14;
  background: rgba(250, 173, 20, 0.2);
}

.airportLocation {
  background-image: url('../../../SI/assets/image/fly.png');
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-position: 0 100%;
  width: 24px;
  height: 24px;
  justify-self: center;
  grid-column: 5;
  &:hover {
    opacity: 0.8;
    transform: scale(1.1);
    transition: all 0.3s ease;
  }
  
  /* 离线状态样式 */
  &.offline {
    background-image: url('../../../SI/assets/image/fly_offline.png');
    cursor: not-allowed;
    opacity: 0.6;
    
    &:hover {
      opacity: 0.6;
      transform: none;
    }
  }
}

.videoIcon {
  background-image: url('../../../SI/assets/image/camera.png');
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-position: 0 100%;
  width: 24px;
  height: 24px;
  justify-self: center;
  cursor: pointer;
  grid-column: 6;
  &:hover {
    opacity: 0.8;
    transform: scale(1.1);
    transition: all 0.3s ease;
  }
  
  /* 离线状态样式 */
  &.offline {
    background-image: url('../../../SI/assets/image/camera_offlone.png');
    cursor: not-allowed;
    opacity: 0.6;
    
    &:hover {
      opacity: 0.6;
      transform: none;
    }
  }
}

.expandIcon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  justify-self: center;
  grid-column: 7;
  
  /* 离线状态禁用展开 */
  &.offline {
    cursor: not-allowed;
    opacity: 0.6;
    
    &:hover {
      opacity: 0.6;
    }
  }
}

.routesList {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 15px 15px;
  max-height: 300px; // 子项的height
  overflow-y: auto;
  margin: 5px 0;
  background-image: url('../../../SI/assets/image/airportItem_airline_bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.routeInfo {
  width: 100%;
}

.routeHeader {
  display: flex;
  align-items: center;
  position: relative;
  margin-bottom: 8px;
}

.routeName {
  color: white;
  font-size: 14px;
  font-weight: bold;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.routeBtns {
  position: absolute;
  right: 0;
  top: 0;
  display: flex;
  gap: 8px;
}

.viewRouteBtn {
  background: #1890ff;
  border-color: #1890ff;
}

.executeBtn {
  background: #52c41a;
  border-color: #52c41a;
}

.routeDetails {
  margin-left: 0;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.routeDescRow,
.routeTimeRow,
.routeStatusRow {
  display: flex;
  align-items: flex-start;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

.routeLabel {
  width: 70px;
  color: rgba(255, 255, 255, 0.6);
}

.routeDesc,
.routeTime,
.routeStatusValue {
  flex: 1;
}

.noRoutes {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30px 0;
  
  :global {
    .ant-spin {
      color: rgba(255, 255, 255, 0.85);
    }
    .ant-spin-nested-loading {
      width: 100%;
    }
    .ant-spin-container {
      width: 100%;
    }
  }
}

.airportStats {
  display: flex;
  justify-content: space-around;
  text-align: center;
  padding: 10px 0;
  height: 16vh;

  .airportStatBox {
    background-image: url('../../../SI/assets/image/circle-bottom.png');
    background-size: 100% auto;
    background-repeat: no-repeat;
    background-position: 0 100%;
    padding: 18px 22px;
    margin-bottom: 10px;
    .statFigure {
      font-size: 24px;
      font-weight: bold;
      color: #409eff;
      margin: 5px 0;
    }
    .statSubLabel {
      font-size: 12px;
      color: #a0a0a0;
    }
  }
}
