import { useState,useEffect } from "react";
import LoadPanel from "@/components/IfShowPanel/load_panel";
import { isEmpty, getBodyH } from "@/utils/utils";
import LastPageButton from "@/components/LastPageButton";
import { message, Card, Modal, Table, Space, Tag, Select, Input } from "antd";
import { axiosApi } from "@/services/general";
import { timeFormat, getGuid } from "@/utils/helper";
import EditTable from "./edit_table";

const FireIncidentTask = () => {
  const [ifLoad, setIfLoad] = useState(true);
  const [orgCodeList, setOrgCodeList] = useState("");
  const [allList, setAllList] = useState([]);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const onSearch = (value, _e, info) => {};
  useEffect(() => {
    GetByOrgCode();
  }, []);
  async function GetList(currentPage, currentSize, status, source) {
    //查询事件列表
    if (!currentPage || !currentSize || !status || !source) return;
    let res = await axiosApi("api/v1/ThirdPartyObject/GetList", "GET", {
      page: currentPage,
      pageSize: currentSize,
      status: status,
      source: source,
    });
    if (res.code === 1) {
      setAllList(res.data);
      setIfLoad(false);
    } else {
      message.warning(res.msg);
      setIfLoad(false);
    }
  }
  async function GetAllList(currentPage, currentSize, status) {
    //查询所有事件列表
    if (!currentPage || !currentSize || !status) return;
    let res = await axiosApi("api/v1/ThirdPartyObject/GetAllList", "GET", {
      page: currentPage,
      pageSize: currentSize,
      status: status,
    });
    if (res.code === 1) {
      setAllList(res.data);
      setIfLoad(false);
    } else {
      message.warning(res.msg);
      setIfLoad(false);
    }
  }
  async function GetByOrgCode() {
    let user = JSON.parse(localStorage.getItem("user"));
    let orgCode = user?.OrgCode;
    if (!orgCode) return;
    let res = await axiosApi(
      `api/v1/ThirdPartyInfo/GetByOrgCode/${orgCode}`,
      "GET"
    );
    if (res.code === 1) {
      setOrgCodeList(res.data);
     handleTableChange();
    }
  }
  const handleTableChange = (pagination) => {
    setPage(1);
    setPageSize(10);
    GetAllList(page, pageSize, -1);
  };
  function statusText(status) {
    switch (status) {
      case 0:
        return "不可用";
      case 1:
        return "初始化";
      case 2:
        return "已接收";
      case 3:
        return "执行中";
      case 4:
        return "已完成";
      case 5:
        return "已取消";
      default:
        return "";
    }
  }
  const TableCols = () => {
    return [
      {
        title: "序列",
        dataIndex: "id",
        key: "id",
        align: "center",
      },
      {
        title: "经纬度",
        align: "center",
        render: (record) => (
          <Space size="middle">{`${record.longitude} , ${record.latitude}`}</Space>
        ),
      },
      {
        title: "高度",
        dataIndex: "altitude",
        key: "altitude",
        align: "center",
      },
      {
        title: "事件",
        dataIndex: "event",
        key: "event",
        align: "center",
      },
      {
        title: "来源",
        dataIndex: "source",
        key: "source",
        align: "center",
      },
      {
        title: "创建时间",
        key: "created_at",
        align: "center",
        render: (record) => <span>{timeFormat(record.created_at)}</span>,
        // dataIndex: "created_at",
      },
      {
        title: "更新时间",
        key: "updated_at",
        align: "center",
        render: (record) => <span>{timeFormat(record.updated_at)}</span>,
        // dataIndex: "updated_at",
      },
      {
        title: "状态",
        key: "status",
        align: "center",
        render: (record) => <span>{statusText(record.status)}</span>,
      },
      {
        title: "地图操作",
        align: "center",
        render: (record) => (
          <Space size="middle">
            <Tag>
              <EditTable
                mapList={allList}
                record={record}
                refrush={handleTableChange}
                key={getGuid()}
              ></EditTable>
            </Tag>
          </Space>
        ),
      },
    ];
  };

  let extra = (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        width: "100%",
        gap: "10px",
      }}
    >
      {/* <Search
        enterButton
        placeholder="输入关键词搜索..."
        onSearch={onSearch}
        key={getGuid()}
      />
      <Select
        allowClear
        style={{ width: 250 }}
        onClear={() => setSelectMapType(null)}
        placeholder={"选择地图类型"}
        onSelect={(e) => setSelectMapType(e)}
      >
        {mapTypeList.map((item) => {
          return (
            <Select.Option key={item.value} data={item.value}>
              {item.label}
            </Select.Option>
          );
        })}
      </Select>
      <UpdateTable mapList={mapList} refrush={getMList} key={getGuid()}></UpdateTable> */}
    </div>
  );
  return (
    <Card title={<LastPageButton title="三方任务" />} extra={extra}>
      <div style={{ cursor: "pointer" }}>
        <div>
          {isEmpty(allList) ? (
            <div />
          ) : (
            <Table
              pagination={{
                defaultPageSize: pageSize,
                defaultCurrent: page,
                showQuickJumper: true,
                pageSizeOptions: [10, 20, 30, 40, 50],
                showSizeChanger: true,
                locale: {
                  items_per_page: "条/页",
                  jump_tp: "跳至",
                  page: "页",
                },
              }}
              bordered
              dataSource={allList}
              columns={TableCols()}
              rowKey={(record) => record.id}
              size="small"
              scroll={{ y: getBodyH(276) }}
              onChange={handleTableChange}
            />
          )}
        </div>
      </div>
    </Card>
  );
};

export default FireIncidentTask;
