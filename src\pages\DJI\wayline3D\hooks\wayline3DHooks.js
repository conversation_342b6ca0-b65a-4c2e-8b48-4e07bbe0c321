import { useState, useEffect, useRef } from 'react';
import { Cesium } from "umi";
import { message } from 'antd';
import { GetCesiumViewer, SetCamera, SetCameraControl, DrawLookCone, PointImg2, Cartesian3_TO_Position } from '@/utils/cesium_help';
import * as turf from '@turf/turf'
import hangarImg from "@/assets/icons/device.png"
import FJGLB from '@/assets/models/triangle.glb';
const useWayLine3DHooks = (computePitch, computeZoomFactor, waylinePush, PListItemHeightChange, PListItemPositionChange, PListItemPositionDelete, seleteIndexChange, setviewerData, getGlobalHeadingAngle) => {
    // 默认高度模式
    let execute_height_mode = useRef('WGS84')
    // 默认高度
    let default_global_height = useRef(100)
    // 大地图viewer
    const viewer1 = useRef(null);
    // 小地图viewer
    const viewer2 = useRef(null);
    // 飞机模型
    let airplaneModel = useRef(null)
    // 飞机对应地面上的点
    let airplaneModel_entity = useRef(null)
    // 飞机对应地面上的线的位置信息
    let entity_polyline_array = useRef([120, 30, 1000, 120, 30, 0])
    // 模型姿态
    let headingPitchRoll = useRef(new Cesium.HeadingPitchRoll());
    // 云台姿态
    let PTZ_headingPitchRoll = useRef(new Cesium.HeadingPitchRoll());
    // 模型初始位置
    let airplaneModel_position = useRef(new Cesium.Cartesian3.fromDegrees(120, 30, 1000));
    // 局部变换坐标系
    let fixedFrameTransform = Cesium.Transforms.localFrameToFixedFrameGenerator("south", "east");
    // 每次操作姿态变化为5°
    let deltaRadians = useRef(Cesium.Math.toRadians(5.0));
    // 速度
    let speed = useRef(0);
    // 速度向量
    let speedVector = useRef(new Cesium.Cartesian3());
    // 模型平移方向
    let Direction = useRef(Cesium.Cartesian3.UNIT_X);
    // 航线点位信息
    let wayline_positions = useRef([]);
    // 航线实体
    let wayline_polyline = useRef(null);
    // 航点实体集合
    let wayline_cesiumObjectEntity = useRef([]);
    // 移动选中的航点
    let move_entity_index = useRef(null);
    // cesium事件
    let handlerPoint = useRef(null)
    // 视椎体
    let cone = useRef(null)
    // 标记实体
    const waypointInformation = useRef(null);
    // 机库实体
    const hangar = useRef(null)
    // 机库SN号
    const SN = useRef(null)
    // 航点索引
    const seleteIndex = useRef(null)
    // 延时器
    let timer = useRef(null)
    // 鼠标移动方式
    let cursor = useRef('')
    // 正在拖动的航点索引
    let move_index = useRef(null)
    // 禁飞区实体合集
    let nfzList = useRef([])
    // 围栏实体合集
    let dfenceList = useRef([])
    // 页面载入
    useEffect(() => {
        viewer1.current = GetCesiumViewer('cesisss')
        viewer1.current.scene.globe.depthTestAgainstTerrain = true;
        setviewerData(viewer1.current)
        viewer2.current = GetCesiumViewer('cesisss2')
        viewer2.current.scene.camera.frustum.fov = Cesium.Math.toRadians(30);
        viewer2.current.scene.camera.frustum.aspectRatio = (4 / 3)
        viewer1.current.cesiumWidget.screenSpaceEventHandler.removeInputAction(
            Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK
        );
        document.getElementById('cesisss').oncontextmenu = function () {//地图上取消浏览器默认的右击事件
            return false;
        }
        SetCameraControl(viewer2.current);
        // 添加视椎体
        cone.current = DrawLookCone(viewer1.current, {
            positions: airplaneModel_position.current,
            headingPitchRoll: headingPitchRoll.current,
        }, 1)
        waypointInformation.current = viewer1.current.entities.add({
            name: "航点信息",
            position: Cesium.Cartesian3.fromDegrees(-75.170726, 39.9208667),//#00ee8b
            label: {
                text: ``,
                font: '12pt Source Han Sans CN',             //字体样式
                fillColor: Cesium.Color.WHITE,                //字体颜色
                backgroundColor: new Cesium.Color(0, 0, 0, 0.9), //背景颜色
                showBackground: true,                         //是否显示背景颜色
                style: Cesium.LabelStyle.FILL,                //label样式
                outlineWidth: 2,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,  //垂直位置
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,//水平位置
                pixelOffset: new Cesium.Cartesian2(0, -30),      //偏移
            },
        });
        hangar.current = viewer1.current.entities.add({
            name: '机库',
            position: airplaneModel_position.current,
            billboard: {
                image: hangarImg,
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
                color: Cesium.Color.WHITE,
                scale: 0.1,
            },
        })
        // 添加cesium事件
        handlerPoint.current = new Cesium.ScreenSpaceEventHandler(viewer1.current.scene.canvas)
        // 添加飞机模型
        fromGltfAsync()
        //注册事件
        document.addEventListener('keyup', onKeyup)
        document.addEventListener('keydown', onKeydown)
        //鼠标事件
        LEFT_DOWN()
        LEFT_UP()
        MOUSE_MOVE()
        RIGHT_CLICK()
        return () => {
            destroy()
        };
    }, []);
    // 使用primitive方式加载模型
    function fromGltfAsync() {
        // 使用primitive方式加载模型
        Cesium.Model.fromGltfAsync({
            url: FJGLB,
            scale: 2,
            modelMatrix: Cesium.Transforms.headingPitchRollToFixedFrame(
                airplaneModel_position.current,
                headingPitchRoll.current,
                Cesium.Ellipsoid.WGS84,
                fixedFrameTransform
            ),
            minimumPixelSize: 64,
            maximumScale: 128
        }).then(res => {
            airplaneModel.current = viewer1.current.scene.primitives.add(res)
            //渲染阶段添加侦听
            viewer1.current.scene.preUpdate.addEventListener(() => {
                // 模型平移方向
                Cesium.Cartesian3.multiplyByScalar(
                    Direction.current,
                    speed.current / 10,
                    speedVector.current
                );
                // 生成新的位置信息
                Cesium.Matrix4.multiplyByPoint(
                    airplaneModel.current.modelMatrix,
                    speedVector.current,
                    airplaneModel_position.current
                )
                // 更新模型姿态与位置
                Update_model_position()
                let cartesian = new Cesium.Cartesian3(airplaneModel.current._boundingSphere.center.x, airplaneModel.current._boundingSphere.center.y, airplaneModel.current._boundingSphere.center.z);
                if (!Cesium.Cartographic.fromCartesian(cartesian)) {
                    return
                }
                let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(cartesian, viewer1.current)
                // 改变地面点的位置
                airplaneModel_entity.current.position.setValue(new Cesium.Cartesian3.fromDegrees(longitude, latitude, globeHeight))
                // 改变飞机与地面点连接线的位置信息
                entity_polyline_array.current = [longitude, latitude, globeHeight, longitude, latitude, height]
                viewer1.current.scene.primitives.remove(cone.current.c1);
                viewer1.current.scene.primitives.remove(cone.current.c2);
                cone.current = DrawLookCone(viewer1.current, {
                    positions: cartesian,
                    headingPitchRoll: {
                        heading: headingPitchRoll.current.heading,
                        pitch: Cesium.Math.toRadians(computePitch(seleteIndex.current)),
                        roll: 0
                    },
                }, computeZoomFactor(seleteIndex.current))
                viewer2.current.scene.camera.frustum.fov = Cesium.Math.toRadians(15 / computeZoomFactor(seleteIndex.current));
                SetCamera(viewer2.current, longitude, latitude, height, headingPitchRoll.current.heading, Cesium.Math.toRadians(computePitch(seleteIndex.current)), 0, 1200)
            })
        })
        // 添加一个点，用来表示无人机模型的经纬度，辅助判断
        airplaneModel_entity.current = viewer1.current.entities.add({
            position: airplaneModel_position.current, // 使用特定经纬度来表示飞机位置
            name: 'airplaneModel_entity',
            point: {
                pixelSize: 5,
                color: Cesium.Color.YELLOW, // 点的颜色
            },
            polyline: {
                positions: new Cesium.CallbackProperty(() => {
                    return new Cesium.Cartesian3.fromDegreesArrayHeights(entity_polyline_array.current);
                }, false),
                // 宽度
                width: 1,
                // 线的颜色
                material: Cesium.Color.YELLOW,
            }
        })
    }
    // 鼠标左键按下事件
    function LEFT_DOWN() {
        handlerPoint.current.setInputAction(function (e) {
            var pickedObject = viewer1.current.scene.pick(e.position);
            if (move_entity_index.current !== null) {
                timer.current = setTimeout(() => {
                    if (pickedObject.id.point && pickedObject.id.polyline) {
                        viewer1.current._container.style.cursor = "move";
                        cursor.current = "move"
                        move_index.current = Number(pickedObject.id.name)
                        viewer1.current.scene.screenSpaceCameraController.enableRotate = false;//禁止旋转
                    } else if (pickedObject.id.billboard) {
                        viewer1.current._container.style.cursor = "s-resize";
                        cursor.current = "s-resize"
                        move_index.current = Number(pickedObject.id.name)
                        viewer1.current.scene.screenSpaceCameraController.enableRotate = false;//禁止旋转
                    }
                    clearTimeout(timer.current)
                    timer.current = null
                }, 100)
            } else {
                timer.current = setTimeout(() => {
                    clearTimeout(timer.current)
                    timer.current = null
                }, 100)
            }
        }, Cesium.ScreenSpaceEventType.LEFT_DOWN);
    }
    // 鼠标左键抬起事件
    function LEFT_UP() {
        handlerPoint.current.setInputAction(function (e) {
            if (timer.current) {
                clearTimeout(timer.current)
                if (move_entity_index.current === null) {
                    var ray = viewer1.current.camera.getPickRay(e.position);
                    var cartesian = viewer1.current.scene.globe.pick(ray, viewer1.current.scene);
                    if (!cartesian) {//underfind说明地图还没加载成功
                        return
                    }
                    let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(cartesian, viewer1.current)
                    airplaneModel_position.current = new Cesium.Cartesian3.fromDegrees(longitude, latitude, execute_height_mode.current === 'WGS84' ? globeHeight + default_global_height.current : globeHeight + default_global_height.current + height)
                    // 更新模型姿态与位置
                    Update_model_position()
                } else {
                    seleteIndexChange(move_entity_index.current)
                }
            } else {
                console.log('长按事件');
                viewer1.current._container.style.cursor = "";
                cursor.current = ""
                move_index.current = null
                viewer1.current.scene.screenSpaceCameraController.enableRotate = true;//开启旋转
            }
        }, Cesium.ScreenSpaceEventType.LEFT_UP);
    }
    // 鼠标移动事件
    function MOUSE_MOVE() {
        handlerPoint.current.setInputAction(function (e) {
            var pickedObject = viewer1.current.scene.pick(e.endPosition);
            if (pickedObject && pickedObject.id && typeof pickedObject.id.name === 'number') {
                if (move_entity_index.current !== null) {
                    wayline_cesiumObjectEntity.current[move_entity_index.current].point_cesiumObjectEntity.point.color = Cesium.Color.WHITE
                    wayline_cesiumObjectEntity.current[move_entity_index.current].point_cesiumObjectEntity.polyline.material = new Cesium.PolylineDashMaterialProperty({
                        color: Cesium.Color.WHITE
                    })
                    wayline_cesiumObjectEntity.current[move_entity_index.current].billboard_cesiumObjectEntity.billboard.color = Cesium.Color.WHITE
                    // wayline_cesiumObjectEntity.current[move_entity_index.current].billboard_cesiumObjectEntity.label.backgroundColor = new Cesium.Color.fromCssColorString('#2d8cf0')
                    move_entity_index.current = null
                }
                move_entity_index.current = Number(pickedObject.id.name)
                if (pickedObject.id.point && pickedObject.id.polyline) {
                    // viewer1.current._container.style.cursor = "move";
                    wayline_cesiumObjectEntity.current[move_entity_index.current].point_cesiumObjectEntity.point.color = Cesium.Color.RED
                    wayline_cesiumObjectEntity.current[move_entity_index.current].point_cesiumObjectEntity.polyline.material = new Cesium.PolylineDashMaterialProperty({
                        color: Cesium.Color.RED
                    })
                } else if (pickedObject.id.billboard) {
                    // viewer1.current._container.style.cursor = "s-resize";
                    wayline_cesiumObjectEntity.current[move_entity_index.current].billboard_cesiumObjectEntity.billboard.color = Cesium.Color.RED
                    // wayline_cesiumObjectEntity.current[move_entity_index.current].billboard_cesiumObjectEntity.label.backgroundColor = Cesium.Color.RED
                }
            } else {
                if (move_entity_index.current !== null) {
                    viewer1.current._container.style.cursor = "";
                    wayline_cesiumObjectEntity.current[move_entity_index.current].point_cesiumObjectEntity.point.color = Cesium.Color.WHITE
                    wayline_cesiumObjectEntity.current[move_entity_index.current].point_cesiumObjectEntity.polyline.material = new Cesium.PolylineDashMaterialProperty({
                        color: Cesium.Color.WHITE
                    })
                    wayline_cesiumObjectEntity.current[move_entity_index.current].billboard_cesiumObjectEntity.billboard.color = Cesium.Color.WHITE
                    // wayline_cesiumObjectEntity.current[move_entity_index.current].billboard_cesiumObjectEntity.label.backgroundColor = new Cesium.Color.fromCssColorString('#2d8cf0')
                    move_entity_index.current = null
                }
            }
            if (cursor.current === "move") {
                viewer1.current._container.style.cursor = "move";
                seleteIndexChange(move_index.current)
                var ray = viewer1.current.camera.getPickRay(e.endPosition);
                var cartesian = viewer1.current.scene.globe.pick(ray, viewer1.current.scene);
                let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(cartesian, viewer1.current)
                item_position_change_function(move_index.current, longitude, latitude, globeHeight)
            } else if (cursor.current === "s-resize") {
                viewer1.current._container.style.cursor = "s-resize";
                seleteIndexChange(move_index.current)
                let move_y = Math.ceil(e.startPosition.y - e.endPosition.y)
                let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(wayline_cesiumObjectEntity.current[move_index.current].point_cesiumObjectEntity.polyline.positions._value[1], viewer1.current)
                let val = height + move_y
                if (val < 10) {
                    val = 10
                }
                item_height_change_function(val, move_index.current)
                PListItemHeightChange(move_index.current, val)
            }
        }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    }
    // 右击事件
    function RIGHT_CLICK() {
        handlerPoint.current.setInputAction(function (e) {
            // 原本是删除航点，现在改为添加航点
            // if (move_entity_index.current !== null) {
            //     delete_function(move_entity_index.current)
            // }
            if (!SN.current) {
                message.error(`请选择机库`);
                return
            }
            // if (move_entity_index.current === null) {
            var ray = viewer1.current.camera.getPickRay(e.position);
            var cartesian = viewer1.current.scene.globe.pick(ray, viewer1.current.scene);
            if (!cartesian) {//underfind说明地图还没加载成功
                return
            }
            let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(cartesian, viewer1.current)
            add_function(longitude, latitude, height, globeHeight, cartesian)
            // }
        }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    }
    // 销毁函数
    function destroy() {
        viewer1.current.scene.primitives.remove(airplaneModel.current);
        viewer1.current.scene.primitives.remove(cone.current.c1);
        viewer1.current.scene.primitives.remove(cone.current.c2);
        viewer1.current.entities.removeAll();
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOWN);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_UP);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.WHEEL, Cesium.KeyboardEventModifier.ALT);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE, Cesium.KeyboardEventModifier.ALT);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE, Cesium.KeyboardEventModifier.CTRL);
        handlerPoint.current.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
        //移除事件
        document.removeEventListener('keyup', onKeyup);
        document.removeEventListener('keydown', onKeydown);
    }
    // 按下键盘
    function onKeydown(event) {
        if (airplaneModel.current._show === false) {
            return
        }
        switch (event.keyCode) {
            // 左转
            case 81:
                headingPitchRoll.current.heading -= deltaRadians.current;
                // 判断是否超过2π范围
                if (headingPitchRoll.current.heading < -Cesium.Math.TWO_PI) {
                    headingPitchRoll.current.heading += Cesium.Math.TWO_PI;
                }
                break;
            // 右转
            case 69:
                headingPitchRoll.current.heading += deltaRadians.current;
                // 判断是否超过2π范围
                if (headingPitchRoll.current.heading > Cesium.Math.TWO_PI) {
                    headingPitchRoll.current.heading -= Cesium.Math.TWO_PI;
                }
                break;
            // 前进
            case 87:
                Direction.current = Cesium.Cartesian3.UNIT_X
                speed.current = -10
                break;
            // 后退
            case 83:
                Direction.current = Cesium.Cartesian3.UNIT_X
                speed.current = 10;
                break;
            // 上升
            case 67:
                Direction.current = Cesium.Cartesian3.UNIT_Z
                speed.current = 10
                break;
            // 下降
            case 90:
                Direction.current = Cesium.Cartesian3.UNIT_Z
                speed.current = -10;
                break;
            // 左平移
            case 65:
                Direction.current = Cesium.Cartesian3.UNIT_Y
                speed.current = -10
                break;
            // 右平移
            case 68:
                Direction.current = Cesium.Cartesian3.UNIT_Y
                speed.current = 10;
                break;
            // 加速
            case 88:
                speed.current = 20;
                break;
            // 添加航点
            case 32:
                // 获取模型的位置并转换成经纬度
                let cartesian = airplaneModel.current._boundingSphere.center
                let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(cartesian, viewer1.current)
                let point_cesiumObjectEntity = null
                let billboard_cesiumObjectEntity = null
                let height_cesiumObjectEntity = null
                let distance_cesiumObjectEntity = null
                if (wayline_cesiumObjectEntity.current.length < 1) {
                    wayline_positions.current.push(longitude, latitude, globeHeight, longitude, latitude, height)
                    // 创建航线
                    wayline_polyline.current = creat_wayline_polyline()
                    point_cesiumObjectEntity = creat_point_cesiumObjectEntity(index, cartesian, new Cesium.Cartesian3.fromDegreesArrayHeights([longitude, latitude, globeHeight, longitude, latitude, height]))
                    billboard_cesiumObjectEntity = creat_billboard_cesiumObjectEntity(index, Cesium.Cartesian3.fromDegrees(longitude, latitude, height), PointImg2(1), Cesium.Color.WHITE, 1, Cesium.HeightReference.NONE, `${index + 1}`, height)
                    height_cesiumObjectEntity = creat_height_cesiumObjectEntity(index, new Cesium.Cartesian3.fromDegrees(longitude, latitude, (globeHeight + globeHeight / 2)), (height).toFixed(0))
                    wayline_cesiumObjectEntity.current.push({ point_cesiumObjectEntity, billboard_cesiumObjectEntity, height_cesiumObjectEntity })
                } else {
                    wayline_positions.current.push(longitude, latitude, height)
                    // 添加航点
                    point_cesiumObjectEntity = creat_point_cesiumObjectEntity(index, cartesian, new Cesium.Cartesian3.fromDegreesArrayHeights([longitude, latitude, globeHeight, longitude, latitude, height]))
                    billboard_cesiumObjectEntity = creat_billboard_cesiumObjectEntity(index, Cesium.Cartesian3.fromDegrees(longitude, latitude, height), PointImg2(wayline_cesiumObjectEntity.current.length + 1), Cesium.Color.WHITE, 1, Cesium.HeightReference.NONE, `${wayline_cesiumObjectEntity.current.length + 1}`, height)
                    height_cesiumObjectEntity = creat_height_cesiumObjectEntity(index, new Cesium.Cartesian3.fromDegrees(longitude, latitude, (globeHeight + globeHeight / 2)), (height).toFixed(0))
                    var point1 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[wayline_positions.current.length - 3], wayline_positions.current[wayline_positions.current.length - 2], wayline_positions.current[wayline_positions.current.length - 1]); // 第一个点的坐标（x、y、z）
                    var point2 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[wayline_positions.current.length - 6], wayline_positions.current[wayline_positions.current.length - 5], wayline_positions.current[wayline_positions.current.length - 4]); // 第二个点的坐标（x、y、z）
                    //计算中点坐标
                    let centerPosition = new Cesium.Cartesian3();
                    Cesium.Cartesian3.midpoint(point1, point2, centerPosition);
                    var distance = Cesium.Cartesian3.distance(point1, point2);
                    distance_cesiumObjectEntity = creat_distance_cesiumObjectEntity(`第${wayline_cesiumObjectEntity.current.length}个距离`, centerPosition, `${distance.toFixed(0)}m`)
                    wayline_cesiumObjectEntity.current.push({ point_cesiumObjectEntity, billboard_cesiumObjectEntity, height_cesiumObjectEntity, distance_cesiumObjectEntity })
                }
                waypointInformation.current.position.setValue(wayline_cesiumObjectEntity.current[wayline_cesiumObjectEntity.current.length - 1].point_cesiumObjectEntity.polyline.positions.getValue()[1])
                waypointInformation.current.label.show = (true)
                waypointInformation.current.label.text.setValue(`航点${wayline_cesiumObjectEntity.current.length}\nHEA:${height.toFixed(0)}m`)
                setTimeout(() => {
                    seleteIndexChange(wayline_cesiumObjectEntity.current.length - 1)
                }, 10)
                break;
            default:
                break;
        }
    }
    // 结束按键
    function onKeyup(event) {
        if (airplaneModel.current._show === false) {
            return
        }
        switch (event.keyCode) {
            // 加速
            case 87:
                Direction.current = Cesium.Cartesian3.UNIT_X
                speed.current = 0
                break;
            // 减速
            case 83:
                Direction.current = Cesium.Cartesian3.UNIT_X
                speed.current = 0;
                break;
            // 停止加速
            case 88:
                speed.current = 0;
                break;
            // 上升
            case 67:
                Direction.current = Cesium.Cartesian3.UNIT_X
                speed.current = 0
                break;
            // 下降
            case 90:
                Direction.current = Cesium.Cartesian3.UNIT_X
                speed.current = 0;
                break;
            // 左平移
            case 65:
                Direction.current = Cesium.Cartesian3.UNIT_X
                speed.current = 0
                break;
            // 右平移
            case 68:
                Direction.current = Cesium.Cartesian3.UNIT_X
                speed.current = 0;
                break;
            default:
                break;
        }
    }
    // 添加航点
    function add_function(longitude, latitude, height, globeHeight, cartesian) {
        let point_cesiumObjectEntity = null
        let billboard_cesiumObjectEntity = null
        let height_cesiumObjectEntity = null
        let distance_cesiumObjectEntity = null
        waylinePush(seleteIndex.current, latitude, longitude, execute_height_mode.current === 'WGS84' ? globeHeight + default_global_height.current : globeHeight + default_global_height.current + height, globeHeight)
        if (wayline_cesiumObjectEntity.current.length < 1) {
            wayline_positions.current.push(longitude, latitude, height, longitude, latitude, execute_height_mode.current === 'WGS84' ? globeHeight + default_global_height.current : globeHeight + default_global_height.current + height)
            // 创建航线
            wayline_polyline.current = creat_wayline_polyline()
            point_cesiumObjectEntity = creat_point_cesiumObjectEntity(wayline_cesiumObjectEntity.current.length, cartesian, new Cesium.Cartesian3.fromDegreesArrayHeights([longitude, latitude, height, longitude, latitude, execute_height_mode.current === 'WGS84' ? globeHeight + default_global_height.current : wayline_positions.current[2] + globeHeight + default_global_height.current]))
            billboard_cesiumObjectEntity = creat_billboard_cesiumObjectEntity(wayline_cesiumObjectEntity.current.length, Cesium.Cartesian3.fromDegrees(longitude, latitude, execute_height_mode.current === 'WGS84' ? globeHeight + default_global_height.current : wayline_positions.current[2] + globeHeight + default_global_height.current), PointImg2(1), Cesium.Color.WHITE, 1, Cesium.HeightReference.NONE, `${wayline_cesiumObjectEntity.current.length + 1}`, globeHeight + default_global_height.current)
            height_cesiumObjectEntity = creat_height_cesiumObjectEntity(wayline_cesiumObjectEntity.current.length, new Cesium.Cartesian3.fromDegrees(longitude, latitude, execute_height_mode.current === 'WGS84' ? ((globeHeight + default_global_height.current - height) / 2 + height) : ((globeHeight + default_global_height.current + height + height) / 2)), (globeHeight + default_global_height.current).toFixed(0))
            wayline_cesiumObjectEntity.current.push({ point_cesiumObjectEntity, billboard_cesiumObjectEntity, height_cesiumObjectEntity })
            seleteIndexChange(0)
        } else {
            wayline_positions.current.splice((seleteIndex.current * 3 + 6), 0, longitude, latitude, execute_height_mode.current === 'WGS84' ? globeHeight + default_global_height.current : wayline_positions.current[2] + globeHeight + default_global_height.current)
            // 添加航点
            point_cesiumObjectEntity = creat_point_cesiumObjectEntity(seleteIndex.current + 2, cartesian, new Cesium.Cartesian3.fromDegreesArrayHeights([longitude, latitude, height, longitude, latitude, execute_height_mode.current === 'WGS84' ? globeHeight + default_global_height.current : wayline_positions.current[2] + globeHeight + default_global_height.current]))
            billboard_cesiumObjectEntity = creat_billboard_cesiumObjectEntity(seleteIndex.current + 2, Cesium.Cartesian3.fromDegrees(longitude, latitude, execute_height_mode.current === 'WGS84' ? globeHeight + default_global_height.current : wayline_positions.current[2] + globeHeight + default_global_height.current), PointImg2(wayline_cesiumObjectEntity.current.length + 1), Cesium.Color.WHITE, 1, Cesium.HeightReference.NONE, `${seleteIndex.current + 2}`, globeHeight + default_global_height.current)
            height_cesiumObjectEntity = creat_height_cesiumObjectEntity(seleteIndex.current + 2, new Cesium.Cartesian3.fromDegrees(longitude, latitude, execute_height_mode.current === 'WGS84' ? ((globeHeight + default_global_height.current - wayline_positions.current[2]) / 2 + wayline_positions.current[2]) : ((globeHeight + default_global_height.current + wayline_positions.current[2] + wayline_positions.current[2]) / 2)), (globeHeight + default_global_height.current).toFixed(0))
            var point1 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[(seleteIndex.current * 3 + 3)], wayline_positions.current[(seleteIndex.current * 3 + 4)], wayline_positions.current[(seleteIndex.current * 3 + 5)]); // 第一个点的坐标（x、y、z）
            var point2 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[(seleteIndex.current * 3)], wayline_positions.current[(seleteIndex.current * 3 + 1)], wayline_positions.current[(seleteIndex.current * 3 + 2)]); // 第二个点的坐标（x、y、z）
            //计算中点坐标
            let centerPosition = new Cesium.Cartesian3();
            Cesium.Cartesian3.midpoint(point1, point2, centerPosition);
            var distance = Cesium.Cartesian3.distance(point1, point2);
            distance_cesiumObjectEntity = creat_distance_cesiumObjectEntity(`第${seleteIndex.current + 2}个距离`, centerPosition, `${distance.toFixed(0)}m`)
            wayline_cesiumObjectEntity.current.splice(seleteIndex.current + 1, 0, { point_cesiumObjectEntity, billboard_cesiumObjectEntity, height_cesiumObjectEntity, distance_cesiumObjectEntity })
            // 将所有航点实体遍历一遍调整数据
            adjust_wayline_cesiumObjectEntity()
            seleteIndexChange(seleteIndex.current + 1)
        }
    }
    // 回显航线
    function add_all_function(PList) {
        PList.forEach((item, index) => {
            let cartesian = Cesium.Cartesian3.fromDegrees(item.Lng, item.Lat, item.ground_height)
            let point_cesiumObjectEntity = null
            let billboard_cesiumObjectEntity = null
            let height_cesiumObjectEntity = null
            let distance_cesiumObjectEntity = null
            if (wayline_cesiumObjectEntity.current.length < 1) {
                wayline_positions.current.push(item.Lng, item.Lat, item.ground_height, item.Lng, item.Lat, item.Height)
                // 创建航线
                wayline_polyline.current = creat_wayline_polyline()
                point_cesiumObjectEntity = creat_point_cesiumObjectEntity(index, cartesian, new Cesium.Cartesian3.fromDegreesArrayHeights([item.Lng, item.Lat, item.ground_height, item.Lng, item.Lat, item.Height]))
                billboard_cesiumObjectEntity = creat_billboard_cesiumObjectEntity(index, Cesium.Cartesian3.fromDegrees(item.Lng, item.Lat, item.Height), PointImg2(1), Cesium.Color.WHITE, 1, Cesium.HeightReference.NONE, `${index + 1}`, item.Height)
                height_cesiumObjectEntity = creat_height_cesiumObjectEntity(index, new Cesium.Cartesian3.fromDegrees(item.Lng, item.Lat, (item.ground_height + item.global_height / 2)), (item.Height).toFixed(0))
                wayline_cesiumObjectEntity.current.push({ point_cesiumObjectEntity, billboard_cesiumObjectEntity, height_cesiumObjectEntity })
            } else {
                wayline_positions.current.push(item.Lng, item.Lat, item.Height)
                // 添加航点
                point_cesiumObjectEntity = creat_point_cesiumObjectEntity(index, cartesian, new Cesium.Cartesian3.fromDegreesArrayHeights([item.Lng, item.Lat, item.ground_height, item.Lng, item.Lat, item.Height]))
                billboard_cesiumObjectEntity = creat_billboard_cesiumObjectEntity(index, Cesium.Cartesian3.fromDegrees(item.Lng, item.Lat, item.Height), PointImg2(wayline_cesiumObjectEntity.current.length + 1), Cesium.Color.WHITE, 1, Cesium.HeightReference.NONE, `${wayline_cesiumObjectEntity.current.length + 1}`, item.Height)
                height_cesiumObjectEntity = creat_height_cesiumObjectEntity(index, new Cesium.Cartesian3.fromDegrees(item.Lng, item.Lat, (item.ground_height + item.global_height / 2)), (item.Height).toFixed(0))
                var point1 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[wayline_positions.current.length - 3], wayline_positions.current[wayline_positions.current.length - 2], wayline_positions.current[wayline_positions.current.length - 1]); // 第一个点的坐标（x、y、z）
                var point2 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[wayline_positions.current.length - 6], wayline_positions.current[wayline_positions.current.length - 5], wayline_positions.current[wayline_positions.current.length - 4]); // 第二个点的坐标（x、y、z）
                //计算中点坐标
                let centerPosition = new Cesium.Cartesian3();
                Cesium.Cartesian3.midpoint(point1, point2, centerPosition);
                var distance = Cesium.Cartesian3.distance(point1, point2);
                distance_cesiumObjectEntity = creat_distance_cesiumObjectEntity(`第${wayline_cesiumObjectEntity.current.length}个距离`, centerPosition, `${distance.toFixed(0)}m`)
                wayline_cesiumObjectEntity.current.push({ point_cesiumObjectEntity, billboard_cesiumObjectEntity, height_cesiumObjectEntity, distance_cesiumObjectEntity })
            }
        })
        waypointInformation.current.position.setValue(wayline_cesiumObjectEntity.current[PList.length - 1].point_cesiumObjectEntity.polyline.positions.getValue()[1])
        waypointInformation.current.label.show = (true)
        waypointInformation.current.label.text.setValue(`航点${PList.length}\nHEA:${PList[PList.length - 1].Height.toFixed(0)}m`)
    }
    // 删除航点
    function delete_function(delete_index) {
        // 先处理航线点位信息
        if (delete_index === 0) {
            if (wayline_cesiumObjectEntity.current.length === 1) {//如果只有一个航点
                wayline_positions.current = []
                viewer1.current.entities.remove(wayline_polyline.current)
            } else {//如果有不止一个航点
                wayline_positions.current.splice(0, 3)
                wayline_positions.current[0] = wayline_positions.current[3]
                wayline_positions.current[1] = wayline_positions.current[4]
                let height = viewer1.current.scene.globe.getHeight(Cesium.Cartographic.fromCartesian(new Cesium.Cartesian3.fromDegrees(wayline_positions.current[3], wayline_positions.current[4], wayline_positions.current[5])))
                wayline_positions.current[2] = height
            }
        } else {
            wayline_positions.current.splice((delete_index + 1) * 3, 3)
        }
        // 删除对应航点所有实体
        for (const key in wayline_cesiumObjectEntity.current[delete_index]) {
            viewer1.current.entities.remove(wayline_cesiumObjectEntity.current[delete_index][key])
        }
        // 删除航点实体数组中对应集合
        wayline_cesiumObjectEntity.current.splice(delete_index, 1)
        // 将所有航点实体遍历一遍调整数据
        adjust_wayline_cesiumObjectEntity()
        if (delete_index >= wayline_cesiumObjectEntity.current.length) {
            if (wayline_positions.current.length >= 9) {
                let N = wayline_positions.current.length - 1
                let coordItem = [[wayline_positions.current[N - 5], wayline_positions.current[N - 4]], [wayline_positions.current[N - 2], wayline_positions.current[N - 1]]]
                computeAngle(coordItem[0], coordItem[1], new Cesium.Cartesian3.fromDegrees(wayline_positions.current[N - 2], wayline_positions.current[N - 1], wayline_positions.current[N]))
            }
        }
        PListItemPositionDelete(delete_index)
        move_entity_index.current = null
    }
    // 改变某个航点高度
    function item_height_change_function(val, index) {
        let N = (index + 2) * 3 - 1
        let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(wayline_cesiumObjectEntity.current[index].point_cesiumObjectEntity.position._value, viewer1.current)
        let positionInfo = { lng: longitude, lat: latitude, height: val }
        wayline_positions.current[N] = execute_height_mode.current === 'WGS84' ? positionInfo.height : positionInfo.height + wayline_positions.current[2]
        wayline_cesiumObjectEntity.current[index].point_cesiumObjectEntity.polyline.positions.setValue(new Cesium.Cartesian3.fromDegreesArrayHeights([positionInfo.lng, positionInfo.lat, height, positionInfo.lng, positionInfo.lat, execute_height_mode.current === 'WGS84' ? positionInfo.height : positionInfo.height + wayline_positions.current[2]]))
        wayline_cesiumObjectEntity.current[index].billboard_cesiumObjectEntity.position.setValue(Cesium.Cartesian3.fromDegrees(positionInfo.lng, positionInfo.lat, execute_height_mode.current === 'WGS84' ? positionInfo.height : positionInfo.height + wayline_positions.current[2]))
        wayline_cesiumObjectEntity.current[index].height_cesiumObjectEntity.position.setValue(Cesium.Cartesian3.fromDegrees(positionInfo.lng, positionInfo.lat, execute_height_mode.current === 'WGS84' ? ((positionInfo.height - height) / 2 + height) : (positionInfo.height + wayline_positions.current[2] + height) / 2))
        wayline_cesiumObjectEntity.current[index].height_cesiumObjectEntity.label.text.setValue(`${positionInfo.height.toFixed(0)}m`)
        if (index !== 0) {
            var point1 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[N - 2], wayline_positions.current[N - 1], wayline_positions.current[N]); // 第一个点的坐标（x、y、z）
            var point2 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[N - 5], wayline_positions.current[N - 4], wayline_positions.current[N - 3]); // 第二个点的坐标（x、y、z）
            let coordItem = [[wayline_positions.current[N - 5], wayline_positions.current[N - 4]], [wayline_positions.current[N - 2], wayline_positions.current[N - 1]]]
            computeAngle(coordItem[0], coordItem[1], new Cesium.Cartesian3.fromDegrees(wayline_positions.current[N - 2], wayline_positions.current[N - 1], wayline_positions.current[N]))
            //计算中点坐标
            let centerPosition = new Cesium.Cartesian3();
            Cesium.Cartesian3.midpoint(point1, point2, centerPosition);
            var distance = Cesium.Cartesian3.distance(point1, point2);
            wayline_cesiumObjectEntity.current[index].distance_cesiumObjectEntity.position.setValue(centerPosition)
            wayline_cesiumObjectEntity.current[index].distance_cesiumObjectEntity.label.text.setValue(`${distance.toFixed(0)}m`)
        }
        if (index !== wayline_cesiumObjectEntity.current.length - 1) {
            var point1 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[N + 1], wayline_positions.current[N + 2], wayline_positions.current[N + 3]); // 第一个点的坐标（x、y、z）
            var point2 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[N - 2], wayline_positions.current[N - 1], wayline_positions.current[N]); // 第二个点的坐标（x、y、z）
            let coordItem = [[wayline_positions.current[N - 2], wayline_positions.current[N - 1]], [wayline_positions.current[N + 1], wayline_positions.current[N + 2]]]
            computeAngle(coordItem[0], coordItem[1], new Cesium.Cartesian3.fromDegrees(wayline_positions.current[N - 2], wayline_positions.current[N - 1], wayline_positions.current[N]))
            //计算中点坐标
            let centerPosition = new Cesium.Cartesian3();
            Cesium.Cartesian3.midpoint(point1, point2, centerPosition);
            var distance = Cesium.Cartesian3.distance(point1, point2);
            wayline_cesiumObjectEntity.current[index + 1].distance_cesiumObjectEntity.position.setValue(centerPosition)
            wayline_cesiumObjectEntity.current[index + 1].distance_cesiumObjectEntity.label.text.setValue(`${distance.toFixed(0)}m`)
        }
        SwitchWaypoint(index)
    }
    // 改变某个航点位置
    function item_position_change_function(index, longitude, latitude, globeHeight) {
        let M = (index + 2) * 3 - 3
        let N = (index + 2) * 3 - 2
        wayline_positions.current[M] = longitude
        wayline_positions.current[N] = latitude
        if (index === 0) {
            wayline_positions.current[0] = longitude
            wayline_positions.current[1] = latitude
            wayline_positions.current[2] = globeHeight
        }
        let positionInfo = { lng: longitude, lat: latitude, height: wayline_positions.current[N + 1] }
        PListItemPositionChange(index, positionInfo.lat, positionInfo.lng, execute_height_mode.current === 'WGS84' ? positionInfo.height : positionInfo.height + wayline_positions.current[2])
        wayline_cesiumObjectEntity.current[index].point_cesiumObjectEntity.position.setValue(new Cesium.Cartesian3.fromDegrees(positionInfo.lng, positionInfo.lat, globeHeight))
        wayline_cesiumObjectEntity.current[index].point_cesiumObjectEntity.polyline.positions.setValue(new Cesium.Cartesian3.fromDegreesArrayHeights([positionInfo.lng, positionInfo.lat, globeHeight, positionInfo.lng, positionInfo.lat, execute_height_mode.current === 'WGS84' ? positionInfo.height : positionInfo.height + wayline_positions.current[2]]))
        wayline_cesiumObjectEntity.current[index].billboard_cesiumObjectEntity.position.setValue(Cesium.Cartesian3.fromDegrees(positionInfo.lng, positionInfo.lat, execute_height_mode.current === 'WGS84' ? positionInfo.height : positionInfo.height + wayline_positions.current[2]))
        wayline_cesiumObjectEntity.current[index].height_cesiumObjectEntity.position.setValue(Cesium.Cartesian3.fromDegrees(positionInfo.lng, positionInfo.lat, execute_height_mode.current === 'WGS84' ? ((positionInfo.height - globeHeight) / 2 + globeHeight) : (positionInfo.height + wayline_positions.current[2] + globeHeight) / 2))
        wayline_cesiumObjectEntity.current[index].height_cesiumObjectEntity.label.text.setValue(`${positionInfo.height.toFixed(0)}m`)
        if (index !== 0) {
            var point1 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[M], wayline_positions.current[N], wayline_positions.current[N + 1]); // 第一个点的坐标（x、y、z）
            var point2 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[M - 3], wayline_positions.current[N - 3], wayline_positions.current[N - 2]); // 第二个点的坐标（x、y、z）
            //计算中点坐标
            let centerPosition = new Cesium.Cartesian3();
            Cesium.Cartesian3.midpoint(point1, point2, centerPosition);
            var distance = Cesium.Cartesian3.distance(point1, point2);
            wayline_cesiumObjectEntity.current[index].distance_cesiumObjectEntity.position.setValue(centerPosition)
            wayline_cesiumObjectEntity.current[index].distance_cesiumObjectEntity.label.text.setValue(`${distance.toFixed(0)}m`)
        }
        if (index !== wayline_cesiumObjectEntity.current.length - 1) {
            var point1 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[M + 3], wayline_positions.current[N + 3], wayline_positions.current[N + 4]); // 第一个点的坐标（x、y、z）
            var point2 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[M], wayline_positions.current[N], wayline_positions.current[N + 1]); // 第二个点的坐标（x、y、z）
            //计算中点坐标
            let centerPosition = new Cesium.Cartesian3();
            Cesium.Cartesian3.midpoint(point1, point2, centerPosition);
            var distance = Cesium.Cartesian3.distance(point1, point2);
            wayline_cesiumObjectEntity.current[index + 1].distance_cesiumObjectEntity.position.setValue(centerPosition)
            wayline_cesiumObjectEntity.current[index + 1].distance_cesiumObjectEntity.label.text.setValue(`${distance.toFixed(0)}m`)
        }
    }
    // 将所有航点实体遍历一遍调整数据
    function adjust_wayline_cesiumObjectEntity() {
        wayline_cesiumObjectEntity.current.forEach((item, index) => {
            if (index === 0) {
                item.point_cesiumObjectEntity.name = index
                item.billboard_cesiumObjectEntity.name = index
                item.billboard_cesiumObjectEntity.position.setValue(new Cesium.Cartesian3.fromDegrees(wayline_positions.current[3], wayline_positions.current[4], wayline_positions.current[5]))
                item.billboard_cesiumObjectEntity.billboard.image.setValue(PointImg2(index + 1))
                item.height_cesiumObjectEntity.name = index
                if (item.distance_cesiumObjectEntity) {
                    viewer1.current.entities.remove(item.distance_cesiumObjectEntity)
                }
            } else {
                item.point_cesiumObjectEntity.name = index
                item.billboard_cesiumObjectEntity.name = index
                item.billboard_cesiumObjectEntity.billboard.image.setValue(PointImg2(index + 1))
                item.height_cesiumObjectEntity.name = index
                item.distance_cesiumObjectEntity.name = `第${index}个距离`
                let N = (index + 2) * 3 - 1
                var point1 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[N - 2], wayline_positions.current[N - 1], wayline_positions.current[N]); // 第一个点的坐标（x、y、z）
                var point2 = new Cesium.Cartesian3.fromDegrees(wayline_positions.current[N - 5], wayline_positions.current[N - 4], wayline_positions.current[N - 3]); // 第二个点的坐标（x、y、z）
                //计算中点坐标
                let centerPosition = new Cesium.Cartesian3();
                Cesium.Cartesian3.midpoint(point1, point2, centerPosition);
                var distance = Cesium.Cartesian3.distance(point1, point2);
                item.distance_cesiumObjectEntity.position.setValue(centerPosition)
                item.distance_cesiumObjectEntity.label.text.setValue(`${distance.toFixed(0)}m`)
            }
        })
    }
    // 计算两个点的方向并调整飞机姿态
    function computeAngle(coordItem1, coordItem2, position) {
        //通过 turf 计算两个点的方向向量
        let angle = turf.rhumbBearing(coordItem1, coordItem2)
        //转换成cesium的角度
        const angleInRadians = Cesium.Math.toRadians(angle)
        let GlobalHeadingAngle = getGlobalHeadingAngle()
        if (GlobalHeadingAngle === '') {
            headingPitchRoll.current.heading = angleInRadians
        } else {
            headingPitchRoll.current.heading = GlobalHeadingAngle
        }
        airplaneModel_position.current = position
        // 更新模型姿态与位置
        Update_model_position()
    }
    // 更新模型姿态与位置
    function Update_model_position() {
        if (!airplaneModel.current) {
            setTimeout(() => {
                Update_model_position()
            }, 1000)
            return
        }
        Cesium.Transforms.headingPitchRollToFixedFrame(
            airplaneModel_position.current,
            headingPitchRoll.current,
            Cesium.Ellipsoid.WGS84,
            fixedFrameTransform,
            airplaneModel.current.modelMatrix
        )
    }
    // 创建航线
    function creat_wayline_polyline() {
        return viewer1.current.entities.add({
            name: `航线`,
            polyline: {
                positions: new Cesium.CallbackProperty(() => {
                    return new Cesium.Cartesian3.fromDegreesArrayHeights(wayline_positions.current);
                }, false),
                // 宽度
                width: 6,
                arcType: Cesium.ArcType.RHUMB,
                material: Cesium.Color.fromCssColorString('#0aed8a'),
            },
        })
    }
    // 创建point_cesiumObjectEntity
    function creat_point_cesiumObjectEntity(name, point_position, polyline_positions) {
        return viewer1.current.entities.add({
            name: name,
            position: point_position,
            point: {
                pixelSize: 10,
                color: Cesium.Color.WHITE, // 点的颜色
            },
            polyline: {
                positions: polyline_positions,
                // 宽度
                width: 2,
                // 线的颜色
                material: new Cesium.PolylineDashMaterialProperty({
                    color: Cesium.Color.WHITE
                }),
            },
        })
    }
    // 创建billboard_cesiumObjectEntity
    function creat_billboard_cesiumObjectEntity(name, position, image, color, scale, heightReference, text, height) {
        return viewer1.current.entities.add({
            name: name,
            position: position,
            billboard: {
                image,
                heightReference,
                color,
                scale,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,  //垂直位置
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,//水平位置
            },
            // ellipse: {
            //     semiMinorAxis: 5,
            //     semiMajorAxis: 5,
            //     outline: true,
            //     outlineColor: Cesium.Color.WHITE,
            //     outlineWidth: 2,
            //     height
            // }
        })
    }
    // 创建height_cesiumObjectEntity
    function creat_height_cesiumObjectEntity(name, position, text) {
        return viewer1.current.entities.add({
            name: name,
            position: position,
            label: {
                text: `${text}m`,
                font: '8pt Source Han Sans CN',             //字体样式
                fillColor: Cesium.Color.WHITE,                //字体颜色
                backgroundColor: new Cesium.Color(0, 0, 0, 0.7), //背景颜色
                showBackground: true,                         //是否显示背景颜色
                style: Cesium.LabelStyle.FILL,                //label样式
                outlineWidth: 2,
                verticalOrigin: Cesium.VerticalOrigin.CENTER,  //垂直位置
                horizontalOrigin: Cesium.HorizontalOrigin.LEFT,//水平位置
                pixelOffset: new Cesium.Cartesian2(10, 0),      //偏移
            }
        })
    }
    // 创建distance_cesiumObjectEntity
    function creat_distance_cesiumObjectEntity(name, position, text) {
        return viewer1.current.entities.add({
            name: name,
            position: position,
            label: {
                text: text,
                font: '8pt Source Han Sans CN',             //字体样式
                fillColor: Cesium.Color.WHITE,                //字体颜色
                backgroundColor: new Cesium.Color(0, 0, 0, 0.7), //背景颜色
                showBackground: true,                         //是否显示背景颜色
                style: Cesium.LabelStyle.FILL,                //label样式
                outlineWidth: 2,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,  //垂直位置
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,//水平位置
                pixelOffset: new Cesium.Cartesian2(0, -10),      //偏移
            }
        })
    }
    // 切换正在编辑的航点
    function SwitchWaypoint(index) {
        if (index === null) {
            waypointInformation.current.label.show = (false)
            return
        }
        waypointInformation.current.position.setValue(wayline_cesiumObjectEntity.current[index].point_cesiumObjectEntity.polyline.positions.getValue()[1])
        let { longitude, latitude, height, globeHeight } = Cartesian3_TO_Position(wayline_cesiumObjectEntity.current[index].point_cesiumObjectEntity.polyline.positions.getValue()[1], viewer1.current)
        waypointInformation.current.label.show = (true)
        waypointInformation.current.label.text.setValue(`航点${index + 1}\nHEA:${height.toFixed(0)}m`)
        if (index !== wayline_cesiumObjectEntity.current.length - 1) {
            let M = (index + 2) * 3 - 3
            let N = (index + 2) * 3 - 2
            let coordItem = [[wayline_positions.current[M - 3], wayline_positions.current[N - 3]], [wayline_positions.current[M], wayline_positions.current[N]]]
            computeAngle(coordItem[0], coordItem[1], new Cesium.Cartesian3.fromDegrees(wayline_positions.current[M], wayline_positions.current[N], wayline_positions.current[N + 1]))
        } else if (index === wayline_cesiumObjectEntity.current.length - 1) {
            let N = wayline_positions.current.length - 1
            let coordItem = [[wayline_positions.current[N - 5], wayline_positions.current[N - 4]], [wayline_positions.current[N - 2], wayline_positions.current[N - 1]]]
            computeAngle(coordItem[0], coordItem[1], new Cesium.Cartesian3.fromDegrees(wayline_positions.current[N - 2], wayline_positions.current[N - 1], wayline_positions.current[N]))
        }
    }
    // 切换机库位置
    function SwitchHanger(Lng, Lat, Height, sn) {
        viewer1.current.scene.camera.setView({
            destination: Cesium.Cartesian3.fromDegrees(Lng, Lat, Height + 1000),
            orientation: {
                heading: 0,
                pitch: Cesium.Math.toRadians(-90),
                roll: 0,
            },
        });
        hangar.current.position.setValue(Cesium.Cartesian3.fromDegrees(Lng, Lat, Height))
        SN.current = sn
        airplaneModel_position.current = Cesium.Cartesian3.fromDegrees(Lng, Lat, Height + 100)
        // 更新模型姿态与位置
        Update_model_position()
    }
    // 云台姿态改变
    function PTZ_headingPitchRoll_Change(params) {
        headingPitchRoll.current.heading = params.heading
        PTZ_headingPitchRoll.current.pitch = params.pitch
    }
    // 改变航点索引
    function EditseleteIndex(index) {
        seleteIndex.current = index
    }
    // 加载禁飞区
    function addNotFlyZone(geojson) {
        console.log(geojson.features);
        nfzList.current.forEach((item, index) => {
            viewer1.current.entities.remove(item)
        })
        nfzList.current = []
        dfenceList.current.forEach((item, index) => {
            viewer1.current.entities.remove(item)
        })
        dfenceList.current = []
        geojson.features.forEach((item, index) => {
            if (item.geofence_type === 'nfz') {//禁飞区
                nfzList.current.push(item.geometry.type === 'Polygon' ?
                    polygonEntity(item.id, item.geometry.coordinates, '#ff0000', 'nfz') :
                    ellipseEntity(item.id, item.geometry.coordinates, '#ff0000', item.properties.radius, 'nfz'))
            } else if (item.geofence_type = "dfence") {//作业区
                dfenceList.current.push(item.geometry.type === 'Polygon' ?
                    polygonEntity(item.id, item.geometry.coordinates, '#0000ff', 'dfence') :
                    ellipseEntity(item.id, item.geometry.coordinates, '#0000ff', item.properties.radius, 'dfence'))
            }
        })
    }
    // 添加多边形
    function polygonEntity(id, coordinates, color, type) {
        let material = null
        if (type === 'nfz') {
            material = Cesium.Color.fromCssColorString(color)
        } else {
            material = new Cesium.PolylineDashMaterialProperty({
                color: Cesium.Color.fromCssColorString(color)
            })
        }
        return viewer1.current.entities.add({
            id: id,
            name: `禁飞区`,
            polygon: {
                hierarchy: new Cesium.Cartesian3.fromDegreesArray(coordinates.flat(Infinity)),
                material: Cesium.Color.fromCssColorString(color).withAlpha(0.3),
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            },
            polyline: {
                show: true,
                positions: new Cesium.Cartesian3.fromDegreesArray(coordinates.flat(Infinity)),
                // 宽度
                width: 3,
                // 线的颜色
                material: material,
                clampToGround: true,
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            }
        })
    }
    // 添加圆形
    function ellipseEntity(id, coordinates, color, radius, type) {
        let material = null
        if (type === 'nfz') {
            material = Cesium.Color.fromCssColorString(color)
        } else {
            material = new Cesium.PolylineDashMaterialProperty({
                color: Cesium.Color.fromCssColorString(color)
            })
        }
        return viewer1.current.entities.add({
            position: Cesium.Cartesian3.fromDegrees(...coordinates.flat(Infinity)),
            id: id,
            name: "围栏",
            ellipse: {
                semiMinorAxis: radius,
                semiMajorAxis: radius,
                material: Cesium.Color.fromCssColorString(color).withAlpha(0.3),
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            },
            polyline: {
                show: true,
                positions: CircleOutline(coordinates, radius),
                // 宽度
                width: 3,
                // 线的颜色
                material: material,
                clampToGround: true,
                heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
            }
        })
    }
    // 计算圆形经纬度
    function CircleOutline(coordinates, radius) {
        // 创建一个圆
        var geometry = Cesium.CircleOutlineGeometry.createGeometry(new Cesium.CircleOutlineGeometry({
            center: Cesium.Cartesian3.fromDegrees(...coordinates.flat(Infinity)),
            radius: radius,
            extrudedHeight: Cesium.HeightReference.CLAMP_TO_GROUND
        }));
        let Cartesian3Array = []
        for (let i = 0; i < geometry.attributes.position.values.length; i += 3) {
            Cartesian3Array.push(new Cesium.Cartesian3(geometry.attributes.position.values[i], geometry.attributes.position.values[i + 1], geometry.attributes.position.values[i + 2]))
        }
        return Cartesian3Array
    }
    return {
        add_all_function,
        delete_function,
        item_height_change_function,
        SwitchWaypoint,
        SwitchHanger,
        PTZ_headingPitchRoll_Change,
        EditseleteIndex,
        addNotFlyZone
    };
};

export default useWayLine3DHooks;