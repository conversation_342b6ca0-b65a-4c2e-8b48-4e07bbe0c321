import { theme } from 'antd';

// SI系统全局主题配置
const SITheme = {
  algorithm: theme.darkAlgorithm,
  token: {
    colorPrimary: '#08E7CB',
    colorBgBase: '#091A1B', // 设置透明背景和文字色
    colorBgContainer: 'rgba(0,0,0,0)', // 卡片、组件背景透明
    colorTextBase: '#ffffff', // 文字颜色
    colorBorder: '#08E7CB', // 边框颜色
    colorSplit: '#08E7CB', // 分割线
    colorBgLayout: 'transparent', // Layout
    colorTextPlaceholder:"rgba(26, 188, 156, 1)"//控制占位文本的颜色
  },
  components: {
    Table: {
      // borderColor: '#08E7CB',
    },
    Layout: {
      siderBg: 'transparent',
    },
    // 表单组件
    Form: {
      labelColor: '#08E7CB',
      labelFontWeight: 500,
    },
    // 输入框组件
    Input: {
      // colorBgContainer: 'rgba(0, 0, 0, 0.3)',
      // colorBorder: '#08E7CB',
      // colorText: '#ffffff',
      // colorTextPlaceholder: 'rgba(255, 255, 255, 0.6)',
      // activeBorderColor: '#08E7CB',
      // hoverBorderColor: '#08E7CB',
      // activeShadow: '0 0 8px rgba(26, 188, 156, 0.3)',
    },
    // 选择器组件
    Select: {
      // colorBgContainer: 'rgba(0, 0, 0, 0.3)',
      // colorBorder: '#08E7CB',
      // colorText: '#ffffff',
      // colorTextPlaceholder: 'rgba(255, 255, 255, 0.6)',
      // activeBorderColor: '#08E7CB',
      // hoverBorderColor: '#08E7CB',
      // activeShadow: '0 0 8px rgba(26, 188, 156, 0.3)',
      // colorBgElevated: 'rgba(18, 40, 50, 0.95)', // 下拉框背景
      // optionSelectedBg: 'rgba(26, 188, 156, 0.3)',
      // optionActiveBg: 'rgba(26, 188, 156, 0.1)',
      // 清除按钮和箭头样式
      // clearBg: 'transparent',
      // selectorBg: 'rgba(0, 0, 0, 0.3)',
    },
    // 日期选择器组件
    DatePicker: {
      // colorBgContainer: 'rgba(0, 0, 0, 0.3)',
      // colorBorder: '#08E7CB',
      // colorText: '#ffffff',
      // colorTextPlaceholder: 'rgba(255, 255, 255, 0.6)',
      // activeBorderColor: '#08E7CB',
      // hoverBorderColor: '#08E7CB',
      // activeShadow: '0 0 8px rgba(26, 188, 156, 0.3)',
      // colorBgElevated: 'rgba(18, 40, 50, 0.95)',
    },
    // 按钮组件
    Button: {
      // primaryShadow: '0 4px 12px rgba(26, 188, 156, 0.3)',
      // defaultBg: 'rgba(0, 0, 0, 0.3)',
      // defaultBorderColor: '#08E7CB',
      // defaultColor: '#08E7CB',
      // defaultHoverBg: 'rgba(26, 188, 156, 0.1)',
      // defaultHoverBorderColor: '#00f5c4',
      // defaultHoverColor: '#00f5c4',
    },
    Card: {
      colorBorderSecondary: 'transparent', // 透明
    },
  },
};

export default SITheme;
