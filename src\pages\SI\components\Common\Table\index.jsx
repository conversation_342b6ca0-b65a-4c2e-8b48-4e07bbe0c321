import React, { useState } from 'react';
import { Table, Button, Space, message, Tooltip } from 'antd';
import './index.less'; // 引入自定义样式

const DynamicDataTable = ({ 
  // 数据相关
  dataSource = [], 
  loading = false, 
  
  // 列配置
  columns = [], // 完全可配置的列定义
  
  // 分页配置
  pagination = false, // 可以是 false 或 pagination 配置对象
  onPageChange,
  
  // 选择配置
  rowSelection = null, // 可以是 null 或 rowSelection 配置对象
  
  // 工具栏配置
  showToolbar = false, // 是否显示工具栏
  toolbarButtons = [], // 工具栏按钮配置
  toolbarAlign = 'left', // 工具栏对齐方式: 'left' | 'center' | 'right' | 'space-between'
  showSelectionInfo = false, // 是否显示选择信息
  showTotal = false, // 是否显示总数信息
  
  // 表格样式配置 - 完全兼容 Antd Table 属性
  scroll,
  size = 'default', // 'default' | 'middle' | 'small'
  bordered = false, // 是否显示边框，默认false保持antd一致
  showHeader = true, // 是否显示表头，默认true保持antd一致
  tableLayout, // 'auto' | 'fixed' | undefined
  sticky, // 粘性头部和滚动条
  
  // 样式相关
  tableStyle = {},
  containerStyle = {},
  className,
  tableClassName,
  
  // 行样式配置
  rowClassName, // 行样式类名函数或字符串
  onRow, // 行事件处理
  
  // 其他 Antd Table 原生属性
  expandable, // 可展开配置
  footer, // 表格尾部
  title, // 表格标题
  showSorterTooltip = true, // 是否显示排序提示，默认true保持antd一致
  sortDirections = ['ascend', 'descend'], // 排序方向，默认保持antd一致
  
  // 虚拟滚动
  virtual,
  
  // 自定义渲染
  customRender = {}, // 自定义渲染函数集合
  
  // 其他回调函数
  onChange, // 分页、排序、筛选变化时触发
  onHeaderRow, // 设置头部行属性
  onSelectChange: onSelectChangeProp, // 选择变化回调
  
  // 其余所有 antd Table 属性
  ...restTableProps
}) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  // 处理行选择变化
  const onSelectChange = (newSelectedRowKeys) => {
    setSelectedRowKeys(newSelectedRowKeys);
    // 优先使用传入的回调
    if (onSelectChangeProp) {
      onSelectChangeProp(newSelectedRowKeys);
    } else if (rowSelection?.onChange) {
      rowSelection.onChange(newSelectedRowKeys);
    }
  };

  // 处理工具栏按钮点击
  const handleToolbarButtonClick = (button) => {
    if (button.needsSelection && selectedRowKeys.length === 0) {
      message.warning(button.selectionMessage || '请选择要操作的数据');
      return;
    }
    
    if (button.onClick) {
      button.onClick(selectedRowKeys, dataSource);
    }
  };

  // 渲染工具栏按钮
  const renderToolbarButtons = () => {
    return toolbarButtons.map((button, index) => {
      const {
        key,
        text,
        type = 'default',
        icon,
        disabled,
        needsSelection = false,
        loading: buttonLoading = false,
        ...buttonProps
      } = button;

      const isDisabled = disabled || (needsSelection && selectedRowKeys.length === 0);

      return (
        <Button
          key={key || index}
          type={type}
          icon={icon}
          onClick={() => handleToolbarButtonClick(button)}
          disabled={isDisabled}
          loading={buttonLoading}
          {...buttonProps}
        >
          {text}
        </Button>
      );
    });
  };

  // 渲染工具栏
  const renderToolbar = () => {
    if (!showToolbar) return null;

    const toolbarItems = renderToolbarButtons();
    const selectionInfo = showSelectionInfo && selectedRowKeys.length > 0 && (
      <span style={{ color: '#00d4aa' }}>已选择 {selectedRowKeys.length} 项</span>
    );

    const toolbarContent = [];
    
    if (toolbarAlign === 'space-between') {
      toolbarContent.push(
        <div key="left" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Space>{toolbarItems}</Space>
          {selectionInfo}
        </div>
      );
    } else {
      const allItems = [...toolbarItems];
      if (selectionInfo) allItems.push(selectionInfo);
      
      toolbarContent.push(
        <Space key="content">{allItems}</Space>
      );
    }

    return (
      <div
        style={{
          marginBottom: '16px',
          display: 'flex',
          justifyContent: toolbarAlign === 'space-between' ? 'space-between' : toolbarAlign,
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: '8px'
        }}
      >
        {toolbarContent}
      </div>
    );
  };

  // 处理列定义中的自定义渲染和省略号
  const processedColumns = columns.map(column => {
    const processedColumn = { ...column };
    
    // 如果有自定义渲染
    if (column.customRender && customRender[column.customRender]) {
      processedColumn.render = customRender[column.customRender];
    }
    
    // 如果启用了省略号且没有自定义渲染，添加Tooltip支持
    if (column.ellipsis && !column.customRender) {
      processedColumn.ellipsis = {
        showTitle: false,
      };
      
      // 如果没有自定义render，添加Tooltip渲染
      if (!processedColumn.render) {
        processedColumn.render = (text) => (
          <Tooltip placement="topLeft" title={text}>
            <span>{text}</span>
          </Tooltip>
        );
      }
    }
    
    return processedColumn;
  });

  // 构建行选择配置
  const finalRowSelection = rowSelection ? {
    selectedRowKeys,
    onChange: onSelectChange,
    ...rowSelection
  } : undefined;

  // 构建分页配置
  const finalPagination = pagination ? {
    onChange: onPageChange,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `共 ${total} 条`,
    ...pagination
  } : false;

  // 默认行样式类名函数 - 实现斑马线条纹效果
  const defaultRowClassName = (record, index) => {
    let classNames = ['dynamic-table-row'];
    
    // 斑马线条纹效果 - 偶数行添加背景色
    if (index % 2 === 0) {
      classNames.push('dynamic-table-row-even');
    }
    
    // 如果传入了自定义 rowClassName
    if (typeof rowClassName === 'function') {
      const customClass = rowClassName(record, index);
      if (customClass) classNames.push(customClass);
    } else if (typeof rowClassName === 'string') {
      classNames.push(rowClassName);
    }
    
    return classNames.join(' ');
  };

  return (
    <div style={containerStyle} className={`dynamic-table-container ${className || ''}`}>
      {renderToolbar()}
      
      <Table
        // 数据相关
        dataSource={dataSource}
        columns={processedColumns}
        loading={loading}
        
        // 选择相关
        rowSelection={finalRowSelection}
        
        // 分页相关
        pagination={finalPagination}
        
        // 样式相关 - 完全兼容 Antd Table
        size={size}
        bordered={bordered}
        showHeader={showHeader}
        tableLayout={tableLayout}
        sticky={sticky}
        scroll={scroll}
        
        // 行相关
        rowClassName={defaultRowClassName}
        onRow={onRow}
        
        // 展开相关
        expandable={expandable}
        
        // 其他配置
        footer={footer}
        title={title}
        showSorterTooltip={showSorterTooltip}
        sortDirections={sortDirections}
        virtual={virtual}
        
        // 回调函数
        onChange={onChange}
        onHeaderRow={onHeaderRow}
        
        // 样式
        style={tableStyle}
        className={`dynamic-table ${tableClassName || ''}`}
        
        // 传递所有其他 antd Table 属性
        {...restTableProps}
      />
    </div>
  );
};

export default DynamicDataTable; 