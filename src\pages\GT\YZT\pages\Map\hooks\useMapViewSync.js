import { useEffect, useCallback, useRef, useState } from 'react';
import { Cesium } from 'umi';
import mapViewSynchronizer from '../services/MapViewSynchronizer';

// 防抖函数，避免频繁触发视图同步
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * 自定义 Hook，用于集成地图视图同步功能
 * 支持 2D (Leaflet) 和 3D (Cesium) 地图
 *
 * @param {Object} mapInstance - 地图实例（Leaflet 或 Cesium）
 * @param {string} mapId - 地图唯一标识
 * @param {boolean} syncEnabled - 是否启用同步
 * @param {string} syncMode - 同步模式：'off' | 'on_end' | 'realtime'
 * @param {string} mapType - 地图类型，'2D' 或 '3D'
 * @param {Object} options - 额外配置选项
 * @param {number} options.debounceTime - 防抖时间（毫秒）
 * @param {boolean} options.smoothAnimation - 是否使用平滑动画
 * @returns {Object} - 包含处理地图视图变化的方法和状态
 */
const useMapViewSync = (
  mapInstance,
  mapId,
  syncEnabled = false,
  syncMode = 'on_end', // 新增同步模式参数
  mapType = '2D',
  options = {
    debounceTime: mapType === '3D' ? 500 : 400, // 3D 地图 500ms，2D 地图 400ms
    smoothAnimation: true
  }
) => {
  // 保存上一次的视图状态，用于比较变化
  const lastViewStateRef = useRef(null);

  // 错误状态
  const errorRef = useRef(null);

  // 防止同步循环的标志
  const isReceivingUpdateRef = useRef(false);

  // 事件转发相关状态
  const isDraggingRef = useRef(false);
  const lastMousePosRef = useRef({ x: 0, y: 0 });
  const eventThrottleRef = useRef(null);
  const [virtualMousePos, setVirtualMousePos] = useState(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isZooming, setIsZooming] = useState(false);
  // 设置同步启用状态
  useEffect(() => {
    mapViewSynchronizer.setEnabled(syncEnabled && syncMode !== 'off');
  }, [syncEnabled, syncMode]);

  // 处理来自其他地图的事件（新增）
  const handleExternalMapEvent = useCallback((eventData) => {
    if (!mapInstance || isReceivingUpdateRef.current) return;

    const { type, payload } = eventData;
    isReceivingUpdateRef.current = true;

    try {
      // 更新虚拟鼠标位置
      if (payload.screenPoint) {
        setVirtualMousePos(payload.screenPoint);
      }
      if (type === 'mouseleave') {
        setVirtualMousePos(null);
        setIsDragging(false);
        setIsZooming(false);
      }

      // 更新操作状态
      if (type === 'mousedown') {
        setIsDragging(true);
      } else if (type === 'mouseup') {
        setIsDragging(false);
      } else if (type === 'zoom') {
        setIsZooming(true);
        setTimeout(() => setIsZooming(false), 300); // 缩放状态持续300ms
      }

      // 根据地图类型执行相应操作
      if (mapType === '2D') {
        handleLeafletEvent(type, payload);
      } else if (mapType === '3D') {
        handleCesiumEvent(type, payload);
      }
    } finally {
      setTimeout(() => {
        isReceivingUpdateRef.current = false;
      }, 50);
    }
  }, [mapInstance, mapType]);

  // 2D地图事件处理
  const handleLeafletEvent = useCallback((type, payload) => {
    if (!mapInstance) return;

    switch (type) {
      case 'pan':
        if (mapInstance.panBy && typeof mapInstance.panBy === 'function') {
          mapInstance.panBy([payload.dx, payload.dy], { animate: false });
        }
        break;
      case 'zoom':
        if (payload.delta && mapInstance.zoomIn && mapInstance.zoomOut) {
          const method = payload.delta > 0 ? 'zoomIn' : 'zoomOut';
          const options = { animate: false };

          // 如果有锚点，使用锚点缩放
          if (payload.anchorPoint && mapInstance.containerPointToLatLng) {
            try {
              const latLng = mapInstance.containerPointToLatLng([
                payload.anchorPoint.x,
                payload.anchorPoint.y
              ]);
              options.anchor = latLng;
            } catch (error) {
              console.warn('锚点转换失败，使用默认缩放:', error);
            }
          }

          mapInstance[method](Math.abs(payload.delta), options);
        }
        break;
    }
  }, [mapInstance]);

  // 3D地图事件处理
  const handleCesiumEvent = useCallback((type, payload) => {
    if (!mapInstance || !mapInstance.camera) return;

    const camera = mapInstance.camera;

    switch (type) {
      case 'pan':
        try {
          // Cesium相机平移 - 使用相对移动
          const moveRate = camera.positionCartographic.height / 100000; // 根据高度调整移动速率
          const moveVector = new Cesium.Cartesian3(
            -payload.dx * moveRate,
            payload.dy * moveRate,
            0
          );
          camera.move(moveVector, moveRate);
        } catch (error) {
          console.warn('Cesium平移操作失败:', error);
        }
        break;
      case 'zoom':
        try {
          // Cesium相机缩放
          const zoomRate = camera.positionCartographic.height / 10; // 根据高度调整缩放速率
          if (payload.delta > 0) {
            camera.zoomIn(zoomRate);
          } else {
            camera.zoomOut(zoomRate);
          }
        } catch (error) {
          console.warn('Cesium缩放操作失败:', error);
        }
        break;
    }
  }, [mapInstance]);

  // 处理来自其他地图的视图更新（保持现有功能）
  const handleExternalViewUpdate = useCallback((viewState) => {
    if (!mapInstance) return;

    // 设置接收更新标志，防止触发同步循环
    isReceivingUpdateRef.current = true;

    try {
      // 根据地图类型选择不同的更新方法
      if (mapType === '2D') {
        // Leaflet 地图视图更新
        if (viewState.center && typeof mapInstance.setView === 'function') {
          mapInstance.setView(
            [viewState.center.lat, viewState.center.lng],
            Math.round(viewState.zoom * 100) / 100, // 四舍五入到两位小数，避免精度问题
            {
              animate: true,
              duration: 0.5
            }
          );
        }
      } else if (mapType === '3D') {
        // Cesium 地图视图更新 - 优化同步性能
        if (mapInstance.camera && typeof mapInstance.camera.setView === 'function') {
          try {
            // 使用 setView 而不是 flyTo，避免动画冲突和延迟
            mapInstance.camera.setView({
              destination: Cesium.Cartesian3.fromDegrees(
                viewState.center.lng,
                viewState.center.lat,
                viewState.height || 1000
              ),
              orientation: {
                heading: viewState.heading || 0,
                pitch: viewState.pitch || -Math.PI/2,
                roll: viewState.roll || 0
              }
            });
          } catch (error) {
            console.warn(`地图 ${mapId} Cesium setView 失败，尝试使用 flyTo:`, error);
            // 如果 setView 失败，回退到 flyTo 但使用更短的动画时间
            if (typeof mapInstance.camera.flyTo === 'function') {
              mapInstance.camera.flyTo({
                destination: Cesium.Cartesian3.fromDegrees(
                  viewState.center.lng,
                  viewState.center.lat,
                  viewState.height || 1000
                ),
                orientation: {
                  heading: viewState.heading || 0,
                  pitch: viewState.pitch || -Math.PI/2,
                  roll: viewState.roll || 0
                },
                duration: 0.1 // 减少动画时间，降低延迟感
              });
            }
          }
        }
      }
    } finally {
      // 延迟重置标志，确保视图更新完成
      setTimeout(() => {
        isReceivingUpdateRef.current = false;
      }, 100);
    }
  }, [mapInstance, mapType, mapId]);

  // 处理当前地图的视图变化（使用防抖）
  const handleViewChange = useCallback(
    debounce((viewState) => {
      if (!mapId || !syncEnabled) return;

      // 如果正在接收外部更新，跳过发送同步通知，防止循环
      if (isReceivingUpdateRef.current) {
        console.log(`地图 ${mapId} 正在接收外部更新，跳过同步通知`);
        return;
      }

      try {
        // 检查视图状态是否有效
        if (!viewState || !viewState.center) {
          console.warn(`地图 ${mapId} 的视图状态无效`, viewState);
          return;
        }
        
        // 检查视图状态是否与上一次相同
        const lastViewState = lastViewStateRef.current;
        if (lastViewState) {
          const isSameCenter =
            Math.abs(lastViewState.center.lat - viewState.center.lat) < 0.000001 &&
            Math.abs(lastViewState.center.lng - viewState.center.lng) < 0.000001;

          let isSameView = isSameCenter;

          // 根据地图类型检查不同的视图属性
          if (viewState.zoom !== undefined && lastViewState.zoom !== undefined) {
            // 2D 地图：检查缩放级别，使用容差避免精度问题
            const zoomTolerance = 0.01; // 缩放级别容差
            const isSameZoom = Math.abs(lastViewState.zoom - viewState.zoom) < zoomTolerance;
            isSameView = isSameView && isSameZoom;
          } else if (viewState.height !== undefined && lastViewState.height !== undefined) {
            // 3D 地图：检查高度、朝向等
            const isSameHeight = Math.abs(lastViewState.height - viewState.height) < 1.0;
            const isSameHeading = Math.abs((lastViewState.heading || 0) - (viewState.heading || 0)) < 0.01;
            const isSamePitch = Math.abs((lastViewState.pitch || 0) - (viewState.pitch || 0)) < 0.01;
            isSameView = isSameView && isSameHeight && isSameHeading && isSamePitch;
          }

          if (isSameView) {
            // 视图没有实质性变化，跳过更新
            return;
          }
        }
        
        // 更新上一次视图状态
        lastViewStateRef.current = { ...viewState };
        
        // 通知其他地图
        mapViewSynchronizer.notifyViewChange(mapId, viewState);
      } catch (error) {
        console.error(`地图 ${mapId} 处理视图变化时出错:`, error);
        errorRef.current = error;
      }
    }, options.debounceTime),
    [mapId, syncEnabled, options.debounceTime]
  );

  // 获取当前地图视图状态
  const getCurrentViewState = useCallback(() => {
    if (!mapInstance) return null;
    // console.log('getCurrentViewState', mapType, mapInstance);

    if (mapType === '2D') {
      // Leaflet 地图
      const center = mapInstance.getCenter();
      const zoom = mapInstance.getZoom();

      return {
        center: {
          lat: Math.round(center.lat * 1000000) / 1000000, // 保留6位小数
          lng: Math.round(center.lng * 1000000) / 1000000  // 保留6位小数
        },
        zoom: Math.round(zoom * 100) / 100 // 保留2位小数，避免精度问题
      };
    } else if (mapType === '3D') {
      // Cesium 地图
      const camera = mapInstance.camera;
      if (!camera) return null;
      
      const position = camera.positionCartographic;
      
      return {
        center: {
          lat: Cesium.Math.toDegrees(position.latitude),
          lng: Cesium.Math.toDegrees(position.longitude)
        },
        height: position.height,
        heading: camera.heading,
        pitch: camera.pitch,
        roll: camera.roll
      };
    }
    
    return null;
  }, [mapInstance, mapType]);

  // 订阅和取消订阅同步服务
  useEffect(() => {
    if (!mapInstance || !mapId) return;

    // 只有启用同步时才订阅同步服务
    if (syncEnabled) {
      // 订阅同步服务
      mapViewSynchronizer.subscribe(mapId, mapInstance, {
        onViewChange: handleExternalViewUpdate
      });

      console.log(`地图 ${mapId} 已订阅视图同步服务`);
    }

    // 组件卸载时或同步状态变化时取消订阅
    return () => {
      if (mapViewSynchronizer.hasObserver(mapId)) {
        mapViewSynchronizer.unsubscribe(mapId);
        console.log(`地图 ${mapId} 已取消订阅视图同步服务`);
      }
    };
  }, [mapInstance, mapId, syncEnabled, handleExternalViewUpdate]);

  // 设置地图移动事件监听
  useEffect(() => {
    if (!mapInstance || !mapId || !syncEnabled) return;

    let moveEndHandler;
    let moveHandler;
    
    try {
      if (mapType === '2D') {
        // Leaflet 地图移动事件
        moveEndHandler = () => {
          const viewState = getCurrentViewState();
          if (viewState) {
            handleViewChange(viewState);
          }
        };
        
        // 监听 moveend 事件（地图移动结束）
        mapInstance.on('moveend', moveEndHandler);
        
        // 可选：监听 move 事件（地图移动中），用于更实时的同步
        if (options.realtimeSync) {
          moveHandler = debounce(() => {
            const viewState = getCurrentViewState();
            if (viewState) {
              handleViewChange(viewState);
            }
          }, 100); // 使用更短的防抖时间
          
          mapInstance.on('move', moveHandler);
        }
      } else if (mapType === '3D') {
        // Cesium 地图移动事件
        moveEndHandler = () => {
          const viewState = getCurrentViewState();
          if (viewState) {
            handleViewChange(viewState);
          }
        };
        
        if (mapInstance.camera) {
          // 监听 moveEnd 事件（相机移动结束）
          mapInstance.camera.moveEnd.addEventListener(moveEndHandler);
          
          // 可选：监听 changed 事件（相机变化中），用于更实时的同步
          if (options.realtimeSync) {
            moveHandler = debounce(() => {
              const viewState = getCurrentViewState();
              if (viewState) {
                handleViewChange(viewState);
              }
            }, 100);
            
            mapInstance.camera.changed.addEventListener(moveHandler);
          }
        }
      }
    } catch (error) {
      console.error(`地图 ${mapId} 设置事件监听时出错:`, error);
      errorRef.current = error;
    }

    // 清理事件监听
    return () => {
      try {
        if (mapType === '2D') {
          mapInstance.off('moveend', moveEndHandler);
          if (options.realtimeSync) {
            mapInstance.off('move', moveHandler);
          }
        } else if (mapType === '3D' && mapInstance.camera) {
          mapInstance.camera.moveEnd.removeEventListener(moveEndHandler);
          if (options.realtimeSync) {
            mapInstance.camera.changed.removeEventListener(moveHandler);
          }
        }
      } catch (error) {
        console.error(`地图 ${mapId} 清理事件监听时出错:`, error);
      }
    };
  }, [mapInstance, mapId, syncEnabled, mapType, getCurrentViewState, handleViewChange, options.realtimeSync]);

  // 手动触发视图同步
  const syncViewNow = useCallback(() => {
    if (!mapInstance || !mapId || !syncEnabled) return false;
    
    try {
      const viewState = getCurrentViewState();
      if (viewState) {
        // 绕过防抖，立即同步
        mapViewSynchronizer.notifyViewChange(mapId, viewState);
        return true;
      }
    } catch (error) {
      console.error(`地图 ${mapId} 手动同步视图时出错:`, error);
      errorRef.current = error;
    }
    
    return false;
  }, [mapInstance, mapId, syncEnabled, getCurrentViewState]);
  
  // 重置错误状态
  const resetError = useCallback(() => {
    errorRef.current = null;
  }, []);

  return {
    // 核心方法
    handleViewChange,
    getCurrentViewState,
    syncViewNow,
    
    // 状态信息
    isSyncEnabled: syncEnabled,
    hasError: !!errorRef.current,
    error: errorRef.current,
    resetError,
    
    // 同步服务状态
    getSyncStatus: () => mapViewSynchronizer.isEnabled(),
    getObserverCount: () => mapViewSynchronizer.getObserverCount(),
    isSubscribed: () => mapViewSynchronizer.hasObserver(mapId)
  };
};

export default useMapViewSync;