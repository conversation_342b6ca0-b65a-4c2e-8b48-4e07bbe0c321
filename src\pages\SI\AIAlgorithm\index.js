import React, { useState, useEffect } from 'react';
import { But<PERSON>, Card, Row, Col, Tooltip, ConfigProvider } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import './index.less';
import { useModel } from 'umi';
import locale from 'antd/locale/zh_CN';
import { queryPage } from '@/utils/MyRoute';
import getMenuItem from '@/pages/SI/utils/getMenuItem';
// 组件
import MyMenu from '@/pages/SI/components/MyMenu';
import HeadTabs from '@/pages/SI/components/HeadTabs';

// 组件
import ModelListPage from '@/pages/DJI/AIPage/ModelListPage3';
import AlgorithmModel from '@/pages/SI/AIAlgorithm/Pages/AlgorithmModel';
import ModelManagement from '@/pages/SI/AIAlgorithm/Pages/ModelManagement';
import ModelTraining from '@/pages/SI/AIAlgorithm/Pages/ModelTraining';

// 算法仓菜单项
const menuItems = [getMenuItem('算法模型', '算法模型', null), getMenuItem('任务管理', '任务管理', null), getMenuItem('模型训练', '模型训练', null)];

// 子页面映射表
const subPageMap = {
  // 算法模型: <AlgorithmModel />,
  算法模型: <ModelListPage isSipage={true} />,
  任务管理: <ModelManagement />,
  模型训练: <ModelTraining />,
};

const AIAlgorithm = () => {
  const [collapsed, setCollapsed] = useState(false);
  const { page, setPage } = useModel('pageModel');

  // 初始化页面
  useEffect(() => {
    setPage(subPageMap['算法模型']);
  }, []);

  // 处理菜单点击
  const handleMenuSelect = key => {
    // 设置当前页面
    setPage(subPageMap[key]);
  };
  // 处理标签页切换
  const handleTabChange = key => {
    if (key) {
      setPage(subPageMap[key]);
    } else {
      console.warn('[ControlCenter] 未找到标签页key对应的标题:', key);
    }
  };

  return (
    <div style={{ height: '100%', overflow: 'hidden', position: 'relative' }}>
      {/* 侧边菜单 */}
      {/* <MyMenu handlePageChange={handleMenuSelect} setCollapsed={setCollapsed} collapsed={collapsed} menuItems={menuItems} /> */}
      {/* 主内容区域 */}
      <div
        style={{
          // marginLeft: collapsed ? 80 : 160,
          transition: 'margin-left 0.2s',
          height: '100%',
          overflow: 'hidden',
          // width: 'calc(100vw - 160px)',
        }}
      >
        <ConfigProvider locale={locale}>
          {/* <HeadTabs handletabChange={handleTabChange} menuItems={menuItems} /> */}
          <div style={{ height: 'calc(100% - 32px)', width: '100%', overflow: 'hidden' }}>{page}</div>
        </ConfigProvider>
      </div>
    </div>
  );
};

export default AIAlgorithm;
