import {
  getBody<PERSON>,
  getBodyW,
  isEmpty,
} from "@/utils/utils";
import { useEffect, useRef, useState } from "react";
import {Tooltip} from "antd";
import styles from "@/pages/DJI/DevicePage/index.less";
import TopPanel from "./Panels/TopPanel";
import IfShowPanel from "@/components/IfShowPanel";
import HmsPanel from "@/pages/DJI/HmsPage/HmsPanel";
import JoyStick from "./joy";
import {FJStart,FJStart2,FJClose, FJStart3,JCStart,JCStart2} from '@/pages/DJI/DRCPage/Panels/RtmpChange'
import OsdPanel from "./Panels/YiBiaoPanel";
import MapPanel from "./Panels/mapPanel";
import WebRtcPlayer from '@/components/WebRtcPlayer';
import { useModel } from "umi";
import cvImg from "@/assets/drcImgs/cv3.png";
import homeImg from "@/assets/drcImgs/home.png";
import fcontinue from "@/assets/drcImgs/fcontinue.png";
import fstop from "@/assets/drcImgs/fstop.png";
import stop from "@/assets/drcImgs/stop.png";
import XinTiao from "./Panels/DrcXinTiao";
import jianpanImg from "@/assets/drcImgs/jianpan.png";
import BianJiaoPanel from "./Panels/BianJiaoPanel";
import BianJiaoPanel2 from "./Panels/BianJiaoPanel2";
import CameraPanel from "./Panels/CameraPanel";
import CameraPanel2 from "./Panels/CameraPanel2";
import { ToHome, WayLinePause, WayLineRecory } from "../FlyToPage/helper";
import KeyboardPanel from "./Panels/KeyBoradPanel";
import { PlusOutlined } from "@ant-design/icons";
import { getMqttTcpAddress } from "@/utils/config";
import MouseCtrPanel from "../FlyToPage/MouseCtrPanel";

const DRCPage = ({ device }) => {
  const [url, setUrl] = useState(1);
 // const [p1, setP1] = useState(<div />);
 // const [p2, setP2] = useState(GetPlayer("100%", "100%", 1));
  
  const [ifY, setIfY] = useState(true);
  const { fj, fjData } = useModel("droneModel");
  const { DoCMD, DoCMD2 } = useModel("cmdModel");
  const {MqttConnect}=useModel('mqttModel');
  const { cameraJT } = useModel("rtmpModel");
  const { drcStatus, joyReason } = useModel("eventModel");
  const [jpOpen, setJpOpen] = useState(false);
  const { jcData } = useModel("dockModel");
  const { pData}=useModel('pageModel');

  const COrV = useRef(1);
  const seq = useRef(0);
  const drc = useRef({ x: 0, y: 0, h: 0, w: 0 });
  const col1 = "rgba(65, 65, 65, 0.8)";
  const col2 = 'rgba(255, 182, 0, 1)';
  const top0 = getBodyH(170);
  // const sn="7CTDLCE00AC2J4";
  // const sn2="1581F6Q8D23CT00A5N49";
  if (isEmpty(device)) {
    device = JSON.parse(localStorage.getItem("device"));
  }

  const onClickYY = () => {
    console.log(ifY);
    setIfY(!ifY);
  };
  const getJData = (v1, min, max) => {
    if (v2 == 80) return 0;
    const v2 = ((v1 - 40) / 80) * (max - min) + min;
    return v2;
  };

  const JoyHandle1 = (d) => {
    const v1 = getJData(d.xPosition, -5, 5);
    const v2 = -1 * getJData(d.yPosition, -4, 4);
    drc.current = { ...drc.current, w: v1, h: v2 };
    sendData(v1, v2, drc.current.x, drc.current.y);
  };
  const JoyHandle2 = (d) => {
    const v1 = -1 * getJData(d.yPosition, -12, 12);
    const v2 = getJData(d.xPosition, -10, 10);
    drc.current = { ...drc.current, x: v1, y: v2 };
    sendData(drc.current.w, drc.current.h, v1, v2);
  };
  const JoyHandle = (x, y, w, h) => {
    drc.current = { w, h, x, y };
    sendData(w, h, x, y);
    // if(x!=null){
    //   drc.current = { ...drc.current, x: x };
    //   sendData(drc.current.w, drc.current.h, x,drc.current.y );
    // }
    // if(y!=null){
    //   drc.current = { ...drc.current, y: y };
    //   sendData(drc.current.w, drc.current.h, drc.current.x,y );
    // }
    // if(w!=null){
    //   drc.current = { ...drc.current, w: w };
    //   sendData(w, drc.current.h, drc.current.x,drc.current.y );
    // }
    // if(h!=null){
    //   drc.current = { ...drc.current, h: h };
    //   sendData(drc.current.w,h, drc.current.x,drc.current.y );
    // }
  };
  const beginX = () => {
    seq.current = 0;
    const data = {
      hsi_frequency: 1,
      mqtt_broker: {
        address: getMqttTcpAddress(),
        client_id: device.SN2,
        enable_tls: false,
        expire_time: 1872744922,
        password: "",
        username: "",
      },
      osd_frequency: 10,
    };
    const data2 = {
      payload_index: device.Camera2,
    };
    DoCMD(device.SN, "drc_mode_enter", data);
    DoCMD(device.SN, "payload_authority_grab", data2);
  };

  const GetDroneName = (x1) => {
    if (x1 == "0-91-0") return "Matrice 3D";
    if (x1 == "0-91-1") return "Matrice 3TD";
    if (x1 == "0-67-0") return "Matrice 30";
    if (x1 == "0-67-1") return "Matrice 30T";
    return x1;
  };
  useEffect(()=>{
    MqttConnect(device);
  },[])
  useEffect(() => {
    // FJStart2(device,device.SN2); //声网直播下关闭飞机镜头
    // setP1(GetPlayer("100%", getBodyH(58), 2, device.SN)); //声网直播
    
    // FJStart3(device,device.SN2); //开启飞机镜头
    FJStart3(device,device.SN2)
   // setP1(GetPlayer("100%", getBodyH(90), 12,device.SN2,"isDrc")) ; // webrtc直播

    // setP1(<WebRtc SN={device.SN2}></WebRtc>)
    //setP1(<div style={{background:'blace',height:getBodyH(58),width:'100%'}}/>)
    // if(pData.current.LiveJT!="飞机云直播"){
    //   pData.current.LiveJT="飞机云直播";
    //   FJStart2(device);
    // }

    // setP2(<DJBaseMap device={device} sn={device.SN} h1={240} />);

    const nL1 = ["上升(Z)", "下降(C)", "左旋(Q)", "右旋(E)"];
    const nL2 = ["前进(W)", "后退(S)", "左移(A)", "右移(D)"];
    const Joy1 = new JoyStick(
      "joyDiv1",
      {
        internalFillColor: col1,
        internalStrokeColor: col2,
        externalStrokeColor: col1,
        nL: nL1,
      },
      JoyHandle1
    );
    const Joy2 = new JoyStick(
      "joyDiv2",
      {
        internalFillColor: col1,
        internalStrokeColor: col2,
        externalStrokeColor: col1,
        nL: nL2,
      },
      JoyHandle2
    );

    beginX();

    document.addEventListener(
      "touchmove",
      function (event) {
        // 阻止默认的下拉刷新行为
        event.preventDefault();
      },
      { passive: false }
    );
  }, []);

  if (drcStatus.current.drc_state == 0) {
    beginX();
  }

  // useEffect(()=>{
  //   if(drcStatus.current.drc_state==0){
  //     beginX();
  //   }
  // },[drcStatus.current]);


  const CameraDrag = (x, y) => {
    const data = {
      locked: false,
      payload_index: "80-0-0",
      pitch_speed: x,
      yaw_speed: y,
    };
    DoCMD(device.SN, "camera_screen_drag", data);
    // goPost("/api/v1/Camera/Control?sn=" + sn + "&m1=" + "camera_screen_drag", data)
  };

  const sendData = (w, w2, w3, w4) => {
    const data = {
      data: {
        h: w2,
        seq: seq.current,
        w: w,
        x: w3,
        y: w4,
      },
      method: "drone_control",
    };

    console.log("joy1", seq.current, data.data);

    DoCMD2(`thing/product/${device.SN}/drc/down`, data);
    seq.current = seq.current + 1;
  };

  const getBianJiao = () => {
    if (cameraJT == "wide") {
      return null;
    }

    if (cameraJT == "zoom") {
      return <BianJiaoPanel />;
    }

    if (cameraJT == "ir") {
      return <BianJiaoPanel2 />;
    }
  };

  const CameraRefrush = () => {
    const data = { payload_index: device.Camera2, reset_mode: COrV.current };
    DoCMD(device.SN, "gimbal_reset", data);
    if (COrV.current == 0) {
      COrV.current = 1;
    } else {
      COrV.current = 0;
    }
  };

  const cvDiv = (
    <div
      onClick={() => CameraRefrush()}
      style={{
        opacity: 0.6,
        zIndex: 1010,
        userSelect: "none",
        cursor: "pointer",
        position: "absolute",
        top: top0 + 80,
        left: 240,
        width: 48,
        height: 48,
      }}
    >
      <img height={36} width={24} src={cvImg} />
    </div>
  );
  const homeDiv = (
    <div
      style={{
        opacity: 0.6,
        zIndex: 1010,
        userSelect: "none",
        cursor: "pointer",
        position: "absolute",
        top: 190,
        left: 20,
        width: 36,
      }}
    >
      <Tooltip
        onClick={() => ToHome(device.SN)}
        placement="right"
        title="一键返航"
      >
        <img height={48} width={36} src={homeImg} />
      </Tooltip>
      <div style={{ marginTop: 16.0, marginLeft: 4.0 }}>
        <Tooltip
          onClick={() => WayLinePause(device.SN)}
          placement="right"
          title="航线暂停"
        >
          <img height={24} width={24} src={fstop} />
        </Tooltip>
      </div>
      <div style={{ marginTop: 24.0, marginLeft: 4.0 }}>
        <Tooltip
          onClick={() => WayLineRecory(device.SN)}
          placement="right"
          title="航线继续"
        >
          <img height={22} width={22} src={fcontinue} />
        </Tooltip>
      </div>
      <div style={{ marginTop: 24.0, marginLeft:0 }}>
        <Tooltip
          onClick={async () => {
            const gateway_sn = device.SN;
            const topic = `thing/product/${gateway_sn}/drc/down`;
            const data = {
              data: {},
              method: "drone_emergency_stop",
            };
            DoCMD2(topic, data);
          }}
          placement="right"
          title="飞行急停"
        >
          <img height={32} width={32} src={stop} />
        </Tooltip>
      </div>
    </div>
  );
  const jianpanDiv = (
    <div
      onClick={() => ToHome(device.SN)}
      style={{
        opacity: 0.6,
        zIndex: 1010,
        userSelect: "none",
        cursor: "pointer",
        position: "absolute",
        top: 320,
        left: 20,
        width: 48,
        height: 48,
      }}
    >
      <Tooltip placement="right" title="一返航">
        <img height={48} width={36} src={homeImg} />
      </Tooltip>
    </div>
  );

  const getHSZ=()=>{
    if(isEmpty(jcData.current)) return null;
    if(isEmpty(jcData.current.sub_device)) return null;

   if( GetDroneName(jcData.current.sub_device.device_model_key) === "Matrice 3D" ){
    return        <div
          style={{
            position: "fixed",
            left: "calc(49% - 20px)",
            top: "calc(51% - 20px)",
          }}
        >
          <PlusOutlined style={{ fontSize: "40px", color: "#ea0c0c" }} />
        </div>
    }
       
      return  <div style={{ position: "fixed", left: "48.2%", top: "51%" }}>
          <PlusOutlined style={{ fontSize: "40px", color: "#ea0c0c" }} />
        </div>
      
  }

  return (
    <div className={styles.IndexPageStyle} style={{ background: "black",height:'100vh' }}>
      {/* {isEmpty(device) ? <div /> : <WebSocketDemo sn={device.SN} sn2={device.SN2} />} */}
      <XinTiao device={device} DoCMD2={DoCMD2} />
      <KeyboardPanel joyHandle={JoyHandle} CameraDrag={CameraDrag} />
      <div
        className="container"
        style={{
          position: "relative",
          width: "100%",
          height: "100%",
          background: "black",
        }}
      >
        <TopPanel />
  
        <MouseCtrPanel child={<WebRtcPlayer  sn3={device.SN2} ctl={false} width={"100%"} height={getBodyH(61)} />}  sn={device.SN}/>
        <MapPanel />
        {getBianJiao()}
        <CameraPanel />
        <CameraPanel2 />
        {IfShowPanel(100, 800, 20, 360, 8, null, HmsPanel(), true)}
        {cvDiv}
        {homeDiv}
        {IfShowPanel(
          0,
          0,
          getBodyH(190),
          getBodyW(430),
          null,
          null,
          <OsdPanel></OsdPanel>,
          true
        )}
        <div
          id="joyDiv1"
          style={{
            display: "block",
            height: 160,
            width: 160,
            position: "absolute",
            zIndex: 1000,
            top: top0,
            left: 50,
          }}
        />
        <div
          id="joyDiv2"
          style={{
            display: "block",
            height: 160,
            width: 160,
            position: "absolute",
            zIndex: 1000,
            top: top0,
            right: 50,
          }}
        />
        {getHSZ()}
      </div>
    </div>
  );
};

export default DRCPage;
