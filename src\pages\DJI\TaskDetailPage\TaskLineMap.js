import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from 'react-leaflet'
import 'leaflet/dist/leaflet.css';
import { isEmpty, getBodyH } from '@/utils/utils'
import {  Location_Market5 } from '@/pages/Maps/dt_market';
import {Wgs84ToGcj02} from '@/pages/Maps/gps_helper';

const WayLineMap = (h1,data) => {

  if(isEmpty(data)) return<div></div>
  const device=data.device;
  if(isEmpty( device)) return <div></div>
  const center=Wgs84ToGcj02( device.Lat, device.Lng); 


  if(h1===0){
    h1 = getBodyH(56);
  }

  const getPList=(pL="")=>{
     // console.log('getPList',pL);
      const list=[];
      const xL=pL.split(";");
      xL.forEach(p => {
          const p1=p.split(",");
        //  console.log(p,p1[0],p1[1]);
          if(p1.length>1){
            const p2=Wgs84ToGcj02( Number(p1[1]), Number(p1[0]));
           // 
            list.push(p2)
          }
      });

   //   console.log('getPList',list);

      return list;
  }



  const xL=getPList(data.PointList);
  // console.log('getPList',xL)
  return (
    <div>

      <MapContainer layersOptions={null} attributionControl={null} zoomControl={null} preferCanvas={true} center={center} zoom={15} style={{ width:'100%', height: h1 }}>


      <TileLayer
    attribution={null}
 
    url="http://webrd01.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}"
/>
      {Location_Market5({Lat:center[0],Lng:center[1],DeviceName:device.DName})}
        <Polyline weight={1.5} color={'red'} positions={xL}>
          <Tooltip sticky>{'飞行轨迹'}</Tooltip>
        </Polyline>
      </MapContainer>
    </div>
  )
}


export default WayLineMap;