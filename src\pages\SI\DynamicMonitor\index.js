/** 动态监测 */
import React, { useState, useEffect, useRef } from 'react';
// 地图组件
import OneMap from '@/pages/GT/YZT/pages/Map/components/OneMap';
// 获取guid
// import { getGuid } from '@/utils/helper';
import LeftSidebar from './components/LeftSidebar';
import RightSidebar from './components/RightSidebar';
import MapControls from './components/MapControls';
import styles from './index.less';
// import { CaretLeftOutlined, CaretRightOutlined } from '@ant-design/icons';
import arrowIcon from '../assets/image/arrow.png';
import arrowLine from '../assets/image/arrow-line.svg';
import { useModel } from "umi";
import VideoPanel from '@/pages/DJI/OrgPage/components/VideoPanel';
import useDeviceStore from '@/stores/deviceStore';
import { Spin } from 'antd';

const DynamicMonitor = () => {
  const [leftCollapsed, setLeftCollapsed] = useState(false);
  const [rightCollapsed, setRightCollapsed] = useState(false);
  const [showLayerTreePanel, setShowLayerTreePanel] = useState(false);
  const [showBasemapPanelActive, setShowBasemapPanelActive] = useState(false);
  const [isMeasureToolActive, setIsMeasureToolActive] = useState(false);
  const { page, setPage } = useModel("pageModel");

  // 视频面板相关状态
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [showVideoPanel, setShowVideoPanel] = useState(false);

  const {
    deviceList,
    loading: deviceLoading,
    error: deviceError,
    fetchDeviceList,
    startAutoRefresh,
    stopAutoRefresh
  } = useDeviceStore();

  // Add this near the top of the component
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  const oneMapRef = useRef(null)

  // 初始化页面
  useEffect(() => {
    setPage(null);
  }, []);

  useEffect(() => {
    fetchDeviceList(true); // Force refresh to ensure fresh data

    // Set up auto-refresh
    const refreshInterval = 60000;
    startAutoRefresh(refreshInterval);

    // Cleanup on component unmount
    return () => {
      stopAutoRefresh();
    };
  }, [fetchDeviceList, startAutoRefresh, stopAutoRefresh]);

  useEffect(() => {
    if (deviceError) {
      console.error('Error fetching device data:', deviceError);
    }
  }, [deviceError]);

  useEffect(() => {
    if (!deviceLoading && isInitialLoading) {
      setIsInitialLoading(false);
    }
  }, [deviceLoading, isInitialLoading]);

  const handleRefreshDevices = () => {
    fetchDeviceList(true); // Force refresh
  };

  // 左侧机场列表视频图标点击，显示视频面板
  const handleVideoClick = (device) => {
    setSelectedDevice(device);
    setShowVideoPanel(true);
  };

  const handleAirportNameClick = (deviceInfo) =>{
    if (oneMapRef.current && typeof oneMapRef.current.setCenter === 'function') {
      oneMapRef.current.setCenter({
        latitude: deviceInfo.lat,
        longitude: deviceInfo.lng,
        height: deviceInfo.height,
        // ...deviceInfo
      })
    } else {
      console.warn('当前OneMap组件不可用或setCenter方法不存在')
    }
  }

  // 关闭视频面板
  const handleCloseVideoPanel = () => {
    setShowVideoPanel(false);
  };

  const handleMapControlClick = (controlType) => {
    if (controlType === 'layertree') {
      setShowLayerTreePanel(prev => !prev);
      setShowBasemapPanelActive(false);
      setIsMeasureToolActive(false);
    } else if (controlType === 'basemap') {
      setShowBasemapPanelActive(prev => !prev);
      setShowLayerTreePanel(false);
      setIsMeasureToolActive(false);
    } else if (controlType === 'measure') {
      setIsMeasureToolActive(prev => !prev);
      setShowLayerTreePanel(false);
      setShowBasemapPanelActive(false);
    }
  };

  const containerClasses = [
    styles.dynamicMonitorContainer,
    leftCollapsed ? styles.leftSidebarCollapsed : '',
    rightCollapsed ? styles.rightSidebarCollapsed : '',
  ].join(' ');

  return (
    <div className={containerClasses}>
      <div className={styles.mainContent}>
        {isInitialLoading ? (
          <div className={styles.loadingContainer}>
            <Spin tip="正在加载数据..." />
          </div>
        ) : page ? (
          // 如果有 page 内容
          <div style={{ position: 'relative', height: '100%' }}>
            {page}
          </div>
        ) : (
          // 默认加载OneMap组件
          <>
              <OneMap
                ref={oneMapRef}
                deviceList={deviceList}
                deviceLoading={deviceLoading}
                showTreePanel={true}
                showZoomControls={true}
                showMapInfoPanel={false}
                showMapScale={false}
                showMeasureBtn={false}
                showMapCompass={true}
                isLeftSidebarCollapsed={leftCollapsed}
                isRightSidebarCollapsed={rightCollapsed}
              />
              {/* <OneMap 
          showLayerTree={showLayerTreePanel} 
          showBasemapPanel={showBasemapPanelActive} 
          showMeasureTool={isMeasureToolActive}
        />
        <MapControls onControlClick={handleMapControlClick} /> */}
              <LeftSidebar
                collapsed={leftCollapsed}
                deviceList={deviceList}
                deviceLoading={deviceLoading}
                onRefresh={handleRefreshDevices}
                onVideoClick={handleVideoClick}
                onAirportNameClick={handleAirportNameClick}
              />
              <img
                onClick={() => setLeftCollapsed(!leftCollapsed)}
                src={arrowIcon}
                alt="Toggle Left Sidebar"
                className={`${styles.toggleIcon} ${styles.leftToggle} ${leftCollapsed ? styles.collapsedIcon : styles.expandedIconLeft}`}
              />
              <img src={arrowLine} alt="" className={`${styles.toggleLine} ${styles.leftLine}`} />

              <RightSidebar
                collapsed={rightCollapsed}
              />
              <img src={arrowLine} alt="" className={`${styles.toggleLine} ${styles.rightLine}`} />
              <img
                onClick={() => setRightCollapsed(!rightCollapsed)}
                src={arrowIcon}
                alt="Toggle Right Sidebar"
                className={`${styles.toggleIcon} ${styles.rightToggle} ${rightCollapsed ? styles.collapsedIcon : styles.expandedIconRight}`}
              />

              {/* 视频面板 */}
              <VideoPanel 
                device={selectedDevice} 
                visible={showVideoPanel} 
                onClose={handleCloseVideoPanel} 
              />
            </>
          )}

      </div>
    </div>
  );
};

export default DynamicMonitor;
