
import { isEmpty } from "@/utils/utils";
import { useState, useRef } from "react";
import { GetMqttClient } from "@/utils/websocket";
export default function drcModel() {

    const mqttC=useRef({})
    const osdData=useRef({})
    const hsiData=useRef({})
    const delayData=useRef({})
    const drcResult=useRef({})

    const [drc, setDRC] = useState({
		"attitude_head": 60,
		"gimbal_pitch": 60,
		"gimbal_roll": 60,
		"gimbal_yaw": 60,
		"height": 10,
		"latitude": 10,
		"longitude": 10,
		"speed_x": 10,
		"speed_y": 10,
		"speed_z": 10
	});
    const DrcMqttConn = (sn) => {
        if (!isEmpty(mqttC.current)) {
            mqttC.current.end();
          }
          mqttC.current = GetMqttClient()
          mqttC.current.on("message", (topic, message) => {
              updateVal(topic,message);
           });
       
        mqttC.current.unsubscribe('#')
        mqttC.current.subscribe("thing/product/"+sn+"/drc/up");
    }

    const updateVal=(t1,m1)=>{
        const data=JSON.parse( m1);
        if(data.method=='osd_info_push'){
            osdData.current=data.data;
            setDRC(data.data);
        }
        if(data.method=='hsi_info_push'){
            hsiData.current=data.data;
        }
        if(data.method=='delay_info_push'){
            delayData.current=data.data;
        }
        if(data.method=='drone_control'){
            console.log('drc','drone_control',data.data);
            console.log('drc','drone_control_mode_code_reason',data.data);
        }

    }

    return {drc,osdData, DrcMqttConn };
};