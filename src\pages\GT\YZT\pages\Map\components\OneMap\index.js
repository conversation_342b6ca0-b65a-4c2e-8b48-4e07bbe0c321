// 导入地图组件和React hooks
import React, { useState, useEffect, useMemo, useRef, useCallback, forwardRef } from 'react';
import { Tree, Input, message, Spin } from 'antd';
import LeafletMap from '@/pages/GT/components/Map/2DMap';
import CesiumMap from '@/pages/GT/components/Map/3DMap';
import useMapViewSync from '../../hooks/useMapViewSync';
import { FolderOutlined, GlobalOutlined, SearchOutlined, AppstoreOutlined, BarsOutlined, CheckCircleFilled, ProfileOutlined } from '@ant-design/icons';
import ZoomControls from '@/pages/GT/components/MapControls/ZoomControls';
import MapSplit from '@/pages/GT/components/MapControls/MapSplit'
import CoordinateQuery from '@/pages/GT/components/MapControls/CoordinateQuery';
import SplitLayerSelector from '../SplitLayerSelector';
import VirtualCursor from '../VirtualCursor';
import api from '@/pages/GT/utils/api';
import styles from './index.module.less';
import mapImg from "@/assets/img/地图.png"
import mapImgButton from "@/pages/SI/assets/image/低空监控/地图.png";
import { Get2 } from "@/services/general";
import { GetDiTuGps } from "@/pages/Maps/ditu";
import { wgs84Togcj02 } from "@/pages/Maps/gps_helper";
import {
  getMarket5,
} from "@/pages/Maps/dt_market";
import { useModel, useLocation, Cesium } from "umi";
import DevicePage from "@/pages/DJI/DevicePage";
import { isEmpty } from "@/utils/utils";
import mapModel from "@/models/mapModel";

// --- 辅助函数 ---

/**
 * 递归过滤函数：只保留匹配节点及其父节点路径
 * @param {Array} nodes - 当前节点数组
 * @param {string} searchValue - 搜索关键词
 * @returns {Array} 过滤后的节点数组
 */
const filterTreeData = (nodes, searchValue) => {
  if (!searchValue) {
    return nodes; // 如果没有搜索词，返回原始数据
  }
  if (!Array.isArray(nodes)) {
    return []; // 防御性编程，如果不是数组则返回空数组
  }

  return nodes.reduce((acc, node) => {
    // 假设 title 存储在 Name 属性中, Antd Tree 主要使用 title
    const titleToSearch = node.title || node.Name || ''; // 优先使用 title，然后是 Name
    const isMatch = titleToSearch.toLowerCase().includes(searchValue.toLowerCase());

    let filteredChildren = undefined;
    if (node.children && node.children.length > 0) {
      filteredChildren = filterTreeData(node.children, searchValue);
    }

    // 条件：
    // 1. 当前节点标题匹配
    // 2. 或者，当前节点的子节点中有匹配项（即 filteredChildren 不为空且长度大于0）
    if (isMatch || (filteredChildren && filteredChildren.length > 0)) {
      // 如果匹配，则保留该节点，并使用过滤后的子节点
      acc.push({
        ...node,
        // 如果原始节点有子节点，则使用过滤后的子节点，否则保持 undefined
        children: node.children ? filteredChildren : undefined,
      });
    }

    return acc;
  }, []);
};

/**
 * 递归获取树中所有节点的 key
 * @param {Array} nodes - 节点数组
 * @returns {Array<React.Key>} 所有节点的 key 组成的数组
 */
const getAllKeys = (nodes) => {
  let keys = [];
  if (!Array.isArray(nodes)) return keys; // 添加检查以防 nodes 不是数组
  for (const node of nodes) {
    keys.push(node.key);
    if (node.children) {
      keys = keys.concat(getAllKeys(node.children));
    }
  }
  return keys;
};

/**
 * 根据key查找节点的辅助函数
 * @param {Array} nodes - 节点数组
 * @param {React.Key} key - 要查找的节点 key
 * @returns {object | null} 找到的节点对象或 null
 */
const findNodeByKey = (nodes, key) => {
  if (!Array.isArray(nodes)) return null;
  for (let i = 0; i < nodes.length; i++) {
    const node = nodes[i];
    if (node.key === key) {
      return node;
    }
    if (node.children) {
      const found = findNodeByKey(node.children, key);
      if (found) return found;
    }
  }
  return null;
};

/**
 * 递归根据 Rid 查找节点的辅助函数
 * @param {Array} nodes - 节点数组
 * @param {string | number} rid - 要查找的节点 Rid
 * @returns {object | null} 找到的节点对象或 null
 */
const findNodeByRid = (nodes, rid) => {
  if (!Array.isArray(nodes)) return null // 防御性编程

  for (const node of nodes) {
    if (node.Rid === rid) {
      return node // 在当前层级找到
    }
    if (node.Children && node.Children.length > 0) {
      const foundInChildren = findNodeByRid(node.Children, rid)
      if (foundInChildren) {
        return foundInChildren // 在子树中找到
      }
    }
  }
  return null // 在此分支未找到
}

// 辅助函数：添加单个图层到地图并返回图层信息或 null
const addLayerToMapAndTrack = async (layerInfo, mapInstance, mapType) => {
  if (!mapInstance) {
    console.warn('Map instance is not available for adding layer:', layerInfo.id)
    return null
  }

  const serviceConfig = {
    id: layerInfo.id,
    type: layerInfo.layerType,
    url: layerInfo.url,
    zIndex: layerInfo.zIndex,
    accessInfo: layerInfo.accessInfo,
    expandParam: layerInfo.expandParam,
    visible: layerInfo.visible // 保持之前的可见性状态
  }

  try {
    const addedLayer = await mapInstance.addLayer(serviceConfig)
    if (addedLayer) {
      console.log(`Layer ${layerInfo.layerName} (ID: ${layerInfo.id}) restored successfully.`)
      // 确保恢复的图层信息结构与 layers 状态一致
      if (mapInstance.setLayerVisibility && !layerInfo.visible) {
        mapInstance.setLayerVisibility(layerInfo.id, false)
      }
      return { ...layerInfo } // 返回原始信息，表示成功
    } else {
      console.warn(`图层 ${layerInfo.layerType} (ID: ${layerInfo.id}) 可能在 ${mapType} 不支持或加载失败`)
      return null
    }
  } catch (error) {
    console.error(`恢复图层 ${layerInfo.layerName} 失败 (ID: ${layerInfo.id}):`, error)
    message.error(`恢复图层 ${layerInfo.layerName} 失败: ${error.message || '未知错误'}`)
    return null
  }
}

const DEFAULT_MAP_CONFIG = {
  'InitLevel': 10,
  'InitCenter': [30.659835174171207, 104.0633797645569],
  'InitHeight': 1000000,
  'MinZoom': 2,
  'MaxZoom': 18,
};

const OneMap = forwardRef(({
  deviceList,
  deviceLoading,
  showTreePanel = true,
  showZoomControls = true,
  showMapInfoPanel = true,
  showMapScale = true,
  showMeasureBtn = true,
  showMapCompass = true,
  showSplitButton = false,
  showCoordinateQuery = false, // 控制坐标查询组件显示，默认隐藏
  isLeftSidebarCollapsed = true,
  isRightSidebarCollapsed = true,
  mapType: propMapType = '3D', // 接收外部传入的mapType，默认为3D
  isCurtainMode = false, // 接收外部传入的卷帘模式状态
  theme = 'dark', // 添加主题参数，默认为dark
  mapId, // 地图唯一ID
  syncView = false, // 是否同步视图
  syncMode = 'on_end', // 同步模式：'off' | 'on_end' | 'realtime'
}, ref) => {
  const mapRef = useRef(null);
  const mapInstanceRef = useRef(null); // 存储实际的地图实例（Leaflet 或 Cesium）

  const { setPage } = useModel("pageModel");
  const { fetchBaseMapData } = mapModel(); // 使用 mapModel 获取底图数据

  // 使用状态来控制当前显示的地图类型，优先使用传入的mapType
  const [mapType, setMapType] = useState(propMapType);
  const [pending3DService, setPending3DService] = useState(null); // 添加三维服务状态

  // 监听外部传入的mapType变化
  useEffect(() => {
    if (propMapType !== mapType) {
      setMapType(propMapType);
    }
  }, [propMapType]);

  // 监听外部传入的卷帘模式变化
  useEffect(() => {
    if (isCurtainMode !== isSplitMode) {
      setIsSplitMode(isCurtainMode);
    }
  }, [isCurtainMode, isSplitMode]);
  const [basemapShow, setBasemapShow] = useState(false);
  const [basemapLayersInfo, setBasemapLayersInfo] = useState([]); // 底图数据
  const [mapServices, setMapServices] = useState([]); // 原始树数据
  const [mapBaseConfigLoaded, setMapBaseConfigLoaded] = useState(false); // 记录基础配置是否已加载完成
  const [mapBaseConfig, setMapBaseConfig] = useState(null); // 地图基础配置
  const [layers, setLayers] = useState([]); // 存储图层信息
  const [basemapLayers, setbasemapLayers] = useState([]);  // 存储底图图层信息
  const [layersToRestore, setLayersToRestore] = useState([]); // 存储切换时需要恢复的图层
  const [initialExpandedKeys, setInitialExpandedKeys] = useState([]); // 存储初始展开状态

  // 搜索功能
  const [searchValue, setSearchValue] = useState('');
  // expandedKeys 现在存储用户手动展开的keys（非搜索状态下）
  const [userExpandedKeys, setUserExpandedKeys] = useState([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true); // 初始加载时可以为 true
  const [selectedKeys, setSelectedKeys] = useState([]); // 当前选中的节点 key

  // 视图类型切换，默认为目录视图
  const [viewType, setViewType] = useState('directory'); // 'directory' 或 'layer'
  const [isExpanded, setIsExpanded] = useState(false); // 控制目录树的展开/收起状态
  const [isShowTreePanel, setIsShowTreePanel] = useState(showTreePanel); // 控制最终是否显示目录树

  const [isLoadingConfig, setIsLoadingConfig] = useState(true); // 加载状态

  // 卷帘功能状态
  const [isSplitMode, setIsSplitMode] = useState(false); // 是否启用卷帘模式
  const [splitPosition, setSplitPosition] = useState(0.5); // 分割位置
  const [leftSplitLayers, setLeftSplitLayers] = useState([]); // 左侧图层ID列表
  const [rightSplitLayers, setRightSplitLayers] = useState([]); // 右侧图层ID列表

  // 卷帘图层选择器状态
  const [showLeftSelector, setShowLeftSelector] = useState(false); // 是否显示左侧图层选择器
  const [showRightSelector, setShowRightSelector] = useState(false); // 是否显示右侧图层选择器

  // 坐标查询相关状态
  const [isPickingCoordinate, setIsPickingCoordinate] = useState(false); // 是否处于拾取模式
  const [pickedCoordinate, setPickedCoordinate] = useState(null); // 拾取到的坐标

  const location = useLocation();
  const pathArray = location.pathname.split('/');
  pathArray.shift(); // 移除第一个空元素

  // 初始化数据
  useEffect(() => {
    const fetchData = async () => {
      setIsLoadingConfig(true); // 开始获取数据，设置加载状态
      let finalConfig = null;
      try {
        // 遍历 pathArray，构建otherLayersParamsArray参数
        let otherLayersParamsArray = pathArray.map((path) => ({
          pcatalog: path,
        }));
        // 针对国土系统页面，otherLayersParamsArray的pcatalog值设置为空，后续可变更
        if (pathArray.includes('gt') && pathArray[0] === 'gt'){
          otherLayersParamsArray = [{
            pcatalog: '',
          }]
        }

        // 使用 mapModel 获取底图数据，避免重复请求
        const rawBasemapData = await fetchBaseMapData();
        console.log('从 mapModel 获取到的原始底图数据:', rawBasemapData);
        const { data: baseConfigResult } = await api.queryMapBaseConfig();
        console.log('获取到的地图基础配置:', baseConfigResult);

        let parsedConfig = { ...DEFAULT_MAP_CONFIG }; // Start with defaults
        if (baseConfigResult && Array.isArray(baseConfigResult) && baseConfigResult.length > 0) {
          baseConfigResult.forEach(item => {
            let value = item.configValue;
            if (['InitLevel', 'InitHeight', 'MinZoom', 'MaxZoom'].includes(item.configName)) {
              value = Number(value);
            } else {
              value = JSON.parse(value);
            }
            parsedConfig[item.configName] = value;
          });
        }
        finalConfig = parsedConfig;

        // 转换数据格式以符合Tree组件要求
        const transformData = (nodes) => {
          if (!Array.isArray(nodes)) return [];
          return nodes.map(node => {
            const newNode = {
              title: node.Name,
              key: node.Rid,
              ...node // 保留原始数据，包括 Name
            };
            if (node.Children && node.Children.length > 0) {
              newNode.children = transformData(node.Children.map(child => ({
                ...child,
                parentSortIndex: node.Attribute.sortValue,
              })));
            }
            return newNode;
          });
        };

        // 只有当showTreePanel为true时才获取otherLayers数据
        let transformedData = [];
        if (showTreePanel) {
          // 遍历otherLayersParamsArray，获取每个参数对应的otherLayers数据，然后合并到transformedData中
          for (const params of otherLayersParamsArray) {
            const data = await api.queryDataDirectory(params);
            transformedData = transformedData.concat(transformData(data));
          }
          console.log('转换后的树数据:', transformedData);
          // 如果没有子目录或只有一个子目录且子目录里没有服务或只有一个子服务（设置为初始自动加载），则不显示目录树
          if (transformedData.length === 0 || (transformedData.length === 1 && transformedData[0].Children.length <= 1)) {
            let showPanel = false
            if (transformedData[0]?.Children[0]?.Attribute.bootLoad === 0) {
              showPanel = true
            }
            setIsShowTreePanel(showPanel); // 设置最终是否显示目录树
          }
          setMapServices(transformedData);
        } else {
          console.log('showTreePanel为false,跳过otherLayers数据获取');
        }

        // 转换并设置底图数据
        if (rawBasemapData && rawBasemapData.length > 0) {
          const transformedBasemapData = transformData(rawBasemapData);
          console.log('转换后的底图数据:', transformedBasemapData);
          setBasemapLayersInfo(transformedBasemapData); // 更新底图状态
        }

        // 默认展开第一级目录
        if (transformedData.length > 0) {
          const firstLevelKeys = transformedData.map(item => item.key);
          setInitialExpandedKeys(firstLevelKeys); // 存储初始展开
          setUserExpandedKeys(firstLevelKeys); // 设置当前展开
          setAutoExpandParent(true); // 初始加载时自动展开父级
        } else {
          setInitialExpandedKeys([]);
          setUserExpandedKeys([]);
        }
      } catch (error) {
        message.error('获取数据或配置失败: ' + (error.message || '未知错误'));
        console.error("获取数据出错:", error);
        finalConfig = { ...DEFAULT_MAP_CONFIG }; // 获取失败，回退到默认配置
        console.log('获取配置失败，使用默认配置:', finalConfig);
      } finally {
        // 无论成功或失败，都设置 mapBaseConfig 并结束加载状态
        setMapBaseConfig(finalConfig);
        setIsLoadingConfig(false);
        setMapBaseConfigLoaded(true); // 标记基础配置已加载完成
      }
    };
    fetchData();
  }, []); // 空依赖数组，表示只在组件挂载时执行一次

  // 加载默认底图及设备标记
  useEffect(() => {

    // 确保 map 实例、底图数据和切换函数都已准备好
    if (!mapRef.current || !basemapLayersInfo.length || !toggleBasemapLayerVisibility) return;

    // --- 加载默认底图 ---
    // 优先查找返回 bootLoad === 1 的节点，如果没有则返回第一个节点
    const defaultBasemapGroupNode = basemapLayersInfo.find(node => node.Attribute?.bootLoad === 1) || basemapLayersInfo[0];

    if (defaultBasemapGroupNode) {
      toggleBasemapLayerVisibility(defaultBasemapGroupNode, true);
      console.log('默认底图加载完成:', defaultBasemapGroupNode.Name);
    } else {
      console.warn('没有找到可加载的默认底图组。');
    }

    // --- 加载设备标记 ---
    updateDeviceMarkers();
    console.log('设备标记加载完成');

  }, [basemapLayersInfo, toggleBasemapLayerVisibility]);

  // 加载初始业务图层，只有showTreePanel为true时才会有业务图层
  useEffect(() => {
    // 确保map实例已准备好且有业务图层数据
    if (!mapRef.current || !mapServices.length) return;

    // --- 加载初始业务图层 (bootLoad === 1) ---
    const collectLayerConfigs = (nodes, configs = []) => {
      nodes.forEach(node => {
        // 只收集非底图 ('DDT') 且标记为 bootLoad 的图层
        if (node.Attribute?.servicePath && node.Attribute.bootLoad === 1) {
          const zIndex = Number(node.parentSortIndex) * 100 + Number(node.Attribute.sortValue);
          configs.push({
            id: node.Rid,
            type: node.Attribute.serviceTypeName,
            url: node.Attribute.servicePath,
            zIndex,
            accessInfo: node.Attribute.accessInfo,
            expandParam: node.Attribute.expandParam,
            layerName: node.Name,
            layerType: node.Attribute.serviceTypeName,
            visible: true, // 初始可见
            checked: true,  // 初始在目录树中勾选
            pcatalog: node.Attribute.pcatalog,
          });
        }
        if (node.children) {
          collectLayerConfigs(node.children, configs); // 递归收集
        }
      });
      return configs;
    };

    const layerConfigs = collectLayerConfigs(mapServices); // 只收集业务图层

    // 批量加载业务图层
    const loadedLayers = layerConfigs.map(config => {
      const layerObj = mapRef.current.addLayer(config);
      // 如果加载成功，返回图层信息用于状态更新
      return layerObj ? {
        id: config.id,
        layerName: config.layerName,
        layerType: config.layerType,
        url: config.url,
        zIndex: config.zIndex,
        visible: true,
        checked: true,
        accessInfo: config.accessInfo,
        pcatalog: config.pcatalog,
      } : null;
    }).filter(Boolean); // 过滤掉加载失败的 null

    // 按 zIndex 排序并更新 layers 状态
    loadedLayers.sort((a, b) => b.zIndex - a.zIndex); // zIndex 大的在前面 (上面)
    setLayers(prevLayers => [...loadedLayers, ...prevLayers]);
    console.log('业务图层加载完成, 共加载:', loadedLayers.length);

  }, [mapServices]);

  const [markerList, setMarkerList] = useState([]);
  const [cesiumEntities, setCesiumEntities] = useState([]);

  const updateDeviceMarkers = useCallback(async () => {
    try {
      // 确保地图已初始化
      if (!mapRef.current) return;

      const mapInstance = typeof mapRef.current.getMap === 'function' ? mapRef.current.getMap() : mapRef.current.getViewer();
      const isCesiumViewer = mapInstance instanceof Cesium.Viewer;

      // 清除现有标记
      if (isCesiumViewer) {
        cesiumEntities.forEach((entity) => {
          if (entity) mapInstance.entities.remove(entity);
        });
        setCesiumEntities([]);
      } else {
        markerList.forEach((marker) => {
          if (marker) mapRef.current.removeMarker(marker);
        });
        setMarkerList([]);
      }

      // 如果没有设备数据，直接返回
      if (!deviceList || deviceList.length === 0) return;

      // 添加新标记
      if (isCesiumViewer) {
        const newEntities = [];
        deviceList.forEach((device) => {
          const entities = createCesiumEntities(mapInstance, device);
          if (entities) {
            newEntities.push(...entities);
          }
        });
        setCesiumEntities(newEntities);
      } else {
        const markers = createLeafletMarkers(deviceList);
        const markersGroup = markers.map(marker => mapRef.current.addMarker(marker));
        setMarkerList(markersGroup);
      }
    } catch (error) {
      console.error('更新设备标记失败:', error);
    }
  }, [deviceList]);


  // 在mapType变化时更新设备标记
  useEffect(() => {
      if (mapRef.current && deviceList) {
        updateDeviceMarkers();
      }
    }, [deviceList, mapType, updateDeviceMarkers]);

  React.useImperativeHandle(ref, () => ({
    setCenter: (options) => {
      if (mapRef.current && typeof mapRef.current.setCenter === 'function') {
        mapRef.current.setCenter(options)
      } else {
        console.warn(`setCenter不可用`)
      }
    },
    // 暴露卷帘功能切换方法给父组件
    toggleSplitMode: () => {
      handleToggleSplitMode()
    },
    // 暴露卷帘状态给父组件
    getSplitMode: () => {
      return isSplitMode
    },
    // 暴露图层管理方法给父组件
    addLayer: (serviceConfig) => {
      if (mapRef.current && typeof mapRef.current.addLayer === 'function') {
        const layerObj = mapRef.current.addLayer(serviceConfig)

        // 如果是多时相图层，需要更新组件状态以便后续管理
        if (layerObj && serviceConfig.pcatalog === 'multidate') {
          const newLayerInfo = {
            id: serviceConfig.id,
            layerName: serviceConfig.layerName || serviceConfig.id,
            layerType: serviceConfig.type,
            url: serviceConfig.url,
            zIndex: serviceConfig.zIndex || 1000,
            visible: true,
            checked: true,
            accessInfo: serviceConfig.accessInfo,
            expandParam: serviceConfig.expandParam,
            pcatalog: 'multidate'
          }

          // 添加到layers状态中
          setLayers(prevLayers => [newLayerInfo, ...prevLayers])
          console.log('多时相图层已添加到状态管理:', newLayerInfo)
        }

        return layerObj
      } else {
        console.warn(`addLayer不可用`)
        return null
      }
    },
    removeAllLayers: async () => {
      if (mapRef.current && typeof mapRef.current.removeAllLayers === 'function') {
        try {
          await mapRef.current.removeAllLayers()
          // 清空组件内部的图层状态
          setLayers([])
          setbasemapLayers([])
          console.log('所有图层已移除，状态已清空')
          return true
        } catch (error) {
          console.error('移除所有图层失败:', error)
          return false
        }
      } else {
        console.warn(`removeAllLayers不可用`)
        return false
      }
    },
    setLayerVisibility: (layerId, visible) => {
      if (mapRef.current && typeof mapRef.current.setLayerVisibility === 'function') {
        mapRef.current.setLayerVisibility(layerId, visible)
      } else {
        console.warn(`setLayerVisibility不可用`)
      }
    },
    // 移除特定pcatalog的图层
    removeLayersByPcatalog: async (pcatalog) => {
      if (!mapRef.current || !pcatalog) return false

      try {
        // 找到需要移除的图层
        const layersToRemove = [...layers, ...basemapLayers].filter(layer => layer.pcatalog === pcatalog)
        
        // 从地图中移除这些图层
        for (const layer of layersToRemove) {
          if (mapRef.current.removeLayer) {
            const removed = mapRef.current.removeLayer(layer.id)
            if (removed) {
              console.log(`已移除图层: ${layer.layerName} (ID: ${layer.id})`)
            }
          }
        }

        // 更新状态，移除对应的图层
        setLayers(prevLayers => prevLayers.filter(layer => layer.pcatalog !== pcatalog))
        setbasemapLayers(prevLayers => prevLayers.filter(layer => layer.pcatalog !== pcatalog))

        console.log(`pcatalog为${pcatalog}的图层已全部移除`)
        return true
      } catch (error) {
        console.error(`移除pcatalog为${pcatalog}的图层失败:`, error)
        return false
      }
    }
  }))

  // 设备点击事件处理
  const handleDeviceClick = useCallback((device) => {
    localStorage.setItem("device", JSON.stringify(device));
    console.log('Device clicked:', device);
    setPage(<DevicePage device={device} />);
  }, [setPage]);

  // 为2D地图创建设备标记
  const createLeafletMarkers = useCallback((deviceList) => {
    return deviceList.map((device) => {
      const deviceData = { ...device };
      return getMarket5(deviceData, () => handleDeviceClick(device));
    });
  }, [handleDeviceClick]);

  // 为3D地图创建设备实体
  const createCesiumEntities = useCallback((viewer, device) => {
    if (isEmpty(viewer)) return null;

    const entities = [];

    // 创建标记实体
    const entity = viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(device.Lng, device.Lat, 573.5),
      billboard: {
        image: require("@/assets/icons/device.png"),
        scale: 0.1,
        maxiumScale: 0.5,
        verticalOrigin: Cesium.VerticalOrigin.BASELINE,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      },
      deviceData: device
    });
    entities.push(entity);

    // 创建连接线实体
    const line = viewer.entities.add({
      name: "Device connection line",
      polyline: {
        positions: [
          Cesium.Cartesian3.fromDegrees(device.Lng, device.Lat, device.Height),
          Cesium.Cartesian3.fromDegrees(device.Lng, device.Lat, 400),
        ],
        width: 2,
        material: new Cesium.PolylineDashMaterialProperty({
          color: Cesium.Color.YELLOW,
          dashLength: 20,
          gapColor: Cesium.Color.TRANSPARENT,
        }),
      },
    });
    entities.push(line);

    return entities;
  }, []);

  // 3D地图设备点击事件处理
  useEffect(() => {
    if (mapRef.current && mapType === '3D') {
      const viewer = mapRef.current.getViewer();
      if (!viewer) return;

      const handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

      handler.setInputAction((click) => {
        const pickedObject = viewer.scene.pick(click.position);
        if (Cesium.defined(pickedObject) && pickedObject.id && pickedObject.id.deviceData) {
          handleDeviceClick(pickedObject.id.deviceData);
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

      return () => {
        handler.destroy();
      };
    }
  }, [mapRef.current, mapType, handleDeviceClick]);

  // --- 图层控制 ---

  // 辅助函数：创建服务配置
  const createServiceConfig = (node, zIndex) => ({
    id: node.Rid,
    type: node.Attribute.serviceTypeName,
    url: node.Attribute.servicePath,
    zIndex: zIndex,
    accessInfo: node.Attribute.accessInfo,
    expandParam: node.Attribute.expandParam,
  });

  // 辅助函数：计算底图 ZIndex
  const calculateBasemapZIndex = (node) => {
    const sortValue = Number(node.Attribute?.sortValue) || 0;
    return -10000 + sortValue;
  };

  // 辅助函数：计算业务图层 ZIndex
  const calculateLayerZIndex = (node) => {
    const parentSortIndex = Number(node.parentSortIndex) || 0;
    const sortValue = Number(node.Attribute?.sortValue) || 0;
    return parentSortIndex * 100 + sortValue;
  };

  // 核心处理函数：切换图层可见性与加载
  const handleLayerToggle = useCallback((node, visible, isBasemap = false) => {
    if (!mapRef.current || !node.Attribute?.servicePath) return;

    const layerState = isBasemap ? basemapLayers : layers;
    const setLayerState = isBasemap ? setbasemapLayers : setLayers;
    const layerType = isBasemap ? '底图' : '业务';

    console.log(`Toggle ${layerType} layer:`, node.Name, 'Visible:', visible);
    const existingLayer = layerState.find(layer => layer.id === node.Rid);

    if (existingLayer) {
      // 图层已加载，更新可见性
      mapRef.current.setLayerVisibility(node.Rid, visible);
      setLayerState(prevLayers => prevLayers.map(layer =>
        layer.id === node.Rid
          ? { ...layer, visible: visible, checked: visible } // 同步 checked 状态
          : layer
      ));
      console.log(`${layerType}图层 ${node.Name} 可见性设置为: ${visible}`);
    } else if (visible) {
      // 图层未加载且需要显示，则加载
      const zIndex = isBasemap ? calculateBasemapZIndex(node) : calculateLayerZIndex(node);
      const serviceConfig = createServiceConfig(node, zIndex);

      console.log(`加载 ${layerType} 图层:`, serviceConfig);
      const layerObj = mapRef.current.addLayer(serviceConfig);

      if (layerObj) {
        console.log(`${layerType}图层 ${node.Name} 加载成功`);
        const newLayerInfo = {
          id: node.Rid,
          layerName: node.Name,
          layerType: node.Attribute.serviceTypeName,
          url: node.Attribute.servicePath,
          zIndex,
          visible: true,
          checked: true, // 加载后默认为选中
          accessInfo: node.Attribute.accessInfo,
          parentId: node.Attribute.parentId,
          pcatalog: node.Attribute.pcatalog,
        };

        setLayerState(prevLayers => {
          const updatedLayers = [newLayerInfo, ...prevLayers];
          if (!isBasemap) {
            updatedLayers.sort((a, b) => b.zIndex - a.zIndex);
          }
          return updatedLayers;
        });
      } else {
        console.warn(`${layerType}图层 ${node.Name} (类型: ${serviceConfig.type}) 可能加载失败或不受当前地图支持`);
        // 如果二维场景下添加三维图层，则不弹出警告
        if (['Cesium3DTileService', 'CesiumTerrainService'].includes(serviceConfig.type) && mapType === '2D') {
          return;
        }

        message.warning(`图层 ${node.Name} 加载失败`);
      }
    } else {
      // 如果图层未加载且设置为隐藏，则无需操作
      console.log(`${layerType}图层 ${node.Name} 未加载，无需隐藏操作`);
    }
  }, [layers, basemapLayers]); // 依赖项包含图层状态

  // 控制业务图层显示/隐藏 (调用核心函数)
  const toggleLayerVisibility = useCallback((node, visible) => {
    // 确保只处理非底图 ('DDT')
    if (node.Attribute?.pcatalog !== 'DDT') {
      handleLayerToggle(node, visible, false);
    } else {
      console.warn("尝试使用 toggleLayerVisibility 控制底图，请使用 toggleBasemapLayerVisibility");
    }
  }, [handleLayerToggle]);

  // 控制底图图层显示/隐藏 (调用核心函数)
  const toggleBasemapLayerVisibility = useCallback((node, visible) => {
    // 确保只处理底图 ('DDT')
    if (node.Attribute?.pcatalog === 'DDT') {
      // 遍历node.children，确保所有子节点都执行handleLayerToggle
      if (node.children) {
        node.children.forEach(child => {
          handleLayerToggle(child, visible, true);
        });
      }
    } else {
      console.warn("尝试使用 toggleBasemapLayerVisibility 控制业务图层，请使用 toggleLayerVisibility");
    }
  }, [handleLayerToggle]);

  // 处理Tree组件的check切换：选中时加载图层，取消选中时移除图层
  const handleLayerCheckToggle = useCallback((node, isChecked) => {
    if (!mapRef.current || !node.Attribute?.servicePath) return;

    const layerState = node.Attribute?.pcatalog === 'DDT' ? basemapLayers : layers;
    const setLayerState = node.Attribute?.pcatalog === 'DDT' ? setbasemapLayers : setLayers;
    const isBasemap = node.Attribute?.pcatalog === 'DDT';
    const layerType = isBasemap ? '底图' : '业务';

    console.log(`Check toggle ${layerType} layer:`, node.Name, 'Checked:', isChecked);
    const existingLayer = layerState.find(layer => layer.id === node.Rid);

    if (isChecked) {
      // 选中：加载图层（如果未加载）或显示图层（如果已加载但隐藏）
      if (existingLayer) {
        // 图层已存在，确保其可见
        mapRef.current.setLayerVisibility(node.Rid, true);
        setLayerState(prevLayers => prevLayers.map(layer =>
          layer.id === node.Rid
            ? { ...layer, visible: true, checked: true }
            : layer
        ));
        console.log(`${layerType}图层 ${node.Name} 已显示`);
      } else {
        // 图层未加载，执行加载
        const zIndex = isBasemap ? calculateBasemapZIndex(node) : calculateLayerZIndex(node);
        const serviceConfig = createServiceConfig(node, zIndex);

        console.log(`加载 ${layerType} 图层:`, serviceConfig);
        const layerObj = mapRef.current.addLayer(serviceConfig);

        if (layerObj) {
          console.log(`${layerType}图层 ${node.Name} 加载成功`);
          const newLayerInfo = {
            id: node.Rid,
            layerName: node.Name,
            layerType: node.Attribute.serviceTypeName,
            url: node.Attribute.servicePath,
            zIndex,
            visible: true,
            checked: true,
            accessInfo: node.Attribute.accessInfo,
            parentId: node.Attribute.parentId,
            pcatalog: node.Attribute.pcatalog,
          };

          setLayerState(prevLayers => {
            const updatedLayers = [newLayerInfo, ...prevLayers];
            if (!isBasemap) {
              updatedLayers.sort((a, b) => b.zIndex - a.zIndex);
            }
            return updatedLayers;
          });
        } else {
          console.warn(`${layerType}图层 ${node.Name} 加载失败`);
          message.warning(`图层 ${node.Name} 加载失败`);
        }
      }
    } else {
      // 取消选中：移除图层
      if (existingLayer) {
        console.log(`移除 ${layerType} 图层:`, node.Name);
        const removed = mapRef.current.removeLayer(node.Rid);
        if (removed) {
          setLayerState(prevLayers => prevLayers.filter(layer => layer.id !== node.Rid));
          console.log(`${layerType}图层 ${node.Name} 已移除`);
        } else {
          console.warn(`${layerType}图层 ${node.Name} 移除失败`);
        }
      } else {
        console.log(`${layerType}图层 ${node.Name} 未加载，无需移除`);
      }
    }
  }, [layers, basemapLayers, calculateBasemapZIndex, calculateLayerZIndex, createServiceConfig]);

  // --- 搜索相关逻辑 ---

  // 1. 使用 useMemo 缓存过滤后的树数据
  const filteredData = useMemo(() => {
    return filterTreeData(mapServices, searchValue);
  }, [mapServices, searchValue]); // 依赖原始数据和搜索词

  // 2. 使用 useMemo 缓存搜索状态下需要展开的 keys
  const searchResultExpandedKeys = useMemo(() => {
    if (!searchValue) {
      return []; // 非搜索状态下返回空，将由 userExpandedKeys 控制
    }
    // 获取过滤后数据中的所有 key
    return getAllKeys(filteredData);
  }, [searchValue, filteredData]);

  // 处理搜索输入变化
  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchValue(value);

    // 根据是否有搜索值，决定是否需要自动展开父节点
    setAutoExpandParent(!!value);
  };

  // 处理展开/收起
  const onExpand = (newExpandedKeys) => {
    // 只有在非搜索状态下，才更新用户手动展开的状态
    if (!searchValue) {
      setUserExpandedKeys(newExpandedKeys);
      setAutoExpandParent(false); // 用户手动操作后，关闭自动展开父节点
    }
    // 在搜索状态下，展开状态由 searchResultExpandedKeys 控制，用户操作无效
  };

  // --- 决定最终传递给 Tree 的 props ---
  // 如果有搜索值，使用过滤后的数据，否则使用原始数据
  const displayData = searchValue ? filteredData : mapServices;
  // 如果有搜索值，使用计算出的展开 Keys，否则使用用户手动控制的 Keys
  const currentExpandedKeys = searchValue ? searchResultExpandedKeys : userExpandedKeys;

  // 切换地图类型的函数
  const toggleMapType = async () => {
    if (!mapRef.current) return;

    const currentLayersState = [...layers, ...basemapLayers]; // 保存当前图层状态和顺序
    console.log('Saving layers before switch:', currentLayersState);

    // 移除当前地图的所有图层
    try {
      await mapRef.current.removeAllLayers();
      console.log('All layers removed from', mapType, 'map.');
      setLayers([]); // 清空当前图层状态
      setbasemapLayers([]); // 清空底图图层状态
      setLayersToRestore(currentLayersState); // 存储待恢复的图层
      // 切换地图类型
      setMapType(prevMapType => (prevMapType === '2D' ? '3D' : '2D'));
    } catch (error) {
      message.error(`移除图层时出错: ${error.message}`);
      console.error('Error removing layers:', error);
    }
  };

  // 切换到3D地图并加载三维服务
  const handleSwitchTo3D = async (serviceConfig) => {
    console.log('Switching to 3D with service:', serviceConfig);
    if (!mapRef.current) return;

    const currentLayersState = [...layers, ...basemapLayers];
    console.log('Saving layers before switch to 3D:', currentLayersState);

    try {
      await mapRef.current.removeAllLayers();
      setLayers([]);
      setbasemapLayers([]);
      setLayersToRestore(currentLayersState.filter(l => l.layerType !== 'Cesium3DTileService' && l.layerType !== 'CesiumTerrainService'));

      const node = findNodeByRid(mapServices, serviceConfig.id);
      console.log('Node found for 3D service:', node);

      // 更新选中状态以反映三维服务加载
      setSelectedKeys([node.key]);

      // 设置待加载的3D服务并切换地图类型
      setPending3DService(node);
      setMapType('3D');
    } catch (error) {
      message.error(`切换到 3D 时移除图层出错: ${error.message}`);
      console.error('Error removing layers during switch to 3D:', error);
    }
  };

  // Effect Hook: 当 mapType 改变且 mapRef 准备好后，重新加载图层或三维服务
  useEffect(() => {
    const restoreLayers = async () => {
      if (mapRef.current) {
        if (layersToRestore.length > 0) {
          console.log('Starting layer restoration for', mapType, 'map. Layers to restore:', layersToRestore)

          const basemapSourceLayers = []
          const otherSourceLayers = []

          // 1. 首先根据 pcatalog 分离底图和其他图层
          for (const layer of layersToRestore) {
            if (layer.pcatalog === 'DDT') {
              basemapSourceLayers.push(layer)
            } else {
              otherSourceLayers.push(layer)
            }
          }
          console.log('Basemap layers to restore:', basemapSourceLayers)
          console.log('Other layers to restore:', otherSourceLayers)

          // 2. 处理并添加底图图层
          if (basemapSourceLayers.length > 0) {
            const restoredBasemapLayersPromises =
              // 按倒序（从底层到顶层）创建添加图层的 Promise
              [...basemapSourceLayers].reverse().map(layerInfo =>
                addLayerToMapAndTrack(layerInfo, mapRef.current, mapType)
              )

            // 等待所有底图图层加载完成
            const settledBasemapResults = await Promise.all(restoredBasemapLayersPromises)
            const successfullyRestoredBasemaps = settledBasemapResults.filter(Boolean)
            console.log('Basemap layers restoration complete. State updated with:', successfullyRestoredBasemaps)
            setbasemapLayers(successfullyRestoredBasemaps)
          } else {
            console.log('No basemap layers to restore.')
          }

          // 3. 处理并添加其他业务图层
          if (otherSourceLayers.length > 0) {
            const restoredOtherLayersPromises =
              // 按倒序（从底层到顶层）创建添加图层的 Promise
              [...otherSourceLayers].reverse().map(layerInfo =>
                addLayerToMapAndTrack(layerInfo, mapRef.current, mapType)
              )

            // 等待所有其他图层加载完成
            const settledOtherResults = await Promise.all(restoredOtherLayersPromises)
            const successfullyRestoredOtherLayers = settledOtherResults.filter(Boolean)
            setLayers(successfullyRestoredOtherLayers.reverse())
            console.log('Other layers restoration complete. State updated with:', successfullyRestoredOtherLayers.reverse())
          } else {
            console.log('No other layers to restore.')
          }

          // 4. 清空待恢复列表
          setLayersToRestore([])
          console.log('All layer restoration attempts finished. LayersToRestore cleared.')
        } else if (mapType === '3D' && pending3DService) {
          console.log('Restoring 3D service:', pending3DService);
          toggleLayerVisibility(pending3DService, true);
          setPending3DService(null); // 重置待加载的3D服务
        }
      }
    };

    restoreLayers();
  }, [mapType, mapRef.current, layersToRestore]); // 依赖项包括 mapType 和 layersToRestore

  // 使用 useCallback 包装上移操作，避免不必要的函数创建
  const handleMoveLayerUp = useCallback((layerId, index) => {
    if (index > 0 && mapRef.current) {
      // 1. 调用子组件方法处理地图内部逻辑
      mapRef.current.moveLayerUp(layerId);

      // 2. 更新父组件状态以反映 UI 顺序
      setLayers(prevLayers => {
        const newLayers = [...prevLayers];
        // 交换父组件状态数组中的元素的zIndex
        const layerToMove = newLayers[index];
        const layerAbove = newLayers[index - 1];
        const tempZIndex = layerToMove.zIndex;
        layerToMove.zIndex = layerAbove.zIndex;
        layerAbove.zIndex = tempZIndex;
        // 交换父组件状态数组中的元素位置
        [newLayers[index], newLayers[index - 1]] = [newLayers[index - 1], newLayers[index]];
        return newLayers;
      });
    }
  }, []); // 空依赖数组，因为 setLayers 是稳定的

  // 使用 useCallback 包装下移操作
  const handleMoveLayerDown = useCallback((layerId, index) => {
    if (index < layers.length - 1 && mapRef.current) {
      // 1. 调用子组件方法处理地图内部逻辑
      mapRef.current.moveLayerDown(layerId);

      // 2. 更新父组件状态以反映 UI 顺序
      setLayers(prevLayers => {
        const newLayers = [...prevLayers];
        // 交换父组件状态数组中的元素的zIndex
        const layerToMove = newLayers[index];
        const layerBelow = newLayers[index + 1];
        const tempZIndex = layerToMove.zIndex;
        layerToMove.zIndex = layerBelow.zIndex;
        layerBelow.zIndex = tempZIndex;
        // 交换父组件状态数组中的元素位置
        [newLayers[index], newLayers[index + 1]] = [newLayers[index + 1], newLayers[index]];
        return newLayers;
      });
    }
  }, [layers.length]); // 依赖 layers.length 确保 index 比较有效

  // 卷帘功能处理函数
  const handleToggleSplitMode = useCallback(() => {
    setIsSplitMode(prev => {
      const newSplitMode = !prev;

      if (newSplitMode) {
        // 启用卷帘模式时，将当前可见的图层自动添加到左侧
        const currentVisibleLayers = layers.filter(layer => layer.visible).map(layer => layer.id);
        console.log('启用卷帘模式，将现有可见图层添加到左侧:', currentVisibleLayers);

        setLeftSplitLayers(currentVisibleLayers);
        setRightSplitLayers([]);

        // 为现有图层设置左侧分割方向
        if (mapRef.current && mapRef.current.setLayerSplitDirection && currentVisibleLayers.length > 0) {
          currentVisibleLayers.forEach(layerId => {
            mapRef.current.setLayerSplitDirection(layerId, Cesium.SplitDirection.LEFT);
          });
        }

        // 显示图层选择器
        setShowLeftSelector(true);
        setShowRightSelector(true);
      } else {
        // 退出卷帘模式时，隐藏图层选择器并重置状态
        setShowLeftSelector(false);
        setShowRightSelector(false);
        setLeftSplitLayers([]);
        setRightSplitLayers([]);

        if (mapRef.current && mapRef.current.resetAllLayersSplitDirection) {
          mapRef.current.resetAllLayersSplitDirection();
        }
      }

      return newSplitMode;
    });
  }, [layers]);

  const handleSplitPositionChange = useCallback((position) => {
    setSplitPosition(position);
  }, []);

  const handleAddLayerToSplit = useCallback((layerId, side) => {
    if (side === 'left') {
      setLeftSplitLayers(prev => [...prev.filter(id => id !== layerId), layerId]);
      setRightSplitLayers(prev => prev.filter(id => id !== layerId));
    } else {
      setRightSplitLayers(prev => [...prev.filter(id => id !== layerId), layerId]);
      setLeftSplitLayers(prev => prev.filter(id => id !== layerId));
    }
  }, []);

  const handleRemoveLayerFromSplit = useCallback((layerId, side) => {
    if (side === 'left') {
      setLeftSplitLayers(prev => prev.filter(id => id !== layerId));
    } else {
      setRightSplitLayers(prev => prev.filter(id => id !== layerId));
    }
  }, []);

  // 卷帘图层选择器处理函数
  const handleSplitLayerSelect = useCallback((node, isSelected, side) => {
    if (!node.Attribute?.servicePath) return;

    const layerId = node.Rid;

    if (isSelected) {
      // 添加图层到指定侧
      handleAddLayerToSplit(layerId, side);

      // 确保图层已加载
      const existingLayer = layers.find(layer => layer.id === layerId);
      if (!existingLayer) {
        toggleLayerVisibility(node, true);
      }

      // 设置图层的splitDirection
      if (mapRef.current && mapRef.current.setLayerSplitDirection) {
        const splitDirection = side === 'left' ? Cesium.SplitDirection.LEFT : Cesium.SplitDirection.RIGHT;
        mapRef.current.setLayerSplitDirection(layerId, splitDirection);
      }
    } else {
      // 从卷帘中移除图层
      handleRemoveLayerFromSplit(layerId, side);

      // 重置图层的splitDirection
      if (mapRef.current && mapRef.current.setLayerSplitDirection) {
        mapRef.current.setLayerSplitDirection(layerId, Cesium.SplitDirection.NONE);
      }
    }
  }, [layers, toggleLayerVisibility]);

  const handleSplitLayerToggle = useCallback((node, visible, side) => {
    handleSplitLayerSelect(node, visible, side);
  }, [handleSplitLayerSelect]);

  // 底图选择处理函数
  const handleBasemapSelect = useCallback((selectedLayer) => {
    const currentlySelectedLayerInfo = basemapLayersInfo.find(
      info => basemapLayers.some(l => l.parentId === info.Rid && l.visible)
    );

    // 如果点击的是当前已选中的底图，则取消选中
    if (currentlySelectedLayerInfo && currentlySelectedLayerInfo.Rid === selectedLayer.Rid) {
      toggleBasemapLayerVisibility(selectedLayer, false);
    } else {
      // 如果有其他底图已选中，先取消选中
      if (currentlySelectedLayerInfo) {
        toggleBasemapLayerVisibility(currentlySelectedLayerInfo, false);
      }
      // 选中当前点击的底图
      toggleBasemapLayerVisibility(selectedLayer, true);
    }
  }, [basemapLayersInfo, basemapLayers, toggleBasemapLayerVisibility]);

  // 坐标查询处理函数
  const handleLocationToCoordinate = useCallback((coordinate) => {
    if (mapRef.current && mapRef.current.locationToCoordinate) {
      mapRef.current.locationToCoordinate(coordinate);
    }
  }, []);

  const handleTogglePickCoordinate = useCallback(() => {
    if (mapRef.current && mapRef.current.togglePickCoordinate) {
      mapRef.current.togglePickCoordinate();
      // 更新拾取状态
      const newPickingState = mapRef.current.getIsPickingCoordinate ? mapRef.current.getIsPickingCoordinate() : false;
      setIsPickingCoordinate(newPickingState);
    }
  }, []);

  const handleClearCoordinateMarkers = useCallback(() => {
    if (mapRef.current && mapRef.current.clearCoordinateMarkers) {
      mapRef.current.clearCoordinateMarkers();
      setPickedCoordinate(null);
    }
  }, []);

  // 监听地图实例的坐标拾取状态变化
  useEffect(() => {
    if (mapRef.current) {
      const checkPickedCoordinate = () => {
        if (mapRef.current.getPickedCoordinate) {
          const coordinate = mapRef.current.getPickedCoordinate();
          if (coordinate && coordinate !== pickedCoordinate) {
            setPickedCoordinate(coordinate);
          }
        }
        if (mapRef.current.getIsPickingCoordinate) {
          const pickingState = mapRef.current.getIsPickingCoordinate();
          if (pickingState !== isPickingCoordinate) {
            setIsPickingCoordinate(pickingState);
          }
        }
      };

      // 定期检查状态变化
      const interval = setInterval(checkPickedCoordinate, 500);
      return () => clearInterval(interval);
    }
  }, [pickedCoordinate, isPickingCoordinate]);

  // 使用地图视图同步钩子
  const { handleViewChange } = useMapViewSync(
    mapInstanceRef.current,
    mapId || `map-${mapType}-${Date.now()}`, // 确保每个地图有唯一ID
    syncView,
    mapType
  );

  // 更新地图实例引用
  useEffect(() => {
    if (mapRef.current) {
      mapInstanceRef.current = mapType === '2D'
        ? mapRef.current.getMap()
        : mapRef.current.getViewer();
    }
  }, [mapRef.current, mapType]);

  // 渲染卷帘相关组件的辅助函数
  const renderSplitComponents = () => {
    if (!isSplitMode || !mapRef.current) return null;

    return (
      <>
        {/* 卷帘组件 */}
        <MapSplit
          viewer={mapType === '2D' ? mapRef.current.getMap() : mapRef.current.getViewer()}
          enabled={isSplitMode}
          splitPosition={splitPosition}
          leftLayers={leftSplitLayers}
          rightLayers={rightSplitLayers}
          onSplitPositionChange={handleSplitPositionChange}
        />

        {/* 卷帘图层选择器 */}
        <SplitLayerSelector
          side="left"
          treeData={mapServices}
          selectedLayers={leftSplitLayers}
          onLayerSelect={handleSplitLayerSelect}
          onLayerToggle={handleSplitLayerToggle}
          visible={showLeftSelector}
          onClose={() => setShowLeftSelector(false)}
          position={{ top: 80, left: 20 }}
          currentLayers={layers}
        />
        <SplitLayerSelector
          side="right"
          treeData={mapServices}
          selectedLayers={rightSplitLayers}
          onLayerSelect={handleSplitLayerSelect}
          onLayerToggle={handleSplitLayerToggle}
          visible={showRightSelector}
          onClose={() => setShowRightSelector(false)}
          position={{ top: 80, right: 20 }}
          currentLayers={layers}
        />
      </>
    );
  };

  const finalInitZoom = mapBaseConfig?.InitLevel ?? DEFAULT_MAP_CONFIG.InitLevel;
  const finalInitCenter = mapBaseConfig?.InitCenter ?? DEFAULT_MAP_CONFIG.InitCenter;
  const finalInitHeight = mapBaseConfig?.InitHeight ?? DEFAULT_MAP_CONFIG.InitHeight;

  const treePanelContainerClasses = [
    styles.treePanelContainer,
    isLeftSidebarCollapsed ? styles.leftSidebarCollapsed : styles.leftSidebarExpanded,
  ].join(' ')

  const MapControlPanelClasses = [
    styles.mapControlPanel,
    isRightSidebarCollapsed ? styles.rightSidebarCollapsed : styles.rightSidebarExpanded,
  ].join(' ')

  const basemapControlsWrapperClasses = [
    styles.basemapControlsWrapper,
    isRightSidebarCollapsed ? styles.rightSidebarCollapsed : styles.rightSidebarExpanded,
  ].join(' ')

  return (
    <div className={styles.mapContainer}>
      {/* 悬浮目录树 */}
      {isShowTreePanel && !isSplitMode && (
        <div className={treePanelContainerClasses}>
          {/* 展开/收起按钮 */}
          <div
            className={`${styles.treeToggleButton} ${isExpanded ? styles.expanded : ''}`}
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <ProfileOutlined className={styles.toggleIcon} />
            <span>图层目录</span>
          </div>

          {/* 悬浮目录树主体 */}
          <div className={`${styles.treePanelYzt} ${isExpanded ? styles.expanded : ''}`}>
            {/* 固定头部 */}
            <div className={styles.fixedHeader}>
              {/* 视图切换按钮 */}
              <div className={styles.viewToggle}>
                <div
                  className={`${styles.viewToggleButton} ${viewType === 'directory' ? styles.active : ''}`}
                  onClick={() => setViewType('directory')}
                >
                  <AppstoreOutlined style={{ marginRight: 5 }} />
                  目录视图
                </div>
                <div
                  className={`${styles.viewToggleButton} ${viewType === 'layer' ? styles.active : ''}`}
                  onClick={() => setViewType('layer')}
                >
                  {/* 确保 BarsOutlined 已经正确导入 */}
                  {BarsOutlined && <BarsOutlined style={{ marginRight: 5 }} />}
                  图层视图
                </div>
              </div>

              <Input
                placeholder="搜索图层"
                prefix={<SearchOutlined />}
                onChange={handleSearchChange} // 绑定处理函数
                value={searchValue} // 受控组件
                style={{ marginBottom: '16px', color: '#333' }}
              />
            </div>
            {/* 可滚动内容区域 */}
            <div className={styles.scrollableContent}>
              {/* 根据视图类型显示不同内容 */}
              {viewType === 'directory' ? (
                // 如果正在搜索且没有结果，显示提示
                searchValue && displayData.length === 0 ? (
                  <div style={{ textAlign: 'center', padding: '20px 0', color: '#999' }}>
                    没有找到匹配的节点
                  </div>
                ) : (
                  // 渲染 Tree 组件
                  <Tree
                    checkable // 允许节点前添加 Checkbox
                    onExpand={onExpand} // 展开/收起时触发
                    expandedKeys={currentExpandedKeys} // 受控控制展开的节点
                    autoExpandParent={autoExpandParent} // 是否自动展开父节点
                    treeData={displayData} // 树的数据源
                    style={{ fontSize: '14px' }}
                    selectedKeys={selectedKeys} // 受控控制选中的节点
                    checkedKeys={layers.filter(layer => layer.checked).map(layer => layer.id)} // 同步图层可见性
                    onCheck={(checkedKeys, { node }) => {
                      // 更新图层加载/移除状态
                      if (node.Attribute?.servicePath) {
                        const isChecked = checkedKeys.includes(node.Rid);
                        handleLayerCheckToggle(node, isChecked);
                      }
                    }}
                    onSelect={(keys, { node }) => { // 节点选中时触发
                      setSelectedKeys(keys);
                      // 处理节点选择逻辑
                      if (node.Attribute) {
                        if (node.Attribute.serviceTypeName) {
                          console.log('Selected service:', node);
                        } else if (node.Attribute.dataType === '1') {
                          console.log('Selected folder:', node);
                        }
                      }
                    }}
                    // 自定义节点渲染
                    titleRender={(node) => (
                      <span>
                        {/* 根据节点类型显示不同图标 */}
                        {node.Attribute?.dataType === '2' ?
                          <GlobalOutlined style={{ marginRight: 8 }} /> :
                          <FolderOutlined style={{ marginRight: 8 }} />}
                        {/* 显示节点的 Name 属性作为标题 */}
                        {node.Name}
                      </span>
                    )}
                  />
                )
              ) : (
                // 图层视图内容
                <div className={styles.layerListContainer}>
                  <h3>
                    <GlobalOutlined style={{ marginRight: 8 }} />
                    已加载的图层服务
                  </h3>
                  {layers.length > 0 ? (
                    <div className={styles.layerList}>
                      {layers.map((layer, index) => (
                        <div key={layer.id} className={styles.layerItem}>
                          <div className={styles.layerName}>
                            {layer.layerName}
                          </div>
                          <div className={styles.layerControls}>
                            {/* 上移按钮 */}
                            <button
                              onClick={() => handleMoveLayerUp(layer.id, index)}
                              disabled={index === 0}
                            >
                              ↑
                            </button>
                            {/* 下移按钮 */}
                            <button
                              onClick={() => handleMoveLayerDown(layer.id, index)}
                              disabled={index === layers.length - 1}
                            >
                              ↓
                            </button>
                            {/* 显示/隐藏按钮 */}
                            <button
                              onClick={() => {
                                const newVisible = !layer.visible;
                                setLayers(prevLayers =>
                                  prevLayers.map(l =>
                                    l.id === layer.id ? { ...l, visible: newVisible, checked: newVisible } : l
                                  )
                                );
                                if (mapRef.current) {
                                  mapRef.current.setLayerVisibility(layer.id, newVisible);
                                }
                              }}
                              className={!layer.visible ? 'hidden' : ''}
                            >
                              {layer.visible ? '隐藏' : '显示'}
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className={styles.emptyMessage}>
                      暂无已加载的图层服务
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>)}

      {/* 地图容器 */}
      <div className={styles.mapView}>

        {isLoadingConfig ? (
          // 如果正在加载配置，显示加载指示器
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', background: '#f0f2f5' }}>
            <Spin size="large" tip="地图配置加载中..." />
          </div>
        ) : (
          // 显示默认地图组件
          <>
            {/* 根据mapType状态决定显示哪个地图组件 */}
            <div style={{ position: 'relative', width: '100%', height: '100%' }}>
              {mapType === '2D' ? (
                <LeafletMap
                  ref={mapRef}
                  initZoom={finalInitZoom}
                  initCenter={finalInitCenter}
                  onSwitchTo3D={handleSwitchTo3D}
                  showMapInfoPanel={showMapInfoPanel}
                  showMapScale={showMapScale}
                  showMeasureBtn={showMeasureBtn}
                  showMapCompass={showMapCompass}
                  isLeftSidebarCollapsed={isLeftSidebarCollapsed}
                  isRightSidebarCollapsed={isRightSidebarCollapsed}
                />
              ) : (
                <CesiumMap
                  ref={mapRef}
                  initCenter={finalInitCenter}
                  initHeight={finalInitHeight}
                  showMapInfoPanel={showMapInfoPanel}
                  showMapScale={showMapScale}
                  showMeasureBtn={showMeasureBtn}
                  showMapCompass={showMapCompass}
                  isLeftSidebarCollapsed={isLeftSidebarCollapsed}
                  isRightSidebarCollapsed={isRightSidebarCollapsed}
                  theme={theme}
                />
              )}

              {/* 卷帘相关组件 */}
              {renderSplitComponents()}
            </div>

            {/* 右上角控制面板 */}
            {showCoordinateQuery && (
              <div className={`${styles.topRightControlPanel}`}>
                {/* 坐标查询组件 */}
                <CoordinateQuery
                  onLocationTo={handleLocationToCoordinate}
                  onPickCoordinate={handleTogglePickCoordinate}
                  onClearMarkers={handleClearCoordinateMarkers}
                  isPickingMode={isPickingCoordinate}
                  pickedCoordinate={pickedCoordinate}
                />
              </div>
            )}

            {/* 地图控制面板 */}
            <div className={MapControlPanelClasses}>
              {/* 缩放控件 */}
              {showZoomControls && <ZoomControls
                onBackToDefault={() => mapRef.current?.backToDefault()}
                onZoomIn={() => mapRef.current?.zoomIn()}
                onZoomOut={() => mapRef.current?.zoomOut()}
                theme={theme}
              />}

              {/* 卷帘模式切换按钮 */}
              {showSplitButton && (
                <button
                  onClick={handleToggleSplitMode}
                  className={`${styles.toggleButtonYzt} ${theme === 'dark' ? styles.darkTheme : styles.lightTheme} ${isSplitMode ? styles.active : ''}`}
                  title={isSplitMode ? '退出卷帘模式' : '启用卷帘模式'}
                >
                  卷帘
                </button>
              )}

              {/* 切换按钮 */}
              <button
                onClick={toggleMapType}
                className={`${styles.toggleButtonYzt} ${theme === 'dark' ? styles.darkTheme : styles.lightTheme}`}
              >
                {mapType === '2D' ? '3D' : '2D'}
              </button>
            </div>

            <div className={basemapControlsWrapperClasses}>
              {/* 底图选择面板 */}
              {basemapShow && (
                <div className={styles.basemapPanel}>
                  {basemapLayersInfo.length === 0 ? (
                    <div className={styles.noBasemapIndicator}>
                      <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="none" stroke="#AAA" strokeLinecap="round" strokeLinejoin="round">
                        <rect x="2" y="2" width="20" height="20" rx="2" />
                        <path d="M5 18q4-6 8 0m-2 0q5-4 9 0" />
                        <circle cx="18" cy="7" r="2" />
                        <path strokeWidth="1.5" d="m4 4 16 16" />
                      </svg>
                      <span>无底图</span>
                    </div>
                  ) : (
                    <div className={styles.basemapList}>
                      {basemapLayersInfo.map(layer => {
                        const isSelected = basemapLayers.find(l => l.parentId === layer.Rid)?.visible ?? false
                        return (
                          <div
                            key={layer.key}
                            className={`${styles.basemapItem} ${isSelected ? styles.selected : ''}`}
                            onClick={() => handleBasemapSelect(layer)}
                          >
                            <div className={styles.basemapThumbnailContainer}>
                              <img
                                src={layer.Attribute?.thumbnail || mapImg}
                                alt={layer.title}
                                className={styles.basemapThumbnail}
                              />
                              {/* 选中标记 */}
                              {isSelected && (
                                <div className={styles.basemapSelectedIndicator}>
                                  <CheckCircleFilled />
                                </div>
                              )}
                            </div>
                            <span className={styles.basemapLabel}>{layer.title}</span>
                          </div>
                        )
                      }
                      )}
                    </div>
                  )}
                </div>
              )}

              {/* 底图切换按钮 */}
              <div
                onClick={() => { setBasemapShow(!basemapShow) }}
                className={`${styles.basemapToggle} ${theme === 'dark' ? styles.darkTheme : styles.lightTheme}`}
              >
                {theme === 'dark' ?
                <img src={mapImgButton} alt="地图底图" style={{ width: '100%', height: '100%', borderRadius: '8px' }} />
                : <img src={mapImg} alt="地图底图" style={{ width: '100%', height: '100%', borderRadius: '8px' }} />}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
});

export default OneMap;