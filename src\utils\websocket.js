import mqtt from 'mqtt';
import { getGuid } from './helper';
import { getMqttWsUrl } from '../utils/config'


export  function  GetMqttClient() {
    //const {setJc,Reboot}=useModel('osdModel')
    
    const options =()=>{
        return {
        keepalive: 60,
        clientId: getGuid(),
        protocolId: 'MQTT',
        protocolVersion: 4,
        clean: true,
        reconnectPeriod: 1000,
        connectTimeout: 30 * 1000,
        rejectUnauthorized: false,
    }}
   // const client = mqtt.connect("ws://47.108.62.6:8083/mqtt",options);
   const client = mqtt.connect(getMqttWsUrl(), options());
     client.on('error', (err) => {
         console.log('MQTT连接错误:', err);
         console.log('连接URL:', getMqttWsUrl());
         console.log('连接选项:', options());
         client.end()
       })

      client.on("connect", (e) => {
             console.log('MQTT连接成功:', e);
            
         });

    return client;
}

