import React, { useEffect, useRef, useState, forwardRef, useCallback } from 'react';
import { throttle } from 'lodash';
import { Cesium } from 'umi';
import { GetCesiumViewer } from '@/utils/cesium_help';
import 'cesium/Build/Cesium/Widgets/widgets.css';
import { message } from 'antd';
import { MAP_CONFIG } from './config';
import MapInfoPanel from '@/pages/GT/components/MapControls/MapInfoPanel';
import MapCompass from '@/pages/GT/components/MapControls/MapCompass';
import MapScale from '@/pages/GT/components/MapControls/MapScale';
import MapMeasure from '@/pages/GT/components/MapControls/MapMeasure';

const CesiumMap = forwardRef((props, ref) => {
  const {
    service,
    initCenter,
    initHeight,
    showMapInfoPanel,
    showMapScale,
    showMeasureBtn,
    showMapCompass,
    isLeftSidebarCollapsed,
    isRightSidebarCollapsed,
    theme, // 接收外部传入的主题参数
  } = props;
  const cesiumContainer = useRef(null);
  const viewerRef = useRef(null);
  const isCameraMoving = useRef(false);  // 添加相机移动状态引用
  const [layers, setLayers] = useState([]);
  // 添加状态来存储鼠标位置和相机高度
  const [latitude, longitude] = initCenter;
  const [mousePosition, setMousePosition] = useState({ lng: longitude, lat: latitude });
  const [cameraHeight, setCameraHeight] = useState(initHeight);

  // // 将viewerRef.current暴露给父组件
  // useEffect(() => {
  //   if (typeof ref === 'function') {
  //     ref(viewerRef.current)
  //   } else if (ref && typeof ref === 'object') {
  //     ref.current = viewerRef.current
  //   }
  // }, [ref, viewerRef.current]);

  // 根据 zIndex 调整 Cesium ImageryLayer 的顺序
  const adjustImageryLayerOrder = (() => {
    let isAdjusting = false; // 防止重入

    return (currentLayers) => {
      if (!viewerRef.current || isAdjusting) return;

      try {
        isAdjusting = true;
        const imageryLayers = viewerRef.current.imageryLayers;

        // 按zIndex排序并过滤出ImageryLayer
        const orderedLayers = currentLayers
          .filter(l => l.instance instanceof Cesium.ImageryLayer)
          .sort((a, b) => a.zIndex - b.zIndex);

        // 使用raiseToTop调整顺序，从底层开始
        orderedLayers.forEach(layerObj => {
          try {
            if (imageryLayers.contains(layerObj.instance)) {
              imageryLayers.raiseToTop(layerObj.instance);
            }
          } catch (error) {
            console.error(`调整图层顺序时出错 (${layerObj.id}):`, error);
          }
        });
      } catch (error) {
        console.error('调整图层顺序时出错:', error);
      } finally {
        isAdjusting = false;
      }
    };
  })();

  // 添加图层函数
  const addLayer = async (serviceConfig) => {
    if (!viewerRef.current || !serviceConfig) return null;

    try {
      let layer;
      let layerObj;
      // 确保 zIndex 是数字，如果未提供则给一个默认值或基于当前层数计算
      const zIndex = typeof serviceConfig.zIndex === 'number' ? serviceConfig.zIndex : (layers.length > 0 ? Math.max(...layers.map(l => l.zIndex)) + 1 : 0);
      const { url, type, ...options } = serviceConfig;


      switch (type) {
        // ArcGIS服务
        case 'ArcGISTiledMapServiceLayer':
          const haveXYZ = url.includes('{z}/{y}/{x}');
          if (!haveXYZ) {
            layer = new Cesium.ArcGisMapServerImageryProvider.fromUrl(url, options)
          } else {
            layer = new Cesium.UrlTemplateImageryProvider({
              url, // url: 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}'
              tilingScheme: new Cesium.WebMercatorTilingScheme(), 
              maximumLevel: 18,
              ...options
            });
          }
          break;
        
        case 'ArcGISDynamicMapServiceLayer':
          layer = await Cesium.ArcGisMapServerImageryProvider.fromUrl(url, {
            enablePickFeatures: true,
            ...options
          });
          break;

        case 'ArcGISImageMapServiceLayer':
          layer = await Cesium.ArcGisMapServerImageryProvider.fromUrl(url, {
            enablePickFeatures: true,
            ...options
          });
          break;

        case 'ArcGISFeatureMapServiceLayer':
          const dataSource = await Cesium.GeoJsonDataSource.load(url, {
            stroke: Cesium.Color.HOTPINK,
            fill: Cesium.Color.PINK.withAlpha(0.5),
            strokeWidth: 3,
            ...options
          });
          viewerRef.current.dataSources.add(dataSource);
          // 创建图层对象并添加到状态中 (保持一致性)
          layerObj = {
            id: serviceConfig.id,
            config: serviceConfig,
            instance: dataSource, // 存储 dataSource 实例
            zIndex: zIndex // 存储 zIndex
          };
          // 添加到数组，保持添加顺序，不排序
          setLayers(prevLayers => {
              const newLayers = [layerObj, ...prevLayers];
              // DataSource 不参与 ImageryLayer 排序
              return newLayers;
          });
          return layerObj;

        // OGC服务
        case 'WmsServiceLayer':
          const wmsUrl = url.split('?')[0];
          const urlParams = new URLSearchParams(url.split('?')[1]);
          
          layer = new Cesium.WebMapServiceImageryProvider({
            url: wmsUrl,
            layers: urlParams.get('layers'),
            parameters: {
              service: 'WMS',
              version: urlParams.get('version') || '1.1.1',
              request: 'GetMap',
              format: urlParams.get('format') || options.format || 'image/png',
              transparent: true
            },
            ...options
          });
          break;

        case 'WmtsServiceLayer':

        // 天地图服务
        case 'TiandituVecLayer':
        case 'TiandituImgLayer':
        case 'TiandituCvaLayer':
          let tiandituUrl = url.split('?')[0]
          const params = new URLSearchParams(url.split('?')[1]);
          const hasToken = url.includes('tk=');
          const token = hasToken ? params.get('tk') : MAP_CONFIG.TIANDITU_KEY;
          tiandituUrl = tiandituUrl + '?tk=' + token;
          
          layer = new Cesium.WebMapTileServiceImageryProvider({
            url: tiandituUrl,
            layer: params.get('LAYER') || params.get('layer') || 'img',
            style: 'default',
            format: 'tiles',
            tileMatrixSetID: 'w', 
            tilingScheme: new Cesium.WebMercatorTilingScheme(),
            maximumLevel: 18,
            subdomains: params.get('subdomains') || ['0', '1', '2', '3', '4', '5', '6', '7'],
            ...options
          });
          break;

        // 其他网络地图服务
        case 'WebTileLayer':
          layer = new Cesium.UrlTemplateImageryProvider({
            url,
            ...options
          });
          break;

        // 正射影像
        case 'TmsServiceLayer':
          const { id, accessInfo, expandParam } = options;
          const expandParamArr = JSON.parse(expandParam).expandParam;
          let paramsObj = {};
          if (expandParamArr && expandParamArr.length > 0) {
            // 使用 reduce 将数组转换为对象
            paramsObj = expandParamArr.reduce((acc, item) => {
              acc[item.paramKey] = item.paramValue;
              return acc;
            }, {});
          }

          const lat = parseFloat(paramsObj.lat) || parseFloat(paramsObj['纬度']) || 30.06346956642108;
          const long = parseFloat(paramsObj.long) || parseFloat(paramsObj['经度']) || 105.31706203454723;
          // const zoom = parseInt(paramsObj.zoom, 10) || parseFloat(paramsObj['缩放层级'])  || 16;

          viewerRef.current.camera.flyTo({
            destination: Cesium.Cartesian3.fromDegrees(long, lat, 1800),
            orientation: {
              heading: Cesium.Math.toRadians(0.0),
              pitch: Cesium.Math.toRadians(-90.0),
              roll: 0.0
            }
          });
          layer = new Cesium.UrlTemplateImageryProvider({
            url: url.replace('{y}', `{reverseY}`), 
            tilingScheme: new Cesium.WebMercatorTilingScheme(),
            rectangle: Cesium.Rectangle.fromDegrees(73.5, 3.8, 135.0, 53.5),
            maximumLevel: 18,
            ...options
          });
          break;

        case 'Cesium3DTileService':
          try {
            // 优化3D Tiles加载配置
            const customOptions = {
              ...options,
              // 调整动态屏幕空间误差参数，减少LOD切换频率
              dynamicScreenSpaceError: true,
              dynamicScreenSpaceErrorDensity: 1.0e-4, // 降低密度，减少更新频率
              dynamicScreenSpaceErrorFactor: 16.0, // 降低因子，减少切换频率
              dynamicScreenSpaceErrorHeightFalloff: 0.25, // 增加高度衰减，提高远处LOD稳定性
              maximumScreenSpaceError: 10.0, // 容错，减少切换
              
              // 优化LOD跳过策略
              skipLevelOfDetail: true,
              baseScreenSpaceError: 64, // 降低基准错误阈值，提前开始LOD
              skipScreenSpaceErrorFactor: 8, // 降低跳过因子，减少跳过导致的卡顿
              skipLevels: 2, // 增加跳过层级，加快加载
              
              // 预加载和缓存策略
              immediatelyLoadDesiredLevelOfDetail: true, // 立即加载目标LOD
              preferLeaves: false, // 不优先加载叶子节点，减少初始加载压力
              loadSiblings: true, // 预加载兄弟节点，提升缓存命中率
              
              // 优化剔除和渲染
              cullWithChildrenBounds: true,
              optimizeForCesium: true,
              
              // 添加预加载距离配置
              preloadFlightDestinations: true,
              maximumMemoryUsage: 1024, // 限制内存使用，防止OOM
              
              // 启用压缩纹理
              compressTextureCoordinates: true,
              
              // 优化网络请求
              maximumSimultaneousRequests: 6, // 限制并发请求数
              throttleRequests: true, // 启用请求节流
            }
            const tileset = await Cesium.Cesium3DTileset.fromUrl(url, customOptions);
            // 为3DTileset设置ID标识，用于卷帘功能查找
            tileset._layerId = serviceConfig.id;

            // 添加到场景
            viewerRef.current.scene.primitives.add(tileset);

            // 等待tileset完全加载
            await tileset.readyPromise;

            viewerRef.current.flyTo(tileset);
            layerObj = {
              id: serviceConfig.id,
              config: serviceConfig,
              instance: tileset,
              zIndex: zIndex
            };
            // 添加到数组，保持添加顺序
            setLayers(prevLayers => {
              const newLayers = [layerObj, ...prevLayers];
              return newLayers;
            });
            return layerObj;
          } catch (error) {
            console.error('Failed to load 3D tiles:', error);
            throw error;
          }

        default:
          message.warning(`不支持的服务类型: ${type}`);
          return null;
      }

      // 添加图层到地图
      if (layer) {
        const imageryLayer = viewerRef.current.imageryLayers.addImageryProvider(layer);
        // 为图层设置ID标识，用于卷帘功能查找
        imageryLayer._layerId = serviceConfig.id;
        console.log('imagerylayer added:', imageryLayer);

        // 创建图层对象并添加到状态中
        layerObj = {
          id: serviceConfig.id,
          config: serviceConfig,
          instance: imageryLayer,
          zIndex: zIndex // 存储 zIndex
        };
        // 只有成功创建了 layerObj 才更新状态
        if (layerObj) {
            // 添加到数组，保持添加顺序，不排序
            setLayers(prevLayers => {
                let newLayers = [layerObj, ...prevLayers];
                // 添加后正序调整所有 ImageryLayer 的顺序
                adjustImageryLayerOrder(newLayers);
                // zIndex倒序排序，与后续上下移动图层的顺序保持一致
                newLayers.sort((a, b) => b.zIndex - a.zIndex)
                return newLayers;
            });
            return layerObj;
        }
      }
    } catch (error) {
      console.error('加载服务失败:', error);
      message.error('加载服务失败: ' + error.message);
    }
    
    return null;
  };

  // 移除图层函数
  const removeLayer = (layerId) => {
    const layerToRemove = layers.find(layer => layer.id === layerId);
    
    if (layerToRemove && viewerRef.current) {
      let removed = false;
      // 根据图层类型进行移除
      if (layerToRemove.instance instanceof Cesium.ImageryLayer) {
        removed = viewerRef.current.imageryLayers.remove(layerToRemove.instance, true);
      } else if (layerToRemove.instance instanceof Cesium.DataSource) {
         removed = viewerRef.current.dataSources.remove(layerToRemove.instance, true);
      } else if (layerToRemove.instance instanceof Cesium.Cesium3DTileset) {
         removed = viewerRef.current.scene.primitives.remove(layerToRemove.instance);
      } else {
         console.warn(`未知的图层实例类型，无法移除: ${layerToRemove.id}`);
      }

      if (removed) {
          setLayers(prevLayers => {
              const newLayers = prevLayers.filter(layer => layer.id !== layerId);
              // 移除后可能需要重新调整顺序（如果移除的是 ImageryLayer）
              if (layerToRemove.instance instanceof Cesium.ImageryLayer) {
                  adjustImageryLayerOrder(newLayers);
              }
              return newLayers;
          });
          return true;
      } else {
          console.warn(`移除图层 ${layerId} 失败，可能实例已不存在`);
          // 即使移除失败，也尝试从 state 中移除，防止状态不一致
          setLayers(prevLayers => prevLayers.filter(layer => layer.id !== layerId));
      }
    }

    return false;
  };

  // 上移图层函数
  const moveLayerUp = (layerId) => {
    setLayers(prevLayers => {
      const index = prevLayers.findIndex(layer => layer.id === layerId);
      // 如果找不到或已经在最上面 (index 0)，则不移动
      if (index <= 0) return prevLayers;

      const newLayers = [...prevLayers];
      const layerToMove = { ...newLayers[index] }; // 创建副本以修改 zIndex
      const layerAbove = { ...newLayers[index - 1] }; // 创建副本以修改 zIndex

      // 1. 交换 zIndex
      const tempZIndex = layerToMove.zIndex;
      layerToMove.zIndex = layerAbove.zIndex;
      layerAbove.zIndex = tempZIndex;

      // 2. 更新数组中的对象 (使用新 zIndex 的副本)
      newLayers[index] = layerAbove;
      newLayers[index - 1] = layerToMove;

      // 3. 根据更新后的 zIndex 重新调整所有 ImageryLayer 的顺序
      adjustImageryLayerOrder(newLayers);
      
      return newLayers; // 返回更新后的数组
    });
  };

  // 下移图层函数
  const moveLayerDown = (layerId) => {
    setLayers(prevLayers => {
      const index = prevLayers.findIndex(layer => layer.id === layerId);
      // 如果找不到或已经在最下面，则不移动
      if (index === -1 || index >= prevLayers.length - 1) return prevLayers;

      const newLayers = [...prevLayers];
      const layerToMove = { ...newLayers[index] }; // 创建副本
      const layerBelow = { ...newLayers[index + 1] }; // 创建副本

      // 1. 交换 zIndex
      const tempZIndex = layerToMove.zIndex;
      layerToMove.zIndex = layerBelow.zIndex;
      layerBelow.zIndex = tempZIndex;

      // 2. 更新数组中的对象
      newLayers[index] = layerBelow;
      newLayers[index + 1] = layerToMove;

      // 3. 根据更新后的 zIndex 重新调整所有 ImageryLayer 的顺序
      adjustImageryLayerOrder(newLayers);

      return newLayers; // 返回更新后的数组
    });
  };

  // 设置图层显示/隐藏状态
  const setLayerVisibility = (layerId, visible) => {
    const layer = layers.find(layer => layer.id === layerId);
    console.log('Setting visibility for layer:', layer);
    
    if (layer && layer.instance) {
      // Cesium 的 ImageryLayer, DataSource, Primitive 都有 show 属性
      if (typeof layer.instance.show !== 'undefined') {
          layer.instance.show = visible;
          // 更新图层状态以反映可见性变化
          setLayers(prevLayers =>
            prevLayers.map(l =>
              l.id === layerId
                ? { ...l, visible }
                : l
            )
          );
          return true;
      } else {
          console.warn(`图层 ${layerId} 的实例没有 show 属性`);
      }
    }
    return false;
  };

  // 移除所有图层函数
  const removeAllLayers = () => {
    return new Promise((resolve) => {
      if (viewerRef.current && layers.length > 0) {
        console.log('Removing all layers from 3D map:', layers);
        const imageryLayersCollection = viewerRef.current.imageryLayers;
        const dataSourcesCollection = viewerRef.current.dataSources;
        const primitivesCollection = viewerRef.current.scene.primitives;

        // 先从 Cesium 中移除所有相关实例
        imageryLayersCollection.removeAll(true); // 移除并销毁所有 ImageryLayer
        dataSourcesCollection.removeAll(true); // 移除并销毁所有 DataSource
        // Primitives 需要手动遍历移除，因为 removeAll 不一定销毁
        const primitivesToRemove = [];
        for (let i = 0; i < primitivesCollection.length; i++) {
            const p = primitivesCollection.get(i);
            // 检查是否是我们添加的 3DTileset (可以根据 URL 或其他标识)
            const isManagedPrimitive = layers.some(l => l.instance === p && p instanceof Cesium.Cesium3DTileset);
            if (isManagedPrimitive) {
                primitivesToRemove.push(p);
            }
        }
        primitivesToRemove.forEach(p => primitivesCollection.remove(p));


        // 清空内部状态
        setLayers([]);
        console.log('All layers removed from 3D map state and Cesium viewer.');
      }
      resolve(); // 无论如何都解析，表示操作完成
    });
  };

  // TODO:添加marker
  const addMarker = (marker) => {
    return
  };

  // TODO:移除marker
  const removeMarker = (marker) => {
    return
  }

  // TODO:获取所有layer
  const getAllLayers = () => {
    return
  }

  // 卷帘功能相关方法
  const setSplitPosition = useCallback((position) => {
    if (viewerRef.current && viewerRef.current.scene) {
      viewerRef.current.scene.splitPosition = position
    }
  }, [])

  const setLayerSplitDirection = useCallback((layerId, direction) => {
    const layer = layers.find(l => l.id === layerId)
    if (layer && layer.instance) {
      // 支持ImageryLayer和3DTileset的splitDirection设置
      if (layer.instance.splitDirection !== undefined) {
        layer.instance.splitDirection = direction
      }
    } else {
      console.warn(`未找到图层 ${layerId} 或图层实例为空`)
    }
  }, [layers])

  const resetAllLayersSplitDirection = useCallback(() => {
    if (!viewerRef.current) return

    // 重置所有ImageryLayer的splitDirection
    for (let i = 0; i < viewerRef.current.imageryLayers.length; i++) {
      const layer = viewerRef.current.imageryLayers.get(i)
      if (layer.splitDirection !== undefined) {
        layer.splitDirection = Cesium.SplitDirection.NONE
      }
    }

    // 重置所有3DTileset的splitDirection
    for (let i = 0; i < viewerRef.current.scene.primitives.length; i++) {
      const primitive = viewerRef.current.scene.primitives.get(i)
      if (primitive instanceof Cesium.Cesium3DTileset && primitive.splitDirection !== undefined) {
        primitive.splitDirection = Cesium.SplitDirection.NONE
      }
    }

    console.log('已重置所有图层的splitDirection为NONE')
  }, [])

  // 标准化缩放方法
  const zoomIn = React.useCallback(() => {
    if (viewerRef.current) {
      const camera = viewerRef.current.camera;
      const currentHeight = camera.positionCartographic.height / 2;
      setCameraHeight(currentHeight);
      camera.zoomIn(currentHeight);
    }
  }, []);

  const zoomOut = React.useCallback(() => {
    if (viewerRef.current) {
      const camera = viewerRef.current.camera;
      const currentHeight = camera.positionCartographic.height / 2;
      setCameraHeight(currentHeight);
      camera.zoomOut(currentHeight);
    }
  }, []);

  const setZoom = React.useCallback((height) => {
    if (viewerRef.current) {
      const camera = viewerRef.current.camera;
      const position = camera.positionCartographic;
      camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(
          Cesium.Math.toDegrees(position.longitude),
          Cesium.Math.toDegrees(position.latitude),
          height
        ),
        orientation: {
          heading: camera.heading,
          pitch: camera.pitch,
          roll: camera.roll
        }
      });
    }
  }, []);

  const setCenter = React.useCallback((center) => {
    if (viewerRef.current) {
      const camera = viewerRef.current.camera;
      const currentHeight = camera.positionCartographic.height;
      
      camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(
          center.longitude,
          center.latitude,
          center.height + 800,
          // currentHeight
        ),
        orientation: {
          // heading: camera.heading,
          // pitch: camera.pitch,
          // roll: camera.roll
          heading: Cesium.Math.toRadians(0.0),
          pitch: Cesium.Math.toRadians(-90.0),
          roll: 0.0
        }
      });
    }
  }, []);

  const backToDefault = React.useCallback(() => {
    if (viewerRef.current) {
      const camera = viewerRef.current.camera;
      const [latitude, longitude] = initCenter;
      camera.setView({
        destination: Cesium.Cartesian3.fromDegrees(longitude, latitude, initHeight),
        orientation: {
          heading: Cesium.Math.toRadians(0.0),
          pitch: Cesium.Math.toRadians(-60.0),
          roll: 0.0
        }
      });
    }
  }, []);

  // 坐标查询相关状态和方法
  const [coordinateEntities, setCoordinateEntities] = useState([]) // 存储坐标实体
  const [isPickingCoordinate, setIsPickingCoordinate] = useState(false) // 是否处于拾取模式
  const [pickedCoordinate, setPickedCoordinate] = useState(null) // 拾取到的坐标
  const coordinatePickHandler = useRef(null) // 坐标拾取事件处理器

  // 定位到指定坐标
  const locationToCoordinate = React.useCallback((coordinate) => {
    if (!viewerRef.current) return

    const { longitude, latitude } = coordinate

    // 定位到坐标
    viewerRef.current.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(longitude, latitude, 1000),
      orientation: {
        heading: Cesium.Math.toRadians(0.0),
        pitch: Cesium.Math.toRadians(-90.0),
        roll: 0.0
      },
      duration: 2.0
    })

    // 创建标记实体
    const entity = viewerRef.current.entities.add({
      position: Cesium.Cartesian3.fromDegrees(longitude, latitude),
      billboard: {
        image: 'data:image/svg+xml;base64,' + btoa(`
          <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
            <circle cx="16" cy="16" r="8" fill="#ff4d4f" stroke="#fff" stroke-width="2"/>
            <circle cx="16" cy="16" r="3" fill="#fff"/>
          </svg>
        `),
        scale: 1.0,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      },
      label: {
        text: `定位坐标\n经度: ${longitude.toFixed(6)}°\n纬度: ${latitude.toFixed(6)}°`,
        font: '12pt sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        pixelOffset: new Cesium.Cartesian2(0, -50),
        showBackground: true,
        backgroundColor: Cesium.Color.fromCssColorString('rgba(0, 0, 0, 0.7)'),
        backgroundPadding: new Cesium.Cartesian2(8, 4)
      }
    })

    // 保存实体到状态
    setCoordinateEntities(prev => [...prev, entity])
  }, [])

  // 开始/停止拾取坐标
  const togglePickCoordinate = React.useCallback(() => {
    if (!viewerRef.current) return

    setIsPickingCoordinate(prev => {
      const newState = !prev

      if (newState) {
        // 开始拾取模式
        viewerRef.current.canvas.style.cursor = 'crosshair'

        // 创建点击事件处理器
        coordinatePickHandler.current = new Cesium.ScreenSpaceEventHandler(viewerRef.current.scene.canvas)
        coordinatePickHandler.current.setInputAction(handleCesiumClick, Cesium.ScreenSpaceEventType.LEFT_CLICK)
      } else {
        // 停止拾取模式
        if (coordinatePickHandler.current) {
          coordinatePickHandler.current.destroy()
          coordinatePickHandler.current = null
        }
        viewerRef.current.canvas.style.cursor = ''
      }

      return newState
    })
  }, [])

  // 处理Cesium地图点击事件
  const handleCesiumClick = React.useCallback((click) => {
    // 使用更精确的坐标拾取方法
    let cartesian = null
    let pickedPosition = null

    // 首先尝试从地形获取坐标
    const ray = viewerRef.current.camera.getPickRay(click.position)
    pickedPosition = viewerRef.current.scene.globe.pick(ray, viewerRef.current.scene)

    if (pickedPosition) {
      cartesian = pickedPosition
    } else {
      // 如果地形拾取失败，使用椭球体拾取
      cartesian = viewerRef.current.camera.pickEllipsoid(click.position, viewerRef.current.scene.globe.ellipsoid)
    }

    if (cartesian) {
      const cartographic = Cesium.Cartographic.fromCartesian(cartesian)
      const longitude = parseFloat(Cesium.Math.toDegrees(cartographic.longitude).toFixed(6))
      const latitude = parseFloat(Cesium.Math.toDegrees(cartographic.latitude).toFixed(6))

      const coordinate = { longitude, latitude }
      setPickedCoordinate(coordinate)

      // 创建拾取标记实体
      const entity = viewerRef.current.entities.add({
        position: cartesian,
        billboard: {
          image: 'data:image/svg+xml;base64,' + btoa(`
            <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <circle cx="12" cy="12" r="6" fill="#52c41a" stroke="#fff" stroke-width="2"/>
              <circle cx="12" cy="12" r="2" fill="#fff"/>
            </svg>
          `),
          scale: 1.0,
          verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
        },
        label: {
          text: `拾取坐标\n经度: ${longitude.toFixed(6)}°\n纬度: ${latitude.toFixed(6)}°`,
          font: '12pt sans-serif',
          fillColor: Cesium.Color.WHITE,
          outlineColor: Cesium.Color.BLACK,
          outlineWidth: 2,
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          pixelOffset: new Cesium.Cartesian2(0, -40),
          showBackground: true,
          backgroundColor: Cesium.Color.fromCssColorString('rgba(0, 0, 0, 0.7)'),
          backgroundPadding: new Cesium.Cartesian2(8, 4)
        }
      })

      // 保存实体到状态
      setCoordinateEntities(prev => [...prev, entity])

      // 停止拾取模式
      setIsPickingCoordinate(false)
      if (coordinatePickHandler.current) {
        coordinatePickHandler.current.destroy()
        coordinatePickHandler.current = null
      }
      if (viewerRef.current) {
        viewerRef.current.canvas.style.cursor = ''
      }
    }
  }, [])

  // 清除所有坐标标记
  const clearCoordinateMarkers = React.useCallback(() => {
    if (!viewerRef.current) return

    coordinateEntities.forEach(entity => {
      viewerRef.current.entities.remove(entity)
    })
    setCoordinateEntities([])
    setPickedCoordinate(null)
  }, [coordinateEntities])

  React.useImperativeHandle(
    ref,
    () => ({
      addLayer,
      removeLayer,
      removeAllLayers,
      addMarker,
      removeMarker,
      getAllLayers,
      moveLayerUp,
      moveLayerDown,
      getLayers: () => layers,
      setLayerVisibility,
      getViewer: () => viewerRef.current,
      zoomIn,
      zoomOut,
      setZoom,
      setCenter,
      backToDefault,
      // 卷帘功能相关方法
      setSplitPosition,
      setLayerSplitDirection,
      resetAllLayersSplitDirection,
      // 坐标查询相关方法
      locationToCoordinate,
      togglePickCoordinate,
      clearCoordinateMarkers,
      getPickedCoordinate: () => pickedCoordinate,
      getIsPickingCoordinate: () => isPickingCoordinate,
    }),
    [layers,
      addLayer, removeLayer, removeAllLayers, moveLayerUp, moveLayerDown, setLayerVisibility,
      zoomIn, zoomOut, setZoom, setCenter, backToDefault, addMarker, removeMarker, getAllLayers,
      setSplitPosition, setLayerSplitDirection, resetAllLayersSplitDirection,
      locationToCoordinate, togglePickCoordinate, clearCoordinateMarkers, pickedCoordinate, isPickingCoordinate
    ]
  );

  useEffect(() => {
    if (!cesiumContainer.current) return;

    // 初始化Cesium查看器，保存viewer引用以便清理
    const viewer = GetCesiumViewer(cesiumContainer.current);
    viewer.camera.setView({
      destination: Cesium.Cartesian3.fromDegrees(longitude, latitude, initHeight),
      orientation: {
        heading: Cesium.Math.toRadians(0.0),
        pitch: Cesium.Math.toRadians(-90.0),
        roll: 0.0
      }
    });

    // 移除默认的底图图层
    viewer.imageryLayers.remove(viewer.imageryLayers.get(0));

    // 添加一个基础底图，例如天地图影像
    const key = MAP_CONFIG.TIANDITU_KEY || 'd48a48254cdbd79edd0ed2c541639813'; // 使用配置或默认 Key
    // const baseImageryLayer = new Cesium.WebMapTileServiceImageryProvider({
    //     url: `http://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&tk=${key}`,
    //     layer: "tdtImgLayer",
    //     style: "default",
    //     format: "image/jpeg",
    //     tileMatrixSetID: "GoogleMapsCompatible", // 或者 'w'
    // });
    // viewer.imageryLayers.addImageryProvider(baseImageryLayer, 0); // 添加到底部

    // 添加天地图注记
    // let tdtCiaLayer = new Cesium.WebMapTileServiceImageryProvider({
    //   url: "http://t0.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&tk=" + key,
    //   layer: "tdtCiaLayer",
    //   style: "default",
    //   format: "image/png", // 注记通常是 png
    //   tileMatrixSetID: "GoogleMapsCompatible", // 或者 'w'
    // });
    // viewer.imageryLayers.addImageryProvider(tdtCiaLayer, 1); // 添加在底图之上


    // 启用按需渲染
    viewer.requestRenderMode = true;
    // 场景变化后多少秒内持续渲染，Infinity 表示仅在需要时渲染
    viewer.maximumRenderTimeChange = Infinity; 
    viewerRef.current = viewer;

    // 添加事件处理器
    const handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

    // 监听鼠标移动事件
    // 创建节流函数
    const throttledSetMousePosition = throttle(setMousePosition, 100);
    const throttledUpdateCameraHeight = throttle(() => {
      if (!viewerRef.current) return;
      const cartographicPosition = viewerRef.current.camera.positionCartographic;
      if (cartographicPosition) {
        const height = cartographicPosition.height.toFixed(2);
        setCameraHeight(height);
      } else {
        setCameraHeight('N/A');
      }
    }, 50, { leading: true, trailing: true });
    // 添加相机移动事件监听器
    viewer.camera.moveStart.addEventListener(() => {
      isCameraMoving.current = true;
    });
    viewer.camera.moveEnd.addEventListener(() => {
      isCameraMoving.current = false;
    });

    // 鼠标移动事件处理
    handler.setInputAction((movement) => {
      // 如果相机正在移动，跳过坐标拾取
      if (isCameraMoving.current) {
        // 当相机移动时，保持上一次的坐标
        return;
      }

      const cartesian = viewer.camera.pickEllipsoid(movement.endPosition, viewer.scene.globe.ellipsoid);
      if (cartesian) {
        const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
        const longitudeString = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6);
        const latitudeString = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6);
        throttledSetMousePosition({ lng: longitudeString, lat: latitudeString });
        throttledUpdateCameraHeight();
      } else {
        // 尝试从地形获取坐标
        const ray = viewer.camera.getPickRay(movement.endPosition);
        const positionOnTerrain = viewer.scene.globe.pick(ray, viewer.scene);
        if (positionOnTerrain) {
          const cartographic = Cesium.Cartographic.fromCartesian(positionOnTerrain);
          const longitudeString = Cesium.Math.toDegrees(cartographic.longitude).toFixed(6);
          const latitudeString = Cesium.Math.toDegrees(cartographic.latitude).toFixed(6);
          throttledSetMousePosition({ lng: longitudeString, lat: latitudeString });
          throttledUpdateCameraHeight();
        } else {
          throttledSetMousePosition({ lng: 'N/A', lat: 'N/A' });
        }
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

    viewer.camera.changed.addEventListener(throttledUpdateCameraHeight);
    viewer.camera.moveEnd.addEventListener(throttledUpdateCameraHeight);

    // 如果有服务配置，添加图层
    if (service) {
      addLayer(service);
    }

    return () => {
      // 清理事件监听器
      handler.destroy();
      viewer.camera.changed.removeEventListener(throttledUpdateCameraHeight);
      viewer.camera.moveEnd.removeEventListener(throttledUpdateCameraHeight);
      // 移除相机移动事件监听器
      viewer.camera.moveStart.removeEventListener(() => {
        isCameraMoving.current = true;
      });
      viewer.camera.moveEnd.removeEventListener(() => {
        isCameraMoving.current = false;
      });
      throttledSetMousePosition.cancel();
      throttledUpdateCameraHeight.cancel();
      // 清理Cesium实例
      if (viewerRef.current) {
        // 移除所有图层和数据源
        removeAllLayers().then(() => {
            // 销毁 viewer
            if (viewerRef.current) {
                viewerRef.current.requestRenderMode = false; 
                viewerRef.current.destroy();
                viewerRef.current = null;
                console.log('Cesium Viewer destroyed.');
            }
        });
      }
    };
  }, [service]);

  return (
    <div
      ref={cesiumContainer}
      style={{
        width: '100%',
        height: '100%',
        position: 'relative'
      }}>

      <div style={{
        display: 'flex',
        alignItems: 'center',
        position: 'absolute',
        bottom: '20px',
        left: '20px',
        gap: '15px',
        zIndex: 1000
      }}>
        {showMapInfoPanel && <MapInfoPanel
          mousePosition={mousePosition}
          mapType="3D"
          scaleInfo={cameraHeight}
        />}
        {showMapScale && viewerRef.current && <MapScale viewer={viewerRef.current} />}
      </div>
      {showMapCompass && viewerRef.current && <MapCompass viewer={viewerRef.current} isRightSidebarCollapsed = {isRightSidebarCollapsed} theme={theme}/>}
      {showMeasureBtn && viewerRef.current && <MapMeasure viewer={viewerRef.current}/>}
    </div>
  );
});

export default CesiumMap;