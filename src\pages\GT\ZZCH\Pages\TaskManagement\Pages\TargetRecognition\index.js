import { queryPage, queryPage2 } from '@/utils/MyRoute';
import { useModel } from "umi";
import { useState, useEffect, useMemo } from 'react';
import {
  Card, Table, Button, Modal, Form, Input, Select, List, Pagination,
  DatePicker, Checkbox, InputNumber, Radio, Upload, message, Spin, Splitter, Tooltip, Steps, Tabs, Descriptions
} from 'antd';
import LastPageButton from '@/components/LastPageButton';
import TableCols from './table';
import { axiosApi } from "@/services/general";
import { getDeviceName,WayLineTypeToString } from "@/utils/utils";
import { Get2, Post2 } from '@/services/general';
import { timeFormat } from "@/utils/helper";

// 目标识别页面
const TargetRecognition = ({doNotShowLastButton}) => {
  const { setPage, currentPage, pageData } = useModel("pageModel");
  const [dataSource, setDataSource] = useState([]);
  const [currentTablePage, setCurrentTablePage] = useState(1);
  const [loading, setLoading] = useState(false); // 目标识别表格加载状态
  const [loading2, setLoading2] = useState(false); // 飞行记录列表加载状态
  const [loading3, setLoading3] = useState(false) // 步骤弹窗的加载状态
  const [modalVisible, setModalVisible] = useState(false);
  const [showWaylineModal, setShowWaylineModal] = useState(false);
  const [isDJPage, setIsDJPage] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [activeTabKey, setActiveTabKey] = useState('1'); // 记录第一步的标签页状态
  const [selectedWayLine, setSelectedWayLine] = useState(); // 选择的航线
  const [filesUploaded, setFilesUploaded] = useState(false); // 是否上传了图片
  const [selectedFiles, setSelectedFiles] = useState([]); // 已上传的图片
  const [pendingFiles, setPendingFiles] = useState([]); // 等待上传的文件
  const [WayLineList, setWayLineList] = useState([]); // 飞行记录列表
  const [WayLineListOptions, setWayLineListOptions] = useState([]); // 飞行记录列表选项
  const [flightRecordPagination, setFlightRecordPagination] = useState({
    current: 1,
    pageSize: 10,
  }); // 飞行记录分页状态
  const [searchFlightRecord, setSearchFlightRecord] = useState(''); // 搜索筛选的飞行记录
  const [modelList, setModelList] = useState([]); // 模型列表
  const [selectedModel, setSelectedModel] = useState(); // 选择的模型
  const [cls, setCls] = useState([]) // 识别内容 (数字)
  const [modelClasses, setModelClasses] = useState([]); // 识别内容 (文字)
  const [resource, setResource] = useState(1); // 识别资源类型 (1: 飞行记录, 2: 图片)
  const [taskName, setTaskName] = useState(''); // 任务名称

  const handlePageChange = (page) => {
    setPage(queryPage(page));
  };
  const handlePageChange2 = (page) => {
    setPage(queryPage2(page));
  };

  const getDefaultData = async () => {
    try {
      setLoading(true);
      const res = await axiosApi(`/api/v1/AITask/GetList?taskType=目标识别`, 'GET', null);
      // 空数据处理
      if (res.data) {
        setDataSource(res.data);
        setLoading(false);
      } else {
        console.log('无数据或者获取数据失败');
      }
    } catch (error) {
      console.log(error);
    }
  }
  // 获取模型列表
  const getModelData = async () => {
    const pst = await Get2('/api/v1/AIModel/GetList', {});
    setModelList(pst);
  };

  const openModal = () => {
    setModalVisible(true);
  }


  useEffect(() => {
    // 如果pageData为空，说明是从DJ页面跳转过来的，需要设置isDJPage为true
    if (!pageData) {
      setIsDJPage(true);
    }
  }, [pageData]);

  const handleBatchUpload = async () => {
    // 提取原始文件对象
    const rawFiles = pendingFiles.map(file => file.originFileObj || file);
    if (rawFiles.length === 0) {
      message.warning("请选择要上传的文件！");
      return;
    }
    const formData = new FormData();
    rawFiles.forEach(file => {
      formData.append('images', file);
    });
    try {
      setLoading3(true);
      const token = localStorage.getItem('token');
      const res = await axiosApi(
        "/api/v1/upload/UploadImages",
        "POST",
        formData,
        {
          headers: {
            'auth': token,
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': '*',
            "Content-Type": "multipart/form-data",
          },
        }
      );
      setLoading3(false);
      message.success("上传成功！");
      console.log('上传成功', res);
      setSelectedFiles(res.data);
      setPendingFiles([]); // 清空待上传文件列表
      setFilesUploaded(false); // 标记未上传文件
      setResource(2); // 识别资源类型为图片
      setCurrentStep(1); // 切换到第二步
    } catch (error) {
      console.log('上传失败', error);
      message.error("上传失败! ");
      setLoading3(false);
    }
  }

  // 处理提交
  const handleSubmit = async () => {
    const data = {
      taskName: taskName,
      taskType: '目标识别',
      ModelPath: selectedModel.ObjectName,
      modelName: selectedModel.AName,
      acls: cls.join(','),
      aclsname: cls.map(num => modelClasses[num]).join(', '),
      flightRecordID: '',
      resource: resource,
    }

    if (resource === 1) {
      // 识别类型为飞行记录
      data.flightRecordID = selectedWayLine.FlightLineId;

    } else if (resource === 2) {
      // 识别类型为图片
      data.images = selectedFiles.images.map(f => f.url);
    }

    console.log('提交的数据', data);
    try {
      setLoading3(true)
      const res = await axiosApi('/api/v1/AITask/CreateAiTask', 'POST', data);
      setLoading3(false)
      message.success("提交成功！");
    } catch (error) {
      console.log('提交失败', error);
      message.error("提交失败! ");
      setLoading3(false)
    }
    setModalVisible(false);
    setCurrentStep(0);
    // 重新获取一遍数据
    getDefaultData();
  }

  // 生成模型选择选项
  const getModelSelect = (modelList) => {
    const list = []
    modelList.forEach(e => {
      list.push(<Select.Option key={e.Guid} data={e} value={e.Guid}>{e.AName}</Select.Option>)
    });
    return list;
  }

  // 模型选择后的处理
  const selectModal = (option) => {
    const model = option.data;
    setSelectedModel(model);
  }

  useEffect(() => {
    if (selectedModel) {
      const arr = selectedModel.AList.split(",");
      setModelClasses(arr);
    } else {
      setModelClasses([]);
    }
  }, [selectedModel]);

  const getAList = (selectedModel) => {
    if (!selectedModel) return <div></div>
    const arr = selectedModel.AList.split(",");

    const list = arr.map((e, index) => ({
      label: e,
      value: index,
      disabled: false,
    }));

    return <Checkbox.Group options={list} value={cls} onChange={(e) => setCls(e)} />
  }

  // 飞行记录弹窗过滤逻辑
  const filteredWaylines = useMemo(() =>
    WayLineListOptions.filter(item => {
      if (!searchFlightRecord) return true;
      const searchText = searchFlightRecord.toLowerCase();
      const formattedTime = timeFormat(item.TaskBeginTime).toLowerCase();

      return (
        item.label.toLowerCase().includes(searchText) ||
        (item.airport || '').toLowerCase().includes(searchText) ||
        formattedTime.includes(searchText)
      );
    }),
    [WayLineListOptions, searchFlightRecord]
  );

  // 步骤条相关
  const steps = [
    {
      title: '第一步',
      content: (
        <div style={{ marginTop: 16 }}>
          <Tabs
            activeKey={activeTabKey}
            onChange={(activeKey) => {
              setActiveTabKey(activeKey);
              // 切换标签时重置所有状态
              setSelectedWayLine(null);
              setFilesUploaded(false);
            }}
            items={[
              {
                key: '1',
                label: '图片上传',
                children: (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
                    <div style={{
                      maxHeight: '30vh',
                      overflowY: 'auto',
                      paddingRight: 8
                    }}>
                      <Upload.Dragger
                        name="file"
                        multiple={true}
                        fileList={pendingFiles}
                        beforeUpload={(file) => {
                          const isValidType = ['image/jpeg', 'image/png'].includes(file.type);
                          if (!isValidType) {
                            message.error('仅支持jpg/png格式文件！');
                            return false;
                          }
                          return false;
                        }}
                        onChange={({ file, fileList }) => {
                          // 转换文件对象格式
                          const formattedList = fileList.map(f => ({
                            ...f,
                            uid: f.uid,
                            name: f.name,
                            status: f.status
                          }));
                          setPendingFiles(formattedList);
                          setFilesUploaded(formattedList.length > 0);
                        }}
                        onRemove={(file) => {
                          const newFiles = pendingFiles.filter(f => f.uid !== file.uid);
                          setPendingFiles(newFiles);
                          setFilesUploaded(newFiles.length > 0);
                          return true;
                        }}
                      >
                        <p className="ant-upload-text">点击或拖拽上传图片</p>
                        <p className="ant-upload-hint">支持jpg/png格式</p>
                      </Upload.Dragger>
                    </div>
                    <Button
                      type="primary"
                      onClick={() => handleBatchUpload()}
                      disabled={!filesUploaded}
                      style={{ alignSelf: 'flex-end' }}
                    >
                      下一步
                    </Button>
                  </div>
                ),
              },
              {
                key: '2',
                label: '飞行记录',
                children: (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <label style={{ whiteSpace: 'nowrap' }}>飞行记录：&nbsp;</label>
                      <Input
                        placeholder="请选择飞行记录"
                        readOnly
                        value={selectedWayLine?.FlightLineName}
                        onClick={() => setShowWaylineModal(true)}
                        style={{ cursor: 'pointer' }}
                      />
                      <Button
                        type="link"
                        onClick={() => setShowWaylineModal(true)}
                        style={{ marginLeft: 8 }}
                      >
                        选择记录
                      </Button>
                    </div>

                    {/* 选择飞行记录弹窗 */}
                    <Modal
                      title="选择飞行记录"
                      open={showWaylineModal}
                      onCancel={() => setShowWaylineModal(false)}
                      footer={null}
                      width={800}
                    >
                      <div style={{ display: 'flex', flexDirection: 'column', gap: 16, height: '70vh' }}>
                        <Input
                          placeholder="搜索飞行记录（名称/机场/时间）"
                          allowClear
                          onChange={(e) => {
                            setSearchFlightRecord(e.target.value);
                            setFlightRecordPagination(prev => ({ ...prev, current: 1 })); // 搜索时重置到第一页
                          }}
                        />

                        <div style={{ flex: 1, overflowY: 'auto' }}>
                          <List
                            dataSource={filteredWaylines.slice(
                              (flightRecordPagination.current - 1) * flightRecordPagination.pageSize,
                              flightRecordPagination.current * flightRecordPagination.pageSize
                            )}
                            renderItem={(item) => (
                              <List.Item
                                onClick={() => {
                                  setSelectedWayLine(item.flightLineData);
                                  setShowWaylineModal(false);
                                }}
                                style={{
                                  cursor: 'pointer',
                                  padding: 12,
                                  borderBottom: '1px solid #f0f0f0',
                                }}
                                onMouseEnter={(e) => e.currentTarget.style.background = '#f5f5f5'}
                                onMouseLeave={(e) => e.currentTarget.style.background = '#fff'}
                              >
                                <List.Item.Meta
                                  title={item.label}
                                  description={
                                    <>
                                      <div>类型：{item.FlightLineType}</div>
                                      <div>所属机场：{item.airport}</div>
                                      <div>开始时间：{timeFormat(item.TaskBeginTime)}</div>
                                    </>
                                  }
                                />
                              </List.Item>
                            )}
                          />
                        </div>
                        <div style={{ marginTop: 16, alignSelf: 'flex-end' }}>
                          <Pagination
                            current={flightRecordPagination.current}
                            pageSize={flightRecordPagination.pageSize}
                            total={filteredWaylines.length}
                            onChange={(page, pageSize) => {
                              setFlightRecordPagination({
                                current: page,
                                pageSize: pageSize
                              });
                            }}
                            showSizeChanger
                            showQuickJumper
                            pageSizeOptions={['10', '20', '50']}
                          />
                        </div>
                      </div>
                    </Modal>

                    <Button
                      type="primary"
                      onClick={() => {
                        setCurrentStep(1);
                        setResource(1);
                      }}
                      disabled={!selectedWayLine}
                      style={{ alignSelf: 'flex-end' }}
                    >
                      下一步
                    </Button>
                  </div>
                ),
              },
            ]}
          />
        </div>
      ),
    },
    {
      title: '第二步',
      content: (
        <div style={{ marginTop: 16 }}>
          <Tabs
            defaultActiveKey="1"
            onChange={(activeKey) => {
            }}
            items={[
              {
                key: '1',
                label: '选择模型',
                children: (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <label style={{ whiteSpace: 'nowrap' }}>任务名称：&nbsp;</label>
                      <Input
                        placeholder="请输入任务名称"
                        value={taskName}
                        onChange={(e) => setTaskName(e.target.value)}
                        style={{ flex: 1 }}
                      />
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <label style={{ whiteSpace: 'nowrap' }}>选择模型：&nbsp;</label>
                      <Select
                        placeholder="请选择模型"
                        style={{ width: '100%' }}
                        value={selectedModel?.Guid}
                        onSelect={(value, option) => {
                          selectModal(option);
                        }}>
                        {getModelSelect(modelList)}
                      </Select>
                    </div>
                    {getAList(selectedModel)}
                    <div style={{
                      display: 'flex',
                      justifyContent: 'flex-end',
                      gap: 8,
                      marginTop: 16
                    }}>
                      <Button
                        type="primary"
                        onClick={() => {
                          setCurrentStep(0);
                          setActiveTabKey(resource === 1 ? '2' : '1'); // 这行代码似乎没有必要
                        }}
                      >
                        上一步
                      </Button>
                      <Button
                        type="primary"
                        onClick={() => setCurrentStep(2)}
                        disabled={cls?.length == 0 || !selectedModel || !taskName}
                      >
                        下一步
                      </Button>
                    </div>
                  </div>
                ),
              },
            ]}
          ></Tabs>
        </div>
      ),
    },
    {
      title: '第三步',
      content: (
        <div style={{
          marginTop: 16,
          height: '100%',
          display: 'flex',
          flexDirection: 'column'
        }}>
          <Tabs
            defaultActiveKey="1"
            items={[
              {
                key: '1',
                label: '确认信息',
                children: (
                  <div style={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    height: '33vh',
                    gap: 16
                  }}>
                    <div style={{
                      flex: 1,
                      overflowY: 'auto',
                      paddingRight: 8,
                      border: '1px solid #f0f0f0',
                    }}>
                      <Descriptions
                        bordered
                        column={2}
                        layout="vertical"
                      >
                        {/* <Descriptions.Item label="上传的图片">
                          {selectedFiles?.images ? (
                            selectedFiles.images.map(img => img.filename).join(', ')
                          ) : '无'}
                        </Descriptions.Item>
                        <Descriptions.Item label="飞行记录">
                          {selectedWayLine?.FlightLineName || '未选择'}
                        </Descriptions.Item> */}
                        {resource === 2 && selectedFiles?.images && (
                          <Descriptions.Item label="上传的图片">
                            {selectedFiles.images.map(img => img.filename).join(', ')}
                          </Descriptions.Item>
                        )}
                        {resource === 1 && selectedWayLine?.FlightLineName && (
                          <Descriptions.Item label="飞行记录">
                            {selectedWayLine.FlightLineName}
                          </Descriptions.Item>
                        )}
                        <Descriptions.Item label="任务名称">
                          {taskName == '' ? '---' : taskName}
                        </Descriptions.Item>
                        <Descriptions.Item label="AI模型">
                          {selectedModel?.AName || '未选择'}
                        </Descriptions.Item>
                        <Descriptions.Item label="识别内容">
                          {cls.length > 0
                            ? cls.map(num => modelClasses[num]).join(', ')
                            : '未选择'}
                        </Descriptions.Item>
                      </Descriptions>
                    </div>
                    <div style={{
                      display: 'flex',
                      justifyContent: 'flex-end',
                      gap: 8,
                      marginTop: 16
                    }}>
                      <Button
                        type="primary"
                        onClick={() => {
                          setCurrentStep(1);
                        }}
                      >
                        上一步
                      </Button>
                      <Button
                        type="primary"
                        onClick={() => handleSubmit()}
                      >
                        确认提交
                      </Button>
                    </div>
                  </div>
                ),
              },
            ]}
          />
        </div>
      ),
    }
  ];


  // 获取飞行列表
  const getAllList = async () => {
    try {
      setLoading2(true);
      const res = await axiosApi('/api/v1/Task/GetAllList', 'GET', null);
      setWayLineList(res)
      setWayLineListOptions(res.map(item => ({
        flightLineData: item, // 飞行记录数据
        label: item.FlightLineName,
        value: item.FlightLineId,
        FlightLineType: WayLineTypeToString(item.FlightLineType),
        airport: getDeviceName(item.DeviceSN),
        TaskBeginTime: item.TaskBeginTime,
        key: item.ID, // 这里一定要设置唯一key 否则会导致渲染丢失
      }))
      )
      setLoading2(false);
    }  
    catch (error) {
      console.log(error);
    }
  };

  const changeStep = value => {
    //点击步骤条的时候 重置所有状态
    setSelectedWayLine(null); // 选择的航线
    setFilesUploaded(false); // 是否上传了图片
    setSelectedFiles([]); // 已上传的图片
    setPendingFiles([]); // 等待上传的文件
    setSelectedModel(null); // 选择的模型
    setCls([]); // 识别内容
    setTaskName(''); // 任务名称

    setCurrentStep(value);
  };

  useEffect(() => {
    getDefaultData(); // 获取目标识别列表
    getAllList(); // 获取飞行记录列表
    getModelData(); // 获取模型列表
  }, [])

  const items = steps.map((item) => ({ key: item.title, title: item.title }));

  const exr = <div><Button type="primary" onClick={() => { console.log('新增'); openModal(); }}>新增</Button> </div>
  return (
    <div className='blackBackground'>
      <Card

        title={pageData ? pageData?.title : doNotShowLastButton ? '目标识别' : <LastPageButton title="目标识别" />}
        extra={exr}
        style={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          flex: 1,
          overflow: 'hidden',
          padding: 0,
        }}
      >
        <Table
          // 自定义分页
          pagination={{
            pageSize: 15,
            showSizeChanger: false,
            current: currentTablePage,
            total: dataSource.length,
            showTotal: (total) => (
              <span className="custom-pagination-text">
                共 {Math.ceil(total / 15)} 条 第 {currentTablePage}/{Math.ceil(total / 15)} 页
              </span>
            ),
            onChange: (page) => setCurrentTablePage(page),
            className: "custom-pagination-container"
          }}
          rowKey={(record) => record.ID}
          loading={loading}
          bordered
          dataSource={dataSource}
          columns={TableCols(handlePageChange, isDJPage, handlePageChange2)}
          size='small'
          scroll={{
            scrollToFirstRowOnChange: true,
            y: `calc(100vh - 310px)`,
            // y:600,  
          }}
        />
        <Modal
          title="新建任务"
          open={modalVisible}
          onCancel={() => {
            // 重置所有状态
            setSelectedWayLine(null);
            setFilesUploaded(false);
            setSelectedFiles([]);
            setPendingFiles([]);
            setSelectedModel(null);
            setCls([]);
            setTaskName('');
            setModalVisible(false)
            setCurrentStep(0)
            setActiveTabKey('1')
          }}
          footer={null}
          width="40vw"
          styles={{
            body: {
              height: '50vh',
              display: 'flex',
              flexDirection: 'column',
              padding: 24
            }
          }}
        >
          <Spin spinning={loading3} active>
            <Steps
              current={currentStep}
              items={items}
            // onChange={changeStep} // 允许点击步骤条切换步骤
            />
            <div>{steps[currentStep].content}</div>
          </Spin>
        </Modal>
      </Card>
    </div>

  );
};
export default TargetRecognition;