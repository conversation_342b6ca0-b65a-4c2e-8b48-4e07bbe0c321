import MyHead from "../components/MyHead"
import MyMenu from './Pages/MyMenu'
import commonStyle from "../style/common.less";
import { ConfigProvider } from "antd";
import locale from "antd/locale/zh_CN";
import { queryPage } from "@/utils/MyRoute";
import "@/pages/GT/style/antd-common.less";
import { useState,useEffect } from "react";
import { useModel } from "umi";
import SITheme from '@/pages/SI/style/theme';

//临时用地
function App() {
  const { page, setPage, lastPage, currentPage } = useModel("pageModel");
  const [collapsed, setCollapsed] = useState(false);
  // 判断是否显示菜单
  const isAnalysisPage = currentPage === 'AI分析' || currentPage === '影像管理' || currentPage === '异常事件';

  const handlePageChange = (page) => {
    setPage(queryPage(page));
  };

  const headList = [
    { label: "大屏展示", key: "大屏展示"},
    { label: "临时用地管理", key: "临时用地管理"},
    { label: "任务巡检", key: "任务巡检"},
    { label: "AI分析", key: "AI分析"},
  ];
  return (
    <div className="gt-page">
      <div className={commonStyle.gt_back_black} style={{ 
        position: "relative",
        overflow: "hidden",
        minHeight: "100vh",
        display: "flex",
        flexDirection: "column"
      }}>
        <MyHead
          headList={headList}
          handlePageChange={handlePageChange}
        />
        <div style={{ 
          display: "flex",
          flex: 1,
          position: "relative",
          overflow: "hidden"
        }}>
          {isAnalysisPage && (
            <MyMenu 
              setPage={handlePageChange} 
              setCollapsed={setCollapsed} 
              collapsed={collapsed}
            />
          )}
          <div 
            className=''
            style={{
              flex: 1,
              overflow: 'hidden',
              marginLeft: isAnalysisPage ? (collapsed ? 80 : 160) : 0,
              transition: isAnalysisPage ? 'margin-left 0.2s' : 'none'
            }}
          >
            <ConfigProvider locale={locale} theme={SITheme}>
              {page}
            </ConfigProvider>
          </div>
        </div>
      </div>
    </div>
  );
}



export default App;
