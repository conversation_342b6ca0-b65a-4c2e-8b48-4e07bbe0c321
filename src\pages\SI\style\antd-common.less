// // @back-color: rgba(#318071, 1);
// @back-color: rgba(#1abc9c);
// @back-color2: rgba(#1abc9c);
// @back-color-none: none;

// @font-color: #fff;

// @font-color-whiteBackground: black;
// // @font-color-active: #348cfd;
// @font-color-active: #b8e6d932;

// @font-color-active2: #5c8a7d; 

// @header-backColor:linear-gradient(to right, #06393f 200px, #1e2531 50%);

// //框线颜色
// // @line-color:#0d459b;
// @line-color: #42ab98;

.gt-page1{
    background-color: #091A1B;
    // background-size: cover;
    // background-position: center;
    // background-repeat: no-repeat;

//     .ant-card {
//         //card
//         background: @back-color-none;
//         color: @font-color;
//     }
    
//     .ant-card-bordered {
//         //card框线
//         border-color: @back-color;
//     }
    
//     .ant-card .ant-card-head {
//         // card标题颜色
//         color: @font-color-whiteBackground;
//         border-bottom-color: @line-color;
    
//     }
    
//     .ant-btn.ant-btn-icon-only {
//         // card标题箭头按钮颜色
//         color: @font-color;
//     }
    
//     //card标题文字
//     .ant-card .ant-card-meta-title {
//         color: @font-color;
//     }

    

//     //解禁文件页面 它单独配置的title 它的LastPageButton不在card里面 这里给它专门写一个样式
//     .unlock-title{
//         color: #ffffff !important;
//     }
    
//     // .ant-tag {
//     //     //tag小按钮
//     //     // background-color: @back-color;
//     //     color: @font-color-whiteBackground;
//     // }
    
//     // .ant-tag,
//     // .ant-tag a,
//     // .ant-tag a:hover {
//     //     //tag小按钮 a标签颜色
//     //     color: #ffffff;
//     // }
    
//     //dropdown输入框
//     .ant-select-outlined:not(.ant-select-customize-input) .ant-select-selector {
//         background: @back-color-none;
//         color: @font-color;
//         border-color: @line-color;
//     }
    
//     .ant-select-single .ant-select-selector {
//         //选择下拉框中的placeholder颜色
//         color: #ccc;
//     }
    
//     .ant-select .ant-select-selection-placeholder {
//         //选择下拉框中的placeholder颜色
//         color: @font-color;
//     }

//     .ant-picker .ant-picker-input input::placeholder{
//         //日期选择框中的placeholder颜色
//         color: #ffffff;
//     }
    
//     .ant-select .ant-select-arrow {
//         //选择下拉框中的倒三角图标
//         color: #ccc;
//     }
    
//     .ant-select-dropdown {
//         //dropdown下拉框显示的背景
//         background: @back-color;
//     }
    
//     //dropdown下拉框
//     .ant-select-dropdown {
//         background: @back-color;
//         color: @font-color-active;
//     }
    
//     //dropdown下拉框字体颜色
//     .ant-select-dropdown .ant-select-item-option-content {
//         color: @font-color;
//     }
    
//     .ant-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
//         //dropdown下拉框选中
//         background-color: @font-color-active;
//     }
    
//     .ant-select-focused.ant-select-outlined:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-selector {
//         //dropdown选择框鼠标经过激活
//         border-color: @line-color;
//     }
    
    
//     .ant-descriptions .ant-descriptions-item-content {
//         //描述列表descriptions
//         color: @font-color;
//     }
    
//     .ant-descriptions.ant-descriptions-bordered>.ant-descriptions-view .ant-descriptions-row>.ant-descriptions-item-label {
//         color: @font-color;
//     }
    
//     .ant-descriptions .ant-descriptions-item-label {
//         //描述列表descriptions 标签颜色
//         color: @font-color;
//     }
    
    
//     .ant-btn-variant-outlined {
//         //按钮1
//         background: @back-color;
//         color: @font-color;
//     }
//     .ant-btn-variant-outlined, .ant-btn-variant-dashed{
//         // background: @back-color2!important;
//         color: @font-color;
//         border:none;
//     }
//     .ant-btn-color-dangerous.ant-btn-variant-outlined:disabled, .ant-btn-color-dangerous.ant-btn-variant-dashed:disabled, .ant-btn-color-dangerous.ant-btn-variant-outlined.ant-btn-disabled, .ant-btn-color-dangerous.ant-btn-variant-dashed.ant-btn-disabled{
//         background: @back-color2;
//         color: @font-color;
//     }
    
//     .ant-btn-variant-outlined:not(:disabled):not(.ant-btn-disabled):hover,
//     .ant-btn-variant-dashed:not(:disabled):not(.ant-btn-disabled):hover {
//         color: @font-color;
//         border-color: @font-color;
//         background: @back-color2;
//     }
    
//     .ant-btn-variant-text {
//         //按钮2
//         color: @font-color;
//     }
    
//     .ant-btn-variant-solid {
//         background: @back-color2 !important;
//     }
    
//     .ant-menu-dark .ant-menu-item-selected,
//     .ant-menu-dark>.ant-menu .ant-menu-item-selected {
//         //menu
//         background-color: #415280;
//     }
    
//     .ant-radio-wrapper {
//         //圆形radio按钮
//         color: @font-color;
//     }
    
//     .ant-radio-wrapper .ant-radio-checked .ant-radio-inner {
//         //圆形radio按钮
//         background-color: @back-color;
//     }
    
    
//     .ant-btn-variant-outlined,
//     .ant-btn-variant-dashed {
//         //选择上传文件按钮 //按钮未激活
//         // background:@font-color;
//     }
    
//     .ant-checkbox .ant-checkbox-inner {
//         //方形radio按钮
//         background: #bbd6d4;
    
//     }
    
//     .ant-checkbox .ant-checkbox-inner:after {
//         //对号
//         border-color: @back-color2;
//     }
    
    
//     .ant-btn-variant-text:not(:disabled):not(.ant-btn-disabled):hover {
//         //按钮2鼠标悬停
//         background: @font-color-active;
//         color: #fff;
//     }
    
//     .ant-btn-variant-outlined:not(:disabled):not(.ant-btn-disabled):hover,
//     .ant-btn-variant-dashed:not(:disabled):not(.ant-btn-disabled):hover {
//         //按钮悬停激活
//         // background: @back-color-none;
//         background: @back-color2;
//     }
    
//     .ant-progress .ant-progress-text {
//         //进度条文字颜色
//         color: @font-color;
//     }
    
    
//     .ant-picker-outlined {
//         //日期选择输入框
//         background: @back-color2;
//         color: @font-color;
//     }

//   .ant-picker-dropdown {
//     .ant-picker-panel-container,
//     .ant-picker-panel-layout {
//         background: @back-color !important;
//     }
// }
  
//     .ant-picker-dropdown .ant-picker-cell-in-view {
//         //日期选择下拉框内容中的日期数字
//         color: @font-color !important;
//     }
    
//     .ant-picker-outlined:hover {
//         //日期选择输入框悬停
//         background: @back-color;
//     }
    
//      a {
//         //日期选择最下方文字
//         color: @font-color;
//     }
    
//     // .ant-list * {
//     //     color: white;
//     // }
    
//     .ant-modal .ant-modal-content {
//         //modal弹出窗
//         background: @back-color;
//     }
    
//     .ant-modal .ant-modal-content {
//         //modal弹出窗
//         background: @back-color;
//     }
    
//     .ant-descriptions .ant-descriptions-title {
//         //modal弹出窗标题
//         color: @font-color;
    
//     }
    
//     .ant-descriptions .ant-descriptions-item-label {
//         //modal弹出窗左侧详情文字
//         color: @font-color;
    
//     }
    
//     .ant-modal .ant-modal-header {
//         //modal弹出窗标题栏
//         background: @back-color;
//     }
    
//     .ant-modal .ant-modal-title {
//         //modal弹出窗标题栏文字
//         color: @font-color;
//     }
    
//     .ant-modal .ant-modal-close {
//         //modal弹出窗右上角关闭×
//         color: @font-color;
//     }
    
//     .ant-btn-variant-outlined,
//     .ant-btn-variant-dashed {
//         //modal弹出窗取消按钮
//         // background: #ccc;
//     }
    
//     .ant-btn-variant-solid {
//         //modal弹出窗下面保存按钮
//         background: #5478d4;
//     }
    
    
    
//     .ant-modal .ant-modal-content {
//         //modal删除弹出窗
//         background: @back-color;
//     }
    
//     .ant-modal-confirm .ant-modal-confirm-content {
//         //modal删除弹出窗内容
//         color: @font-color;
//     }
    
//     .ant-modal-confirm .ant-modal-confirm-title {
//         //modal删除弹出窗标题
//         color: @font-color;
//     }
    
    
    
//     .ant-table-wrapper .ant-table {
//         //table背景色
//         color: #fff;
//         background: @back-color-none;
//     }
    
//     // 白色背景专属样式
//     .whiteBackground{
//         .ant-table-wrapper .ant-table {
//             //table背景色
//             color: @font-color-whiteBackground;
//             background: @back-color-none;
//         };
//         .ant-table-wrapper .ant-table-tbody .ant-table-row>.ant-table-cell-row-hover {
//             //table里面的鼠标悬停的颜色
//             background: @font-color-active;
//         };
        
//         .ant-table-wrapper .ant-table-tbody .ant-table-row.ant-table-row-selected>.ant-table-cell {
//             //table里面的选中的颜色
//             background: @font-color-active;
//         };

//         .ant-card {
//             background: @back-color-none;
//             color: @font-color-whiteBackground;
//         };

//         .ant-card-bordered {
//             //card框线
//             border: 1px solid #f0f0f0;
//             // border-color: transparent;
//         }

//         .ant-radio-wrapper {
//             //圆形radio按钮
//             color: @font-color-whiteBackground;
//         }

//         .ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tbody > tr > td{
//             max-width: 150px;
//             height: 50px;
//             white-space: nowrap; // 禁止换行 多余内容用省略号显示
//             overflow: hidden;
//             text-overflow: ellipsis;
    
//             // 鼠标悬停的时候 允许换行显示
//             &:hover {
//                 overflow: visible;
//                 white-space: normal;
//             }
//         }
//         .ant-pagination .ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis,
//         .ant-pagination .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis {
//             //尾部导航省略号样式
//             color: @font-color-whiteBackground;
//         }
//         // 返回箭头样式
//         .ant-btn.ant-btn-icon-only{
//             color: @font-color-whiteBackground;
//         }
//     }

//     // 黑色背景专属样式
//     .blackBackground{

//         a{
//             color: @font-color-whiteBackground;
//         }

//         .ant-btn-variant-outlined{
//             // background: #d9d9d9;
//             // color: gray;
//             // border: solid 1px #d9d9d9
//         }
//         .ant-tabs{
//             //tabs标签组件
//             background: none;
//         }
//         .ant-tabs-top, .ant-tabs-bottom {
//             flex-direction: none;
//         }
//         .ant-tabs-top >.ant-tabs-nav{
//             margin:0;
//         }
//         .ant-tabs-content-holder{
//             display: block;
//         }
//         .ant-tabs-top >.ant-tabs-nav::before{
//             border-bottom: none;
//         }
//         // tabs组件标题颜色
//         .ant-tabs .ant-tabs-tab{
//             color: @font-color;
//         }
//         .ant-tabs .ant-tabs-tab:hover{
//             color: @line-color;
//         }
//         .ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn{
//             color: @line-color;
//         }

//         // 带删除按钮的 选中的tab组件样式
//         // .ant-tabs-tab.ant-tabs-tab-with-remove.ant-tabs-tab-active{
//         //     background: transparent;
//         //     border-color: @font-color;
//         // }

//         .ant-card .ant-card-head {
//             // card标题颜色
//             color: @font-color;
//             border-bottom: none;
        
//         }
//         .ant-table-wrapper .ant-table-thead > tr > th {
//             //table表头
//             background: rgba(11, 65, 69, 0.5);
//             color: @font-color;
//             border-bottom: #096C79 1px solid;
//             border-top: #096C79 1px solid;
//         }

//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container {
//             //table最上部与最左边的边框颜色
//             border-color: #096C79;
//         }
        
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>thead>tr>th,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>thead>tr>th,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>thead>tr>th,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-summary>table>thead>tr>th,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>thead>tr>td,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>thead>tr>td,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>thead>tr>td,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-summary>table>thead>tr>td,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tbody>tr>th,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tbody>tr>th,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tbody>tr>th,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-summary>table>tbody>tr>th,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tbody>tr>td,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tbody>tr>td,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tbody>tr>td,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-summary>table>tbody>tr>td,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tfoot>tr>th,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tfoot>tr>th,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tfoot>tr>th,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-summary>table>tfoot>tr>th,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tfoot>tr>td,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tfoot>tr>td,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tfoot>tr>td,
//         .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-summary>table>tfoot>tr>td {
//             border-color: #096C79;
//             //table里面的每个小框的颜色
//         }
        
//         .ant-table-wrapper .ant-table {
//             //table背景色
//             color: @font-color;
//             background: @back-color-none;
//         };
//         .ant-table-wrapper .ant-table-tbody .ant-table-row>.ant-table-cell-row-hover {
//             //table里面的鼠标悬停的颜色
//             background: rgba(11, 65, 69, 0.5);
//         };
        
//         .ant-table-wrapper .ant-table-tbody .ant-table-row.ant-table-row-selected>.ant-table-cell {
//             //table里面的选中的颜色
//             background: rgba(11, 65, 69, 0.5);
//         };

//         .ant-card {
//             background: @back-color-none;
//             color: @font-color;
//         };

//         .ant-card-bordered {
//             //card框线
//             border: none;
//             // border-color: transparent;
//         }

//         .ant-radio-wrapper {    
//             //圆形radio按钮
//             color: @font-color;
//         }

//         .ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tbody > tr > td{
//             max-width: 150px;
//             height: 50px;
//             white-space: nowrap; // 禁止换行 多余内容用省略号显示
//             overflow: hidden;
//             text-overflow: ellipsis;
    
//             // 鼠标悬停的时候 允许换行显示
//             &:hover {
//                 overflow: visible;
//                 white-space: normal;
//             }
//         }
//         // 尾部导航选择页面按钮样式
//         .ant-pagination {
//             .ant-pagination-item {
//                 // margin: 0 1px 0 1px;
//                 background: none !important;
//                 border: 0.5px solid @line-color !important;
//                 transition: all 0.3s ease;
//                 a {
//                     color: #fff !important;
//                     font-weight: lighter;
//                 }
                
//                 &:hover {
//                     transform: scale(1.2);
//                     box-shadow: 0 2px 4px rgba(0,0,0,0.2);
//                 }
//             }
        
//             .ant-pagination-item-active {
//                 background: @back-color2 !important;
//                 border: 1.5px solid @line-color !important;
                
//                 a {
//                     color: #fff !important;
//                     font-weight: bold;
//                 }
//             }
//         }
        
//         .ant-pagination .ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis,
//         .ant-pagination .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis {
//             //尾部导航省略号样式
//             color: @font-color;
//         }

//         // 尾部导航箭头样式
//         .ant-pagination-prev,
//         .ant-pagination-next {
//             .ant-pagination-item-link {
//             background: none !important;
//             border: 0.5px solid @line-color !important;
//             color: @font-color;
//             }

//             &:hover {
//                 .ant-pagination-item-link {
//                     transform: scale(1.2);
//                     box-shadow: 0 2px 6px rgba(0,0,0,0.3);
//                 }
//             }
//         }

//         // 尾部分页数字输入框样式
//         .ant-pagination .ant-pagination-options-quick-jumper input{
//             background-color: transparent;
//         }

//         // 列表样式
//         .ant-list * {
//             // color: white;
//         }
//     }

//     // 表单的label样式
//     .ant-form-item-label {
//         width: 120px;
//         text-align: right;
//     }


    
    
    
    
//     .ant-table-wrapper .ant-table-thead>tr>th,
//     .ant-table-wrapper .ant-table-thead>tr>td {
//         //table表头
//         background: @line-color;
//         color: @font-color;
//         border-bottom: #318071;
//     }
    
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container {
//         //table最上部与最左边的边框颜色
//         border-color: @line-color;
//     }
    
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>thead>tr>th,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>thead>tr>th,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>thead>tr>th,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-summary>table>thead>tr>th,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>thead>tr>td,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>thead>tr>td,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>thead>tr>td,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-summary>table>thead>tr>td,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tbody>tr>th,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tbody>tr>th,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tbody>tr>th,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-summary>table>tbody>tr>th,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tbody>tr>td,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tbody>tr>td,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tbody>tr>td,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-summary>table>tbody>tr>td,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tfoot>tr>th,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tfoot>tr>th,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tfoot>tr>th,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-summary>table>tfoot>tr>th,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-content>table>tfoot>tr>td,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-header>table>tfoot>tr>td,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-body>table>tfoot>tr>td,
//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container>.ant-table-summary>table>tfoot>tr>td {
//         border-color: @line-color;
//         //table里面的每个小框的颜色
//     }

//     .ant-table-wrapper .ant-table.ant-table-bordered>.ant-table-container {
//         //滚动条上部的border
//         border-top:none;
//     }
    
//     .ant-table-wrapper .ant-table-tbody .ant-table-row>.ant-table-cell-row-hover {
//         //table里面的鼠标悬停的颜色
//         background: @font-color-active;
//     }
    
//     .ant-table-wrapper .ant-table-tbody .ant-table-row.ant-table-row-selected>.ant-table-cell {
//         //table里面的选中的颜色
//         background: @font-color-active;
//     }


//     .ant-table-wrapper .ant-table.ant-table-bordered >.ant-table-container >.ant-table-body >table >tbody>tr>td{
//         height: 50px;
//         white-space: nowrap; // 禁止换行 多余内容用省略号显示
//         overflow: hidden;
//         text-overflow: ellipsis;

//         // 鼠标悬停的时候 允许换行显示
//         &:hover {
//             overflow: visible;
//             white-space: normal;
//         }
//     }

//     // 隐藏滚动条
//     .ant-table-body {
//         // Chrome/Safari
//         &::-webkit-scrollbar {
//           display: none;
//         }
//         // Firefox
//         scrollbar-width: none;
//         // IE/Edge
//         -ms-overflow-style: none;
//     }
//     .ant-table-cell.ant-table-cell-scrollbar {
//         display: none;
//       }
//       .ant-table-wrapper .ant-table-tbody>tr.ant-table-placeholder{
//         //表格的空白内容
//         background: @back-color-none;
//       }

//     // 尾部导航选择页面按钮样式
//     .ant-pagination {
//         .ant-pagination-item {
//             // margin: 0 1px 0 1px;
//             background:none;
//             // color:@font-color;
//             transition: all 0.3s ease;
            
//             &:hover {
//                 transform: scale(1.2);
//                 box-shadow: 0 2px 4px rgba(0,0,0,0.2);
//             }
//             a {
//                 color: @font-color;
//             }
//         }
    
//         .ant-pagination-item-active {
//             background: @back-color2 !important;
//             border: 1.5px solid @font-color !important;
            
//             a {
//                 color: #fff !important;
//                 font-weight: bold;
//             }
//         }
//     }
    
//     .ant-pagination .ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis,
//     .ant-pagination .ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis {
//         //尾部导航省略号样式
//         color: @font-color;
//     }
//     .ant-pagination.ant-pagination-mini .ant-pagination-options-quick-jumper{
//         //尾部导航快速跳转输入框样式
//         // border:1px solid @line-color;
//         color: @font-color;
//     }
//     .ant-pagination.ant-pagination-mini .ant-pagination-options-quick-jumper input{
//         //尾部导航快速跳转输入框样式
//         border-color:@line-color;
//         background: none;
//         color: @font-color;
//     }

//     // 尾部导航箭头样式
//     .ant-pagination-prev,
//     .ant-pagination-next {
//         .ant-pagination-item-link {
//             border: 0.5px solid @line-color !important;
//             color: @font-color;
//         }

//         &:hover {
//         .ant-pagination-item-link {
//             transform: scale(1.2);
//             box-shadow: 0 2px 6px rgba(0,0,0,0.3);
//            }
//         }
//     }
    
    
    
//     .ant-input-affix-wrapper {
//         // input输入框placehloder
//         color: #ccc;
//     }
    
//     .ant-input-affix-wrapper>input.ant-input,
//     .ant-input-affix-wrapper>textarea.ant-input {
//         // input输入框placehloder
//         color: #ccc;
//     }
    
//     .ant-input-outlined:hover {
//         // input输入框鼠标经过激活状态
//         border-color: @line-color ;
//     }
    
//     .ant-input-outlined:focus,
//     .ant-input-outlined:focus-within {
//         // input输入框点击激活状态
//         border-color: @line-color ;
//     }
//     .ant-input-outlined:focus, .ant-input-outlined:focus-within{
//         color: @font-color;
//     }
//     .ant-input-number-outlined {
//         // input输入框数字
//         color: @font-color;
//         background: #0000006b;
//     }
    
//     .ant-input-number .ant-input-number-input {
//         color: #ccc;
//     }
    
//     .ant-input-number-outlined:focus,
//     .ant-input-number-outlined:focus-within {
//         // input输入框数字点击激活
//         border-color: #ffffffcc;
//         box-shadow: 0 0 0 2px rgba(5, 97, 213, 0.12);
//         outline: 0;
//         background-color: #0000006b;
//     }
    
    
//     .ant-select-dropdown {
//         //选择下拉框背景
//         background: @back-color;
//         border-color: @line-color;
    
//     }
    
//     .ant-select-outlined:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer):hover .ant-select-selector {
//         //选择下拉框边框
//         border-color: @line-color;
//     }
    
    
//     .ant-badge.ant-badge-status .ant-badge-status-text {
//         //徽标badge
//         color: @font-color;
//     }
    
//     .ant-switch.ant-switch-checked {
//         //switch开关
//         background: @font-color-active;
//     }
    
//     .ant-timeline {
//         //时间轴Timeline
//         color: @font-color;
//     }
    
//     .ant-timeline .ant-timeline-item-head-blue {
//         //处理过程>ai识别radio
//         border-color: @back-color;
//     }
    
    
//     .ant-list .ant-list-item {
//         // List
//         color: @font-color;
//     }
    
//     .notipage___JpLVT {
//         //公告发布页背景色
//         background: @back-color;
//         color: @font-color;
//     }
    
//     .ant-form-item .ant-form-item-label>label {
//         //表单标题颜色
//         color: @font-color;
//     }
    
//     .page___wVfaX::after {
//         content: "";
//         position: absolute;
//         width: 100%;
//         height: 100%;
//         // background: url(../assets/17bg.jpg) no-repeat;
//         background-size: 100% auto;
//         z-index: -1;
//         opacity: 0.5;
//     }
    
//     .ant-tree {
//         //树组件
//         color: @font-color;
//         background: @back-color-none ;
//     }
    
    
    
    
    
//     //登录
//     .ant-input-outlined {
//         background: transparent;
//         border-color: @line-color;
//     }
    
//     .ant-btn-variant-solid {
//         //登录按钮
//         background: #1ad5b0;
//     }
    
//     .ant-btn-variant-solid:not(:disabled):not(.ant-btn-disabled):hover {
//         //登录按钮激活
//         background: @back-color;
//     }
    
//     .ant-input-affix-wrapper>input.ant-input,
//     .ant-input-affix-wrapper>textarea.ant-input {
//         //输入框字体
//         color: #fff;
//     }
    
//     .ant-input-outlined:hover {
//         //输入框鼠标激活
//         background: @back-color-none;
//     }
    
//     .ant-input-outlined:focus,
//     .ant-input-outlined:focus-within {
//         //输入框内容激活
//         background: @back-color-none;
//     }
    
//     .ant-input-affix-wrapper .anticon.ant-input-password-icon {
//         //眼睛
//         color: @font-color;
//     }
//     .ant-tabs{
//         //tabs标签组件
//         background: @header-backColor;
//     }
//     .ant-tabs .ant-tabs-tab-btn{
//         color: @font-color;
//     }
//     .ant-tabs-card >.ant-tabs-nav .ant-tabs-tab, .ant-tabs-card >div>.ant-tabs-nav .ant-tabs-tab{
//         border-color: @line-color;
//     }
//     .ant-tabs-top, .ant-tabs-bottom {
//         flex-direction: none;
//     }
//     .ant-tabs-top >.ant-tabs-nav{
//         margin:0;
//     }
//     .ant-tabs-content-holder{
//         display: none;
//     }
//     .ant-tabs-top >.ant-tabs-nav::before{
//         border-bottom: none;
//     }
//     .ant-tabs .ant-tabs-tab .ant-tabs-tab-remove .anticon{
//         //tabs标签删除按钮
//         color:rgb(121 77 29);
//     }
// }

//   // 自定义分页样式
//   .custom-pagination-container {
//     .ant-pagination-total-text {
//       position: absolute;
//       left: 0;
//       bottom: 0;
//       color: @font-color;
//     }
    
//     .ant-pagination-options {
//       position: relative;
//       float: right;
//     }
//   }
}