import {Outlet,useLocation } from 'umi';
import styles from './index.less';
import { ConfigProvider } from 'antd';
import { MainColor } from '@/utils/colorHelper';
import { useEffect } from 'react';
import useConfigStore from "@/stores/configStore";

export default function NewLayout() {
  const initConfig = useConfigStore((state) => state.initConfig)
  const location = useLocation();

  useEffect(() => {
    initConfig()
  }, []);

  if (location.pathname === '/login') {
    return <Outlet />
  }

  return (
    <ConfigProvider
    theme={{
      token: {
        colorPrimary: MainColor,
        borderRadius: 2,
      },
    }}
  >
    <div className={styles.navs}>
      <Outlet /> 
  </div>

  </ConfigProvider>
  );
}
